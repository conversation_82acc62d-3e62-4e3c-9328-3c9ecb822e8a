# 🔍 DICOM图像PESI路径查询指南

> **收件人**: <PERSON><PERSON><PERSON>
> **主题**: 使用Coreload工具查询DICOM图像物理路径

---

## 📋 目录

1. [概述](#概述)
2. [工具介绍](#工具介绍)
3. [查询示例](#查询示例)
4. [路径查找流程](#路径查找流程)
5. [实际应用示例](#实际应用示例)
6. [推荐方案](#推荐方案)
7. [原始邮件内容](#原始邮件内容)

---

## 📖 概述

针对您的使用场景，可以使用 **Coreload** 提供的 `executequery.sh` 工具连接图像数据库并查询所需属性。该脚本是 Coreload 的发布工件，可供各种模态使用。

您可以编写 **shell脚本** 或 **Python脚本** 来查找特定患者姓名、序列描述或其他属性对应图像的 **PESI路径**（DICOM图像的物理系统路径）。

---

## 🛠️ 工具介绍

### executequery.sh
- **来源**: Coreload发布工件
- **功能**: 连接图像数据库并执行SQL查询
- **适用**: 所有模态设备

---

## 💡 查询示例

以下是使用患者姓名和序列描述进行查询的示例：

### 查询命令

```bash
{ctuser@oc79}executequery.sh -c "select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id from v_image img,imageset series, exam study where study.exam_id=series.exam_id and series.image_set_id=img.image_set_id and study.patient_name_unicode='RT_exam_MR' and series.series_description='FIESTA MULTICOUPES';"
```

### 查询结果

```
warning: iv not used by this cipher
warning: iv not used by this cipher

| patient_id | exam_id | image_set_id | image_id | dcm_image_id |
|------------|---------|--------------|----------|--------------|
|         12 |      13 |           47 |     2934 |            2 |
|         12 |      13 |           47 |     2935 |            3 |
|         12 |      13 |           47 |     2936 |            4 |
|         12 |      13 |           47 |     2937 |            5 |
|         12 |      13 |           47 |     2938 |            6 |
|         12 |      14 |           48 |     2939 |            2 |
|         12 |      12 |           44 |     2919 |            2 |
|         12 |      12 |           44 |     2920 |            3 |
|         12 |      12 |           44 |     2921 |            4 |
|         12 |      12 |           44 |     2922 |            5 |
|         12 |      12 |           44 |     2923 |            6 |

(11 rows)
```

> **说明**: 上述查询将获取匹配的患者属性，您可以使用这些属性来查找PESI路径。

---

## 🔄 路径查找流程

### 步骤1: 遍历查询结果
在脚本中遍历每一行结果，然后准备并执行以下查询：

### 步骤2: 构建查找命令
使用以下两种格式之一：

```bash
# 格式1: 包含dcm_image_id
find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*.<dcm_image_id>

# 格式2: 不包含dcm_image_id
find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*
```

### 步骤3: 获取PESI路径
执行查找命令后，您将获得相应的PESI路径。

---

## 🎯 实际应用示例

### 示例场景
使用上述查询结果的第一行数据：

**输入参数**:
- `patient_id`: 12
- `exam_id`: 13
- `image_set_id`: 47
- `image_id`: 2934

### 执行命令

```bash
{ctuser@oc79}find $SDCIMAGEPOOL/images/p12/e13/s47/i2934.*
```

### 输出结果

```bash
/usr/g/sdc_image_pool/images/p12/e13/s47/i2934.MRDC.2
```

✅ **成功获得特定结果行的PESI路径！**

> **重要**: 同样地，遍历每个结果行，执行上述查询，即可获得所有对应的PESI路径。

---

## 🚀 推荐方案

我建议采用以下 **两步法**：

### 方案一: 数据库查询

```bash
executequery.sh -c "select img.patient_id,img.exam_id,img.image_set_id,img.image_id from v_image img,imageset series, exam study where study.exam_id=series.exam_id and series.image_set_id=img.image_set_id and study.patient_name_unicode='RT_exam_MR' and series.series_description='FIESTA MULTICOUPES';"
```

### 方案二: 路径查找循环

- **遍历每一行结果**
- **准备查找查询并执行以获取PESI路径**

```bash
find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*
```

---

## 📧 原始邮件内容

> **注**: 以下是原始技术邮件的完整内容，保留原始格式以供参考。

```
Hi Minyue.

For the below use case, you can use the Coreload provided utility executequery.sh to connect with image database and query the required attributes.
This script is a released artifact from Coreload and can be used by modalities.

You can write a shell script or python script to find the PESI path(physical system path of DICOM image) of image for the specific patient name or series description or other attributes.

Below I'm showing an example query using patient name and series description.

{ctuser@oc79}executequery.sh -c "select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id from v_image img,imageset series, exam study where study.exam_id=series.exam_id and series.image_set_id=img.image_set_id and study.patient_name_unicode='RT_exam_MR' and series.series_description='FIESTA MULTICOUPES';"
warning: iv not used by this cipher
warning: iv not used by this cipher
patient_id | exam_id | image_set_id | image_id | dcm_image_id
------------+---------+--------------+----------+--------------
         12 |      13 |           47 |     2934 | 2
         12 |      13 |           47 |     2935 | 3
         12 |      13 |           47 |     2936 | 4
         12 |      13 |           47 |     2937 | 5
         12 |      13 |           47 |     2938 | 6
         12 |      14 |           48 |     2939 | 2
         12 |      12 |           44 |     2919 | 2
         12 |      12 |           44 |     2920 | 3
         12 |      12 |           44 |     2921 | 4
         12 |      12 |           44 |     2922 | 5
         12 |      12 |           44 |     2923 | 6
(11 rows)

The above query will fetch the matching patient attributes, using which you can find the pesi path.

Iterate through the result of each row in the script and then prepare and execute below query.

find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*.<dcm_image_id>
or
find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*

You can get the respective result PESI Path.
For e.g. In above example result If we use the first row, the query will be like
{ctuser@oc79}find $SDCIMAGEPOOL/images/p12/e13/s47/i2934.*
/usr/g/sdc_image_pool/images/p12/e13/s47/i2934.MRDC.2

You got the PESI path for the specific result row. Similarly iterate through each result row, execute the above query, and get the PESI path.
I hope it helps.

I suggest going with below 2 options.
•	executequery.sh -c "select img.patient_id,img.exam_id,img.image_set_id,img.image_id from v_image img,imageset series, exam study where study.exam_id=series.exam_id and series.image_set_id=img.image_set_id and study.patient_name_unicode='RT_exam_MR' and series.series_description='FIESTA MULTICOUPES';"

•	iterate each row, prepare find query and execute to get PESI path.
o	find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*
```

---

**希望这些信息对您有所帮助！** 🎉
