# DICOM图像质量分析工具

这是一个用于分析DICOM图像质量的工具，可以对DICOM图像进行自动化的质量评估。

## 功能特点

- 支持DICOM图像格式
- 自动分析图像质量指标
- 生成分析报告
- 图形用户界面
- 可配置的分析参数

## 系统要求

- Java 8或更高版本
- Maven 3.6或更高版本

## 安装说明

1. 克隆项目代码：
```bash
git clone https://github.com/your-username/quality-assurance-tool.git
```

2. 进入项目目录：
```bash
cd quality-assurance-tool
```

3. 编译项目：
```bash
mvn clean package
```

4. 运行程序：
```bash
java -jar target/quality-assurance-tool-1.0-SNAPSHOT-jar-with-dependencies.jar
```

## 使用说明

1. 启动程序后，选择配置文件
2. 点击"开始分析"按钮开始分析
3. 等待分析完成，查看分析报告
4. 可以随时点击"停止分析"按钮停止分析

## 配置文件说明

配置文件包含以下主要参数：

- `dicom.image.path`: DICOM图像文件路径
- `analysis.thread.count`: 分析线程数
- `max.image.size`: 最大图像尺寸
- `report.template.path`: 报告模板路径
- `report.output.path`: 报告输出路径

## 分析指标说明

工具会分析以下图像质量指标：

- 平均灰度值
- 标准差
- 对比度
- 信噪比
- 噪声水平

## 报告格式说明

分析报告包含以下内容：

- 基本信息（患者信息、检查信息等）
- 序列分析结果
- 图像分析结果
- 分析摘要

## 开发说明

### 项目结构

```
quality-assurance-tool/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── ge/
│   │   │           └── med/
│   │   │               └── hcct/
│   │   │                   ├── analysis/
│   │   │                   ├── core/
│   │   │                   ├── dicom/
│   │   │                   ├── report/
│   │   │                   └── ui/
│   │   └── resources/
│   └── test/
├── pom.xml
└── README.md
```

### 主要类说明

- `MainApplication`: 主应用程序入口
- `MainView`: 主界面视图
- `MainController`: 主界面控制器
- `AnalysisEngine`: 分析引擎
- `ImageProcessor`: 图像处理器
- `ImageValidator`: 图像验证器
- `ReportGenerator`: 报告生成器

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。
