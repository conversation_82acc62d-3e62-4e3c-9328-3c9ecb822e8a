#Determines whether doseinformation is needed in dosesc or not.
#If the value of this tag is 1, dose info should be written in to dosesc.
writeDoseInSC = 1

#Determines dose text file needs to be created or not.
#If the value of the tag is 1, dose text file should be created.
writeDoseText = 1

#Determines the number of dose text files to be kept.
#If the number of files exceeds this value, the extra files should be deleted.
numDoseText = 100

#examDescLen determine whether the exam description stored in DICOM data element "Study
#Description" (0008, 1030) is 22 or 64 characters long.
#22 --> (Legacy, default)      22 character exam description
#64 --> (DICOM)		       64 character exam description
examDescLen = 64

#CT Image
#patIDLen determines whether the Patient ID stored in DICOM data element
#"Patient ID" (0010,0020) is 16 or 64 characters long.
#16 --> (Legacy, default)	16 character patient ID
#64 --> (DICOM)			64 character patient ID
patIDLen = 64

#CT Image
#patNameLen determines whether the patient name stored in the DICOM
#data element "Patient Name" (0010,0010) is 32 or 64 characters long.
#32 --> (<PERSON>, default)	32 character patient name
#64 --> (DICOM)			64 character patient name
patName<PERSON>en = 32

#CT Image
#refPhysNameLen determines whether the referring physician name stored in the DICOM
#data element "ReferringPhysicianName" (0008,0090) is 32 or 64 characters long.
#32 --> (Legacy, default)	32 character referring physician name
#64 --> (DICOM)			64 character referring physician name
refPhysNameLen = 32

#CT Image
#fillAccNum determines whether or not DICOM data element "Accession
#Number" (0008,0050) that lies OUTSIDE of sequence "Request Attributes
#Sequence" (0040,0275) gets filled in for GROUPED PROCEDURES with
#different accession numbers.
#0 --> (DICOM/IHE)              Do not fill in the (0008,0050) outside of sequence
#1 --> (LEGACY, default)        Fill in (0008,0050) outside of sequence
fillAccNum = 1

#CT Image
#acqTimeFracSec determines whether or not fractional seconds
#are stored in the DICOM data element "Acquisition Time" (0008,0032).
#0 --> No       (Legacy)         Acquisition Time is displayed in whole seconds
#1 --> Yes      (DICOM, default) Acquisition Time is displayed in fractional seconds
acqTimeFracSec = 1

#CT Image
#sliceLocAsCenter determines whether DICOM data element "Slice
#Location" (0020,1041) is stored as the image center.
#0 --> No   (Legacy, default)    Table iso-location is recorded for "SliceLocation" (0020,1041)
#1 --> Yes  (DICOM)              Image center location is recorded for "SliceLocation" (0020,1041)
sliceLocAsCenter = 0

#CT Image
#fillBodyPartExamined determines whether DICOM data element "Body Part Examined" (0018,0015)
#is set in the non-GSI CT Image.
#0 --> No  (default)     Do not fill in the (0018,0015) for non-GSI CT Image.
#1 --> Yes               Fill in the (0018,0015) for non-GSI CT Image.
fillBodyPartExamined = 1

#Transaction (MPPS)
#sendProcCodeSeq determines whether DICOM data element "Procedure
#Code Sequence" (0008,1032) is sent in the MPPS N-CREATE message.
#If the value is set to 0, created image doesn't include the Code Sequence (0032, 1064).
#0 --> (Legacy)         "ProcedureCodeSequence" is omitted from the MPPS N-CREATE message
#1 --> (DICOM, default) "ProcedureCodeSequence" is included in the MPPS N-CREATE message
sendProcCodeSeq = 1

#Transaction (MPPS)
#fillInPerfAISeq determines whether or not DICOM data element
#"ProcedureProtocolCodeSequence" (0040,0260) is sent in the N-SET message.
#If the value is set to 0, created image doesn't include the Code Sequence (0040, 0008).
#0 --> (Legacy)         "ProcedureProtocolCodeSequence" is omitted from the MPPS N-SET message
#1 --> (DICOM, default) "ProcedureProtocolCodeSequence" is included in the MPPS N-SET message
fillInPerfAISeq = 1

#CJKPatientData
#cjkPatientAcceptance determines whether or not to accept Japanese Patient Name.
#if it is set, specific character set is checked and if it contains Japanese,
#patient name will be split into alphabetical name, ideographic name and phonetic name.
#If following CJKEncoding is not set, only alphabetical name is stored to Patient Name (0010,0010).
#This is intended to display ideographic name and phonetic name on Xtream Display (Gantry LCD).
#Currently only ISO IR 2022 87 is supported as Japanese patient name character set.
#0 --> No  (Legacy)             patient name is NOT parsed to split components
#1 --> Yes (DICOM, default)     patient name is parsed and split into 3 components.
cjkPatientAcceptance = 1

#JISCharacterFilter
#jisCharacterFilter determines whether or not to check JIS Character strictly.
#if it is set, all character codes which are not a part of JISX0208 are filtered and replaced with "?"
#This is intended to avoid unexpected character conversion from JIS to EUC/UTF since such characters are
#Machine dependent and may be converted to wrong characters.
#There is some RIS system which violates DICOM standard and send machine dependent codes to CT.
#0 --> No  (Legacy)             JIS character codes are not filtered. Accept machine dependent code as is.
#1 --> Yes (DICOM, default)     JIS character codes are filtered to comply on JISX0208. Machine dependent code will be shown as "?"
jisCharacterFilter = 1

#CJKEncoding 
#cjkEncoding determines whether or not to store CJK patient name and specific character sets to Image.
#Do not turn it 1 until all browser and viewer support CJK patinet name display.
#0 --> No  (Legacy, default)             "Specific Character Set" is set to ISO_IR 100
#1 --> Yes (Future Nuevo base Product)   "Specific Character Set" is set to value from RIS
cjkEncoding = 0

#MWLCharset
#MWLCharset is used for MWL. this character set is filled in 0008,0005 Specific Character Set for MWL Query
#\ISO 2022 IR 87 should be set for Japanese Patient 
#ISO_IR 100 should be set for other patient
MWLCharset = "ISO_IR 100"

#Dose SR 2009 Compliance
#complianceLevel of 2009 will include the new tags, complianceLevel absent or set to another value will ignore the new tags
complianceLevel=2009

#windowCenterInSC
#windowCenterInSc is preferred WindowCenter value in Secondary Capture Image (DoseReportSC and InjectorReportSC).
#it needs to set to -512 if SC image is sent to PACS which interporate pixel value.
#for coreload system, it needs to be set to -512 as default
windowCenterInSC = -512

#windowWidthInSC
#windowWidthInSc is preferred WindowWidth value in Secondary Capture Image (DoseReportSC and InjectorReportSC).
#it needs to set to 1024 if SC image is sent to PACS which interporate pixel value.
#for coreload system, it needs to be set to 1024 as default
windowWidthInSC = 1024

#RadiologistMapping 
#RadiologistMapping determines whether to use the (0008,1060) tag value obtained from the work list server to assign the value of "radiologist".
#0 --> No (do not get) assigns the value "" to "radiologist".
#1 --> Yes (get) assign value to "radiologist" with the obtained (0008, 1060) tag value.
RadiologistMapping = 1
