# System error messages
error.system.unexpected=An unexpected system error has occurred
error.system.config=System configuration error: {0}
error.system.io=IO operation error: {0}
error.system.database=Database operation error: {0}

# Retry related messages
system.retry.attempt=Retrying operation (attempt {0}/{1})
system.retry.failed=Retry failed (attempt {0}): {1}
system.retry.exhausted=Retry attempts exhausted ({0} attempts): {1}
system.retry.interrupted=Retry operation was interrupted

# Business error messages
error.business.validation=Data validation failed: {0}
error.business.processing=Business processing failed: {0}
error.business.not.found=Requested data not found: {0}
error.business.not.allowed=Operation not allowed: {0}

# UI error messages
error.ui.operation=UI operation error: {0}
error.ui.display=UI display error: {0}

# DICOM validation messages
dicom.error.missing.required.tag=Missing required tag {0} (0x{1})
dicom.error.invalid.tag=Invalid tag {0}: {1}
dicom.error.empty.pixel.data=Pixel data is empty
dicom.error.invalid.dimension=Invalid image dimension
dicom.error.invalid.bits.allocated=Invalid bits allocated
dicom.error.invalid.samples=Invalid number of samples
dicom.error.size.limit.exceeded=Size limit exceeded
dicom.error.size.mismatch=Size mismatch
dicom.error.invalid.image.params=Invalid image parameters: rows={0}, columns={1}, allocated bits={2}, stored bits={3}, high bit={4}
dicom.error.empty.patient.id=Patient ID is empty
dicom.error.empty.patient.name=Patient name is empty
dicom.error.empty.series.id=Series ID is empty
dicom.error.empty.exam.id=Exam ID is empty
dicom.error.empty.file.id=DICOM file ID is empty
dicom.quick.validation.failed=Quick validation failed

# DICOM file operation messages
dicom.file.not.found=DICOM file does not exist: {0}
dicom.file.read.failed=Failed to read DICOM file: {0}
dicom.error.invalid.path=Invalid file path
dicom.provider.directory.invalid=Invalid directory: {0}
dicom.provider.scan.start=Started scanning directory: {0} {1}
dicom.provider.scan.complete=Scan completed, found {0} DICOM files
dicom.provider.scan.failed=Failed to scan directory: {0}
dicom.provider.scan.directory=Scanning directory: {0}
dicom.provider.scan.no.files=No DICOM files found in directory: {0}
dicom.provider.scan.found.files=Found {0} DICOM files
dicom.provider.processing.file=Processing DICOM file: {0} ({1}/{2})
dicom.provider.processing.file.error=Failed to process file: {0}
dicom.provider.processing.start=Starting to process new files
dicom.provider.processing.summary=DICOM processing completed: success={0}, failed={1}

# DICOM basic error messages
dicom.error.invalid.data=Invalid DICOM data: {0}
dicom.error.file.not.found=DICOM file not found: {0}
dicom.error.processing=DICOM processing error: {0}
dicom.error.image.processing=DICOM image processing error: {0}
dicom.error.series.processing=DICOM series processing error: {0}
dicom.error.exam.processing=DICOM exam processing error: {0}
dicom.error.tag=DICOM tag error: {0}
dicom.error.validation=DICOM validation error: {0}

# DICOM async operation messages
dicom.scan.async.failed=Async scan failed: {0}
dicom.read.async.failed=Async read failed: {0}
dicom.count.failed=Count operation failed
dicom.check.failed=Check operation failed
dicom.monitor.close.failed=Failed to close monitor: {0}

# DICOM provider basic messages
dicom.provider.model.null=DICOM file model cannot be null
dicom.provider.model.id.invalid=DICOM file model must have a valid ID
dicom.provider.file.not.found=DICOM file path is invalid or file does not exist: {0}
dicom.provider.model.exists=DICOM file model with ID {0} already exists and will be updated
dicom.provider.no.images=Series {0} has no available images
dicom.provider.no.series=Exam {0} has no available series
dicom.provider.data.cleared=All data has been cleared
dicom.provider.shutdown=DicomDataProvider has been shut down
dicom.provider.create.exam.failed=Failed to create exam object: {0}
dicom.provider.create.series.failed=Failed to create series object: {0}
dicom.provider.update.exam.failed=Failed to update exam attributes: {0}
dicom.provider.update.image.failed=Failed to update image attributes: {0}
dicom.provider.update.series.failed=Failed to update series attributes: {0}
dicom.provider.clear.failed=Failed to clear DICOM data: {0}
dicom.provider.shutdown.failed=Failed to shutdown DicomDataProvider: {0}
dicom.provider.validation.failed=DICOM file validation failed: {0}
dicom.provider.tags.empty=DICOM tag data is empty
dicom.provider.study.uid.empty=Study instance UID is empty
dicom.provider.series.uid.empty=Series instance UID is empty
dicom.provider.init=System initialized, root directory: {0}
dicom.provider.listener.error=Listener error: {0}
dicom.provider.monitor.start=Started monitoring directory: {0}
dicom.provider.monitor.stopped=File monitoring has been stopped
dicom.provider.monitor.failed=File monitoring failed: {0}
dicom.provider.file.changed=Detected DICOM file change: {0}
dicom.provider.first.exam=Detected first exam, selecting automatically: {0}
dicom.provider.first.series=Automatically selecting first series: {0}
dicom.provider.images.loaded=Successfully loaded series images, count: {0}
dicom.provider.exam.attributes.updated=Successfully updated exam attributes: patientId={0}, patientName={1}
dicom.provider.validate.first.exam.failed=Validation of first exam failed

# Configuration Messages
config.file.not.found=Configuration file not found: {0}
config.file.loaded=Successfully loaded configuration file
config.key.not.found=Configuration key not found: {0}
config.invalid.number=Invalid numeric configuration value: {0} = {1}
config.invalid.log.level=Invalid log level: {0}

# System Messages
dicom.provider.system.init=System initialized, root directory: {0}
dicom.provider.system.shutdown=System has been shut down

# Tag parsing messages
dicom.tag.create.failed=Failed to create tag: {0}
dicom.tag.parse.failed=Failed to parse DICOM tags: {0}
dicom.tag.empty.model=DicomFileModel is empty
dicom.tag.empty.attributes=DICOM attributes are empty
dicom.tag.empty.id=Tag ID cannot be empty
dicom.model.empty.id=ID cannot be empty
dicom.image.empty.id=Image ID cannot be empty
dicom.image.create.failed=Failed to create DicomImage: {0}

# Service related messages
dicom.service.init.failed=DICOM service initialization failed
dicom.service.init.complete=DICOM service initialized, scan directory: {0}
dicom.service.refresh.complete=DICOM data refreshed
dicom.service.refresh.failed=Failed to refresh DICOM data
dicom.directory.not.exist=DICOM scan directory does not exist: {0}
dicom.directory.create.failed=Failed to create DICOM scan directory: {0}
dicom.no.files=No DICOM files found in directory: {0}
dicom.no.files.found=No DICOM files found
dicom.process.file.failed=Failed to process DICOM file: {0}, error: {1}
dicom.process.file.success=Successfully processed DICOM file: {0}
dicom.process.unknown.error=Unknown error occurred while processing DICOM file: {0} - {1}
dicom.process.summary=DICOM processing completed: success={0}, failed={1}
dicom.aspect.processing.error=Error processing DICOM data: {0} - {1}

# Analysis module messages
analysis.error=Analysis failed: {0}
analysis.timeout=Analysis timeout
analysis.unsupported=Unsupported analysis type: {0}

# Analysis status messages
analysis.in.progress=Analysis is already in progress, please wait for the current analysis to complete
analysis.started=Starting analysis...
analysis.completed=Analysis completed successfully, report generated
analysis.failed=Analysis failed, could not generate valid report
analysis.process.result.error=Error processing result: {0}

# Protocol related messages
analysis.protocol.info=Analysis protocol: {0} - {1}
analysis.series.prefix=Series

# Error messages
analysis.unknown.error=Unknown error
analysis.execution.failed=Execution failed: {0}

# File related messages
analysis.output.file.warning=Warning: Output file not generated



# Error message extraction keywords
analysis.io.error.prefix=IO error:
analysis.create.process.error=CreateProcess error=
analysis.ellipsis=...
