# System error messages
error.system.unexpected=ç³»ç»åçæªé¢æçéè¯¯
error.system.config=ç³»ç»éç½®éè¯¯: {0}
error.system.io=IOæä½éè¯¯: {0}
error.system.database=æ°æ®åºæä½éè¯¯: {0}

# Retry related messages
system.retry.attempt=éè¯æä½ (ç¬¬{0}æ¬¡ï¼å±{1}æ¬¡)
system.retry.failed=éè¯å¤±è´¥ (ç¬¬{0}æ¬¡): {1}
system.retry.exhausted=éè¯æ¬¡æ°å·²ç¨å°½ ({0}æ¬¡): {1}
system.retry.interrupted=éè¯æä½è¢«ä¸­æ­

# Business error messages
error.business.validation=æ°æ®éªè¯å¤±è´¥: {0}
error.business.processing=ä¸å¡å¤çå¤±è´¥: {0}
error.business.not.found=æªæ¾å°è¯·æ±çæ°æ®: {0}
error.business.not.allowed=ä¸åè®¸çæä½: {0}

# UI error messages
error.ui.operation=UIæä½éè¯¯: {0}
error.ui.display=UIæ¾ç¤ºéè¯¯: {0}

# DICOM validation messages
dicom.error.missing.required.tag=ç¼ºå°å¿éçæ ç­¾ {0} (0x{1})
dicom.error.invalid.tag=æ æçæ ç­¾ {0}: {1}
dicom.error.empty.pixel.data=åç´ æ°æ®ä¸ºç©º
dicom.error.invalid.dimension=æ æçå¾åç»´åº¦
dicom.error.invalid.bits.allocated=æ æçä½åé
dicom.error.invalid.samples=æ æçéæ ·æ°
dicom.error.size.limit.exceeded=è¶åºå¤§å°éå¶
dicom.error.size.mismatch=å¤§å°ä¸å¹é
dicom.error.invalid.image.params=æ æçå¾ååæ°: è¡={0}, å={1}, åéä½={2}, å­å¨ä½={3}, é«ä½={4}
dicom.error.empty.patient.id=æ£èIDä¸ºç©º
dicom.error.empty.patient.name=æ£èå§åä¸ºç©º
dicom.error.empty.series.id=åºåIDä¸ºç©º
dicom.error.empty.exam.id=æ£æ¥IDä¸ºç©º
dicom.error.empty.file.id=DICOMæä»¶IDä¸ºç©º
dicom.quick.validation.failed=å¿«ééªè¯å¤±è´¥

# DICOM file operation messages
dicom.file.not.found=DICOMæä»¶ä¸å­å¨: {0}
dicom.file.read.failed=è¯»åDICOMæä»¶å¤±è´¥: {0}
dicom.error.invalid.path=æ æçæä»¶è·¯å¾
dicom.provider.directory.invalid=æ æçç®å½: {0}
dicom.provider.scan.start=å¼å§æ«æç®å½: {0} {1}
dicom.provider.scan.complete=æ«æå®æï¼æ¾å° {0} ä¸ªDICOMæä»¶
dicom.provider.scan.failed=æ«æç®å½å¤±è´¥: {0}
dicom.provider.scan.directory=æ«æç®å½: {0}
dicom.provider.scan.no.files=æ«æç®å½æªæ¾å°DICOMæä»¶: {0}
dicom.provider.scan.found.files=æ¾å° {0} ä¸ªDICOMæä»¶
dicom.provider.processing.file=å¤çDICOMæä»¶: {0} ({1}/{2})
dicom.provider.processing.file.error=å¤çæä»¶å¤±è´¥: {0}
dicom.provider.processing.start=å¼å§å¤çæ°æä»¶
dicom.provider.processing.summary=DICOMå¤çå®æ: æå={0}, å¤±è´¥={1}

# DICOM basic error messages
dicom.error.invalid.data=æ æçDICOMæ°æ®: {0}
dicom.error.file.not.found=DICOMæä»¶æªæ¾å°: {0}
dicom.error.processing=DICOMå¤çéè¯¯: {0}
dicom.error.image.processing=DICOMå¾åå¤çéè¯¯: {0}
dicom.error.series.processing=DICOMåºåå¤çéè¯¯: {0}
dicom.error.exam.processing=DICOMæ£æ¥å¤çéè¯¯: {0}
dicom.error.tag=DICOMæ ç­¾éè¯¯: {0}
dicom.error.validation=DICOMéªè¯éè¯¯: {0}

# DICOM async operation messages
dicom.scan.async.failed=å¼æ­¥æ«æå¤±è´¥: {0}
dicom.read.async.failed=å¼æ­¥è¯»åå¤±è´¥: {0}
dicom.count.failed=è®¡æ°æä½å¤±è´¥
dicom.check.failed=æ£æ¥æä½å¤±è´¥
dicom.monitor.close.failed=å³é­çæ§å¤±è´¥: {0}

# DICOM provider basic messages
dicom.provider.model.null=DICOMæä»¶æ¨¡åä¸è½ä¸ºç©º
dicom.provider.model.id.invalid=DICOMæä»¶æ¨¡åå¿é¡»æææID
dicom.provider.file.not.found=DICOMæä»¶è·¯å¾æ æææä»¶ä¸å­å¨: {0}
dicom.provider.model.exists=å·æIDä¸º {0} çDICOMæä»¶æ¨¡åå·²å­å¨å¹¶å°è¢«æ´æ°
dicom.provider.no.images=åºå {0} æ²¡æå¯ç¨çå¾å
dicom.provider.no.series=æ£æ¥ {0} æ²¡æå¯ç¨çåºå
dicom.provider.data.cleared=æææ°æ®å·²æ¸é¤
dicom.provider.shutdown=DicomDataProviderå·²å³é­
dicom.provider.create.exam.failed=åå»ºæ£æ¥å¯¹è±¡å¤±è´¥: {0}
dicom.provider.create.series.failed=åå»ºåºåå¯¹è±¡å¤±è´¥: {0}
dicom.provider.update.exam.failed=æ´æ°æ£æ¥å±æ§å¤±è´¥: {0}
dicom.provider.update.image.failed=æ´æ°å¾åå±æ§å¤±è´¥: {0}
dicom.provider.update.series.failed=æ´æ°åºåå±æ§å¤±è´¥: {0}
dicom.provider.clear.failed=æ¸é¤DICOMæ°æ®å¤±è´¥: {0}
dicom.provider.shutdown.failed=å³é­DicomDataProviderå¤±è´¥: {0}
dicom.provider.validation.failed=DICOMæä»¶éªè¯å¤±è´¥: {0}
dicom.provider.tags.empty=DICOMæ ç­¾æ°æ®ä¸ºç©º
dicom.provider.study.uid.empty=æ£æ¥å®ä¾UIDä¸ºç©º
dicom.provider.series.uid.empty=åºåå®ä¾UIDä¸ºç©º
dicom.provider.init=ç³»ç»åå§åå®æï¼æ ¹ç®å½: {0}
dicom.provider.listener.error=çå¬å¨éè¯¯: {0}
dicom.provider.monitor.start=å¼å§çæ§ç®å½: {0}
dicom.provider.monitor.stopped=æä»¶çæ§å·²åæ­¢
dicom.provider.monitor.failed=æä»¶çæ§å¤±è´¥: {0}
dicom.provider.file.changed=æ£æµå°DICOMæä»¶åå: {0}
dicom.provider.first.exam=æ£æµå°ç¬¬ä¸ä¸ªæ£æ¥ï¼èªå¨éæ©: {0}
dicom.provider.first.series=èªå¨éæ©ç¬¬ä¸ä¸ªåºå: {0}
dicom.provider.images.loaded=æåå è½½åºåå¾åï¼æ°é: {0}
dicom.provider.exam.attributes.updated=æåæ´æ°æ£æ¥å±æ§: æ£èID={0}, æ£èå§å={1}
dicom.provider.validate.first.exam.failed=éªè¯é¦ä¸ªæ£æ¥å¤±è´¥

# Configuration Messages
config.file.not.found=éç½®æä»¶æªæ¾å°: {0}
config.file.loaded=æåå è½½éç½®æä»¶
config.key.not.found=éç½®é®æªæ¾å°: {0}
config.invalid.number=æ æçæ°å­éç½®å¼: {0} = {1}
config.invalid.log.level=æ æçæ¥å¿çº§å«: {0}

# System Messages
dicom.provider.system.init=ç³»ç»åå§åå®æï¼æ ¹ç®å½: {0}
dicom.provider.system.shutdown=ç³»ç»å·²å³é­

# Tag parsing messages
dicom.tag.create.failed=åå»ºæ ç­¾å¤±è´¥: {0}
dicom.tag.parse.failed=è§£æDICOMæ ç­¾å¤±è´¥: {0}
dicom.tag.empty.model=DicomFileModelä¸ºç©º
dicom.tag.empty.attributes=DICOMå±æ§ä¸ºç©º
dicom.tag.empty.id=æ ç­¾IDä¸è½ä¸ºç©º
dicom.model.empty.id=IDä¸è½ä¸ºç©º
dicom.image.empty.id=å¾åIDä¸è½ä¸ºç©º
dicom.image.create.failed=åå»ºDicomImageå¤±è´¥: {0}

# Service related messages
dicom.service.init.failed=DICOMæå¡åå§åå¤±è´¥
dicom.service.init.complete=DICOMæå¡åå§åå®æï¼æ«æç®å½: {0}
dicom.service.refresh.complete=DICOMæ°æ®å·²å·æ°
dicom.service.refresh.failed=å·æ°DICOMæ°æ®å¤±è´¥
dicom.directory.not.exist=DICOMæ«æç®å½ä¸å­å¨: {0}
dicom.directory.create.failed=åå»ºDICOMæ«æç®å½å¤±è´¥: {0}
dicom.no.files=ç®å½ä¸­æ²¡ææ¾å°DICOMæä»¶: {0}
dicom.no.files.found=æªæ¾å°DICOMæä»¶
dicom.process.file.failed=å¤çDICOMæä»¶å¤±è´¥: {0}, éè¯¯: {1}
dicom.process.file.success=æåå¤çDICOMæä»¶: {0}
dicom.process.unknown.error=å¤çDICOMæä»¶æ¶åçæªç¥éè¯¯: {0} - {1}
dicom.process.summary=DICOMå¤çå®æ: æå={0}, å¤±è´¥={1}
dicom.aspect.processing.error=å¤çDICOMæ°æ®æ¶åçéè¯¯: {0} - {1}

# åææ¨¡åæ¶æ¯
analysis.error=åæå¤±è´¥: {0}
analysis.timeout=åæè¶æ¶
analysis.unsupported=ä¸æ¯æçåæç±»å: {0}

# åæç¶ææ¶æ¯
analysis.in.progress=åæå·²ç»å¨è¿è¡ä¸­ï¼è¯·ç­å¾å½ååæå®æ
analysis.started=å¼å§åæ...
analysis.completed=åææåå®æï¼ç»ææ¥åå·²çæ
analysis.failed=åæå¤±è´¥ï¼æªè½çææææ¥å
analysis.process.result.error=å¤çç»ææ¶åçéè¯¯: {0}

# åè®®ç¸å³æ¶æ¯
analysis.protocol.info=åæåè®®: {0} - {1}
analysis.series.prefix=Series

# éè¯¯æ¶æ¯
analysis.unknown.error=æªç¥éè¯¯
analysis.execution.failed=æ§è¡å¤±è´¥: {0}

# æä»¶ç¸å³æ¶æ¯
analysis.output.file.warning=è­¦å: è¾åºæä»¶æªçæ



# éè¯¯æ¶æ¯æåå³é®è¯
analysis.io.error.prefix=IOéè¯¯:
analysis.create.process.error=CreateProcess error=
analysis.ellipsis=...
