# CT Quality Assurance Tool éç½®æä»¶

# åºç¨ç¨åºè®¾ç½®
app.name=CT Quality Assurance Tool
app.version=1.0.0
app.logging.level=INFO

# UIè®¾ç½®
ui.theme=light
ui.locale=zh_CN
ui.language=zh_CN

# è¡¨æ ¼è®¾ç½®
table.config.file=config/table_columns.properties

# æä»¶çæ§
file.monitor.enabled=true
file.monitor.interval=5000

# çº¿ç¨æ± è®¾ç½®
thread.pool.size=4
dicom.max.threads=4

# DICOMè®¾ç½®
dicom.scan.directory.win=C:\\GEHC\\usr\\g\\sdc_image_pool\\images
dicom.scan.directory.linux=/usr/g/sdc_image_pool/images/p1
dicom.cache.enabled=true
dicom.cache.size=1000

# DICOMæä»¶å¤çéç½®
dicom.validation.detailed_warnings=true
dicom.validation.group_warnings=true
dicom.validation.max_examples=10
dicom.validation.skip_invalid=true
dicom.scan.parallel=true

# æ¥å
report.output.directory.win=C:\\GEHC\\usr\\g\\bin\\qatdemo\\reports
report.output.directory.linux=/usr/g/bin/qatdemo/reports
