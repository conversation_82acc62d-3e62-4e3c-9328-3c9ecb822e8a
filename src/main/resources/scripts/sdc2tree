#!/bin/bash
## Name:	sdc2tree
## Author:	<PERSON><PERSON> (2004)
## Revised:	18 November 2004
## Function: 	copies	individual dicom images 
##			or multiple series
##			or multiple exams
##		from the Helios OC image database to a directory tree 
##		rooted in /usr/g/ctuser
##
## Useage:	select the images, series or exams to copy using the ImageWorks browser
##		open a shell and type sdc2tree
##		images will be copied to a unique directory:
##		/usr/g/ctuser/DCM_TREE/bayNN/date-eNN/sNN, based on info from the dicom header
##		images are renamed by image number, but with a fixed length, like i000001.dcm
##
## Options:	-v verbose output (not yet supported)
##		-V prints version number and exits
##		-h prints this useage information and exits
##		
## Needs:	DcmDump from the DCMTK dicom toolkit (should be part of the build)
##		dicom.dic (should be in the build)
##
## tar HOWTO:	The preferred method of moving the image tree to another system is to first create an archive
##		file (compressed or noncompressed), as follows:
##		cd /usr/g/ctuser/DCM_TREE
##		tar -cvf Myfile.tar bayNN/date-*	# this example will add all exams with today's datecode
##		tar -cvzf Myfile.tgz bayNN/date-*	# tar with gzip compression (~70% compression)
##		tar -xvzf Myfile.tgz			# extract the files on the target machine
##
## zip HOWTO:	If you prefer, our GNU/linux CT console can create zip files which are compatible with pkzip.
##		zip -r Myfile.zip bayNN/date-*
##
## known bugs:	Service Exams which have been reconstructed on the console don't have the StationName field
##		filled-in correctly in the dicom header, so that directory name comes out funny. You will need
##		to rename it manually, with quotes around the bad directory name.
#
process_image ()
{
	echo $1
# image file from sdc_image_pool/images is expected as $1
#	sys=`DcmDump $1 |grep 0009,1002 |awk '{print $3}' |tr -d []` && sys=${sys}/
	IFS=$'\n'
	string_to_parse=($(DcmDump.py $1))

	date=`echo ${string_to_parse[0]} |grep StudyDate |awk {'print $3'} |tr -d []`
	sys=`echo ${string_to_parse[1]} |grep StationName |awk '{print $3}' |tr -d []|awk -F "_" '{print $1}'` && sys=${sys}/
	exam=`echo ${string_to_parse[2]} |grep StudyID |awk {'print $3'} |tr -d []` && exam=${date}-e${exam}/
	series=`echo ${string_to_parse[3]} |grep SeriesNumber |awk {'print $3'} |tr -d []` && series=s${series}/
	img=`echo ${string_to_parse[4]}  |grep ImageNumber |awk {'print $3'} |tr -d []`
		# format to 6 characters
	[[ ${#img} -lt "6" ]] && img=000000${img} && img=${img:${#img}-6:6} && img=i${img}.dcm
	[[ ! -d $tree$sys ]] && mkdir $tree$sys && echo $tree$sys" created."
	[[ ! -d $tree$sys$exam ]] && mkdir $tree$sys$exam && echo $tree$sys$exam" created."
	[[ ! -d $tree$sys$exam$series ]] && mkdir $tree$sys$exam$series && echo $tree$sys$exam$series" created."
#echo $1
#echo $tree$sys$exam$series$img
	cp $1 $tree$sys$exam$series$img
	return 0
}
#
version="0.3"
while getopts ":hvV" Option
do
   case $Option in
	h ) cat $0 |grep ^## && exit ;;
	v ) echo "option v not supported yet" && exit ;;
	V ) echo $0" version "${version} && exit ;;
   esac
done
sdc_base="/usr/tmp/"
export DCMDICTPATH=/usr/g/config/dicom.dic
tree=$HOME"/DCM_TREE/"
#
#
[[ ! -d $tree ]] && mkdir $tree && echo $tree" created."
# split last line of sdc_selection only
sdc_array=( `awk 'BEGIN {i = 1} END {while (i<=NF) {{print $i} {++i}}}' $sdc_base/sdc_selection` )
if [ ! -f ${sdc_array[0]} ] ; then
	test=`echo ${sdc_array[0]} |awk -F "/" '{print $NF}'` && test=${test:0:1}
# several series are selected
	if [[ $test = "s" ]]; then
		for sdc_series in ${sdc_array[@]}; do
			if [ -d $sdc_series ]; then
				pushd $sdc_series &> /dev/null
				i=0
				for image in *; do
					esi=$sdc_series"/"$image
					[[ -f $image ]] && process_image $image && echo $esi && let i++
				done
				echo $i" images exported"
				popd &> /dev/null
			fi
		done
# several exams are selected
	elif [[ $test = "e" ]]; then
		for sdc_exam in ${sdc_array[@]}; do
			if [ -d $sdc_exam ]; then
				pushd $sdc_exam &> /dev/null
				for sdc_series in *; do
					if [ -d $sdc_series ]; then
						pushd $sdc_series &> /dev/null
						i=0
						for image in *; do
							esi=$sdc_exam"/"$sdc_series"/"$image
							[[ -f $image ]] && process_image $image && echo $esi && let i++
						done						
						echo $i" images exported"
						popd &> /dev/null
					fi
				done
				popd &> /dev/null
			fi			
		done
# unexpected input
	else
		echo "I'm confuzed ..."
		exit
	fi
else
i=0
for image in ${sdc_array[@]}
do
	[[ -f $image ]] && process_image $image && echo $image && let i++
done
echo $i" images exported"
fi
