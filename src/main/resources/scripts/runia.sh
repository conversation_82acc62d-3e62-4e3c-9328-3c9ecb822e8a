#!/bin/csh -f

# stu engine startup script
setenv BIN /usr/g/bin
setenv CONFIGDIR /usr/g/config
setenv QAT2DIR /usr/g/bin/QATdemo
setenv JVM_PATH /usr/java64/latest/bin/java

set ADD_PARAMS=""

set LOG_FILENAME=/usr/g/service/log/stu-`date +\%d`-ia.out
echo "$LOG_FILENAME"

# 添加时间戳到日志行
echo "$(date '+\%Y-\%m-\%d \%H:\%M:\%S') ==========================================" >> "$LOG_FILENAME"
echo "$(date '+\%Y-\%m-\%d \%H:\%M:\%S') starting feature $1" >> "$LOG_FILENAME"
date >> "$LOG_FILENAME"

"$JVM_PATH" $ADD_PARAMS -cp "${QAT2DIR}/demo-1.0-SNAPSHOT.jar" -Djava.util.logging.config.file=/usr/g/config/stu/config/logging.properties com.ge.med.ct.ui.QualityAssuranceTool $1 >> "$LOG_FILENAME"
