#!/usr/bin/perl
# Added by Kiran

use strict;
use warnings;

# 读取命令行参数
my %args;
while (@ARGV) {
    my $arg = shift @ARGV;
    if ($arg eq '-text') {
        $args{text} = '';
    } elsif ($arg eq '-input') {
        $args{input} = shift @ARGV;
    } elsif ($arg eq '-output') {
        $args{output} = shift @ARGV;
    } elsif ($arg eq '-protocol') {
        $args{protocol} = shift @ARGV;
    }
}

# 打印读取的参数（用于调试）
print "Text Mode: $args{text}\n" if defined $args{text};
print "Input Files: $args{input}\n" if defined $args{input};
print "Output File: $args{output}\n" if defined $args{output};
print "Protocol: $args{protocol}\n" if defined $args{protocol};

# 设置环境变量
if ( $^O eq 'linux') {
    local $ENV{"LD_LIBRARY_PATH"} = "/usr/g/ctuser/gvtk/arch/linux24/lib:/usr/g/ctuser/gvtk/arch/linux24d/lib:/usr/g/ctuser/gvtk/arch/linux24-gcc3/lib:$ENV{LD_LIBRARY_PATH}";
} else {
    local $ENV{"LD_LIBRARYN32_PATH"} = "/usr/g/ctuser/gvtk/arch/irix65/lib/:$ENV{LD_LIBRARYN32_PATH}";
}

# 构建 exec 命令
my $exec_command = "/usr/g/bin/IAUI";
my @exec_args;

push @exec_args, "-text" if defined $args{text};
push @exec_args, "-input", $args{input} if defined $args{input};
push @exec_args, "-output", $args{output} if defined $args{output};
push @exec_args, "-protocol", $args{protocol} if defined $args{protocol};

# 打印 exec 命令（用于调试）
print "Executing: $exec_command @exec_args\n";

# 执行外部命令
exec $exec_command, @exec_args;
