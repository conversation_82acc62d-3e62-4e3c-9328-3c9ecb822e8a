#!/usr/bin/perl
# IA Commander - Interface script for CT quality analysis tool

use strict;
use warnings;
use File::Basename;
use File::Path qw(mkpath);
use Cwd;
use POSIX qw(strftime);

# Configuration
my $DEBUG_MODE = 0;
my $LOG_DIR = "/usr/g/bin/qatdemo/logs";
my $LOG_PREFIX = "ia2commander";
my $IAUI_DIR = "/usr/g/bin";
my $IAUI_EXE = "IAUI";

# Initialize log file
ensure_log_directory();
my $log_file = get_log_filename();
my $LOG_FH;
open($LOG_FH, ">>", $log_file) or warn "Cannot open log file $log_file: $!";

# Process command
my $cmd_line = join(" ", @ARGV);
log_msg("Command line: $cmd_line");

my %args = parse_args(@ARGV);
validate_args(\%args);
check_input_file($args{input});
ensure_output_dir(dirname($args{output}));
execute_iaui_command(\%args);

close($LOG_FH) if $LOG_FH;
exit 0;

# Core functions
sub ensure_log_directory {
    if (! -d $LOG_DIR) {
        eval { mkpath($LOG_DIR, 0, 0755) };
        if ($@) {
            print "WARNING: Could not create log directory: $@\n";
        }
    }
}

sub get_log_filename {
    my $timestamp = strftime("%Y%m%d", localtime);
    return "$LOG_DIR/${LOG_PREFIX}_$timestamp.log";
}

sub log_msg {
    my ($message, $level) = @_;
    $level ||= "INFO";
    
    my $formatted = "[$level] $message";
    print $LOG_FH "$formatted\n" if $LOG_FH;
    
    print "$formatted\n" if ($DEBUG_MODE || $level eq "ERROR" || $level eq "WARNING");
}

sub parse_args {
    my %args;
    
    # Check for text mode
    my $cmd_str = join(" ", @_);
    $args{text} = 1 if ($cmd_str =~ /\s-text\s|\s-text$|^-text\s/);
    
    # Try to parse quoted args
    if ($cmd_str =~ /-input\s+(?:["']([^"']+)["']|(\S+))/) {
        $args{input} = $1 || $2;
    }
    
    if ($cmd_str =~ /-output\s+(?:["']([^"']+)["']|(\S+))/) {
        $args{output} = $1 || $2;
    }
    
    if ($cmd_str =~ /-protocol\s+(?:["']([^"']+)["']|(\S+))/) {
        $args{protocol} = $1 || $2;
    }
    
    # If not all required args were found, try standard parsing
    if (!$args{input} || !$args{output} || !$args{protocol}) {
        my @args_array = @_;
        
        while (@args_array) {
            my $arg = shift @args_array;
            if ($arg eq '-text') {
                $args{text} = 1;
            } elsif ($arg eq '-input' && @args_array) {
                $args{input} = shift @args_array;
            } elsif ($arg eq '-output' && @args_array) {
                $args{output} = shift @args_array;
            } elsif ($arg eq '-protocol' && @args_array) {
                $args{protocol} = shift @args_array;
            }
        }
    }
    
    return %args;
}

sub validate_args {
    my ($args) = @_;
    
    foreach my $param ('input', 'output', 'protocol') {
        if (!defined $args->{$param}) {
            log_msg("Missing required parameter: -$param", "ERROR");
            exit 1;
        }
    }
}

sub check_input_file {
    my ($input) = @_;
    
    if (!-f $input) {
        log_msg("Input file not found: $input", "ERROR");
        exit 1;
    }
}

sub ensure_output_dir {
    my ($dir) = @_;
    
    if (!-d $dir) {
        log_msg("Creating output directory: $dir");
        eval { mkpath($dir, 0, 0755) };
        if ($@) {
            log_msg("Cannot create output directory: $@", "WARNING");
        } else {
            chmod 0755, $dir;
        }
    }
}

sub execute_iaui_command {
    my ($args) = @_;
    
    # Check IAUI executable
    my $iaui_path = "$IAUI_DIR/$IAUI_EXE";
    if (!-e $iaui_path) {
        log_msg("IAUI executable not found: $iaui_path", "ERROR");
        exit 1;
    }
    
    if (!-x $iaui_path) {
        log_msg("IAUI not executable, attempting to set permission", "WARNING");
        chmod 0755, $iaui_path;
        if (!-x $iaui_path) {
            log_msg("Cannot set IAUI executable permission", "ERROR");
            exit 1;
        }
    }
    
    # Set environment for Linux
    if ($^O eq 'linux') {
        local $ENV{"LD_LIBRARY_PATH"} = "/usr/g/ctuser/gvtk/arch/linux24/lib:/usr/g/ctuser/gvtk/arch/linux24d/lib:/usr/g/ctuser/gvtk/arch/linux24-gcc3/lib:$ENV{LD_LIBRARY_PATH}";
    }
    
    # Execute command
    my $cmd = build_command($args);
    my $result = `$cmd 2>&1`;
    my $exit_code = $? >> 8;
    
    log_msg("IAUI exit code: $exit_code");
    
    if ($exit_code != 0) {
        log_msg("Command execution failed with code: $exit_code", "ERROR");
        log_msg("Error output: $result", "ERROR") if $result;
        exit $exit_code;
    }
    
    # Check output file
    if (-f $args->{output}) {
        chmod 0644, $args->{output};
        my $size = -s $args->{output};
        log_msg("Report created: $args->{output}, size: $size bytes");
    } else {
        log_msg("Report not created: $args->{output}", "WARNING");
    }
}

sub build_command {
    my ($args) = @_;
    
    my $cmd = "cd $IAUI_DIR && ./$IAUI_EXE";
    $cmd .= " -text" if defined $args->{text};
    $cmd .= " -input \"$args->{input}\"" if defined $args->{input};
    $cmd .= " -output \"$args->{output}\"" if defined $args->{output};
    $cmd .= " -protocol \"$args->{protocol}\"" if defined $args->{protocol};
    
    return $cmd;
}
