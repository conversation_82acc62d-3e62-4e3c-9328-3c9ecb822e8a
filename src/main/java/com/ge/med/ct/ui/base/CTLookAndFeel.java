package com.ge.med.ct.ui.base;

import javax.swing.UIManager;
import javax.swing.BorderFactory;
import javax.swing.plaf.ColorUIResource;
import javax.swing.plaf.FontUIResource;

import com.ge.med.ct.ui.utils.UIConstants;

import java.awt.Color;
import java.util.Enumeration;

/**
 * 自定义外观和感觉，实现CT工作站风格的UI
 */
public class CTLookAndFeel {
    
    /**
     * 应用CT工作站风格的UI
     */
    public static void applyLookAndFeel() {
        try {
            // 尝试使用系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            
            // 设置全局字体
            setUIFont(new FontUIResource(UIConstants.DEFAULT_FONT));
            
            // 设置组件颜色
            UIManager.put("Panel.background", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            UIManager.put("OptionPane.background", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            UIManager.put("Window.background", new ColorUIResource(UIConstants.BACKGROUND_COLOR));
            UIManager.put("Frame.background", new ColorUIResource(UIConstants.BACKGROUND_COLOR));
            UIManager.put("Dialog.background", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            
            // 设置按钮样式
            UIManager.put("Button.background", new ColorUIResource(UIConstants.BUTTON_BACKGROUND));
            UIManager.put("Button.foreground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("Button.border", BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(UIConstants.BUTTON_BORDER, 1),
                    BorderFactory.createEmptyBorder(3, 8, 3, 8)));
            UIManager.put("Button.focus", new ColorUIResource(new Color(0, 0, 0, 0)));
            UIManager.put("Button.select", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            
            // 设置文本框样式
            UIManager.put("TextField.background", new ColorUIResource(UIConstants.FIELD_BACKGROUND));
            UIManager.put("TextField.foreground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("TextField.caretForeground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("TextField.selectionBackground", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            UIManager.put("TextField.selectionForeground", new ColorUIResource(Color.WHITE));
            UIManager.put("TextField.border", BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(UIConstants.BORDER_COLOR, 1),
                    BorderFactory.createEmptyBorder(2, 5, 2, 5)));
            
            // 设置标签样式
            UIManager.put("Label.foreground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("Label.background", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            
            // 设置工具栏样式
            UIManager.put("ToolBar.background", new ColorUIResource(UIConstants.TOOLBAR_BACKGROUND));
            UIManager.put("ToolBar.foreground", new ColorUIResource(Color.WHITE));
            UIManager.put("ToolBar.dockingBackground", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            UIManager.put("ToolBar.floatingBackground", new ColorUIResource(UIConstants.TOOLBAR_BACKGROUND));
            
            // 设置表格样式
            UIManager.put("Table.background", new ColorUIResource(Color.WHITE));
            UIManager.put("Table.foreground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("Table.selectionBackground", new ColorUIResource(UIConstants.TABLE_SELECTED_ROW_COLOR));
            UIManager.put("Table.selectionForeground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("Table.gridColor", new ColorUIResource(UIConstants.BORDER_COLOR));
            UIManager.put("TableHeader.background", new ColorUIResource(UIConstants.TABLE_HEADER_COLOR));
            UIManager.put("TableHeader.foreground", new ColorUIResource(Color.WHITE));
            UIManager.put("TableHeader.font", UIConstants.TABLE_HEADER_FONT);
            
            // 设置滚动条样式
            UIManager.put("ScrollBar.thumb", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            UIManager.put("ScrollBar.thumbHighlight", new ColorUIResource(UIConstants.PRIMARY_COLOR));
            UIManager.put("ScrollBar.thumbDarkShadow", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            UIManager.put("ScrollBar.thumbShadow", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            UIManager.put("ScrollBar.track", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            UIManager.put("ScrollBar.trackHighlight", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            
            // 设置下拉框样式
            UIManager.put("ComboBox.background", new ColorUIResource(UIConstants.FIELD_BACKGROUND));
            UIManager.put("ComboBox.foreground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("ComboBox.selectionBackground", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            UIManager.put("ComboBox.selectionForeground", new ColorUIResource(Color.WHITE));
            
            // 设置菜单样式
            UIManager.put("Menu.background", new ColorUIResource(UIConstants.TOOLBAR_BACKGROUND));
            UIManager.put("Menu.foreground", new ColorUIResource(Color.WHITE));
            UIManager.put("MenuItem.background", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            UIManager.put("MenuItem.foreground", new ColorUIResource(UIConstants.TEXT_COLOR));
            UIManager.put("MenuItem.selectionBackground", new ColorUIResource(UIConstants.BTN_SECONDARY_COLOR));
            UIManager.put("MenuItem.selectionForeground", new ColorUIResource(Color.WHITE));
            
            // 设置分隔面板样式
            UIManager.put("SplitPane.background", new ColorUIResource(UIConstants.PANEL_BACKGROUND_COLOR));
            UIManager.put("SplitPane.dividerSize", 8);
            UIManager.put("SplitPaneDivider.border", BorderFactory.createEmptyBorder());
            UIManager.put("SplitPane.dividerFocusColor", new ColorUIResource(UIConstants.BORDER_COLOR));
            
        } catch (Exception e) {
            System.err.println("无法应用CT工作站风格的UI: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 设置全局UI字体
     * @param font 要应用的字体
     */
    private static void setUIFont(FontUIResource font) {
        Enumeration<Object> keys = UIManager.getDefaults().keys();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            Object value = UIManager.get(key);
            if (value instanceof FontUIResource) {
                UIManager.put(key, font);
            }
        }
    }
} 