package com.ge.med.ct.ui.components;

import javax.swing.JTable;
import javax.swing.table.DefaultTableModel;
import javax.swing.SwingUtilities;

import com.ge.med.ct.ui.utils.TableOperations;


public class CTTable extends JTable {
    

    public CTTable() {
        this(new DefaultTableModel());
    }
    

    public CTTable(DefaultTableModel model) {
        super(model);
    }
    

    public CTTable(String[] columnNames) {
        this(new DefaultTableModel(columnNames, 0));
    }

        /**
     * 更新数据并自动调整列宽
     * @param model 表格模型
     */
    public void updateData(DefaultTableModel model) {
        setModel(model);
        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidths(this));
    }

    /**
     * 更新表格数据并自动调整列宽
     * @param data 表格数据
     */
    public void updateData(java.util.Vector<java.util.Vector<String>> data) {
        DefaultTableModel model = (DefaultTableModel) getModel();
        model.setRowCount(0);
        for (java.util.Vector<String> row : data) {
            model.addRow(row);
        }
        
        // 数据加载后，自动调整列宽
        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidths(this));
    }

    public void clearItems() {
        DefaultTableModel model = (DefaultTableModel) getModel();
        model.setRowCount(0);
        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidths(this));
    }

    /**
     * 更新表格数据后自动调整列宽
     */
    @Override
    public void addNotify() {
        super.addNotify();
        // 组件添加到容器后调整列宽
        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidths(this));
    }

} 