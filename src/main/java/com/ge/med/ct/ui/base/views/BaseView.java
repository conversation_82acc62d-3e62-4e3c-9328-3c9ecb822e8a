package com.ge.med.ct.ui.base.views;

import javax.swing.JOptionPane;
import java.util.logging.Logger;

import com.ge.med.ct.dicom.util.DicomDataProvider;
import com.ge.med.ct.ui.components.CTJSplitPane;

public abstract class BaseView extends CTJSplitPane implements IBaseView {
    private static final long serialVersionUID = 1L;
    protected static final Logger LOGGER = Logger.getLogger(BaseView.class.getName());
    
    protected DicomDataProvider dataManager;
    
    public BaseView(int orientation) {
        super(orientation);
    }
    
    @Override
    public void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "提示", JOptionPane.INFORMATION_MESSAGE);
    }
    
    @Override
    public void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "错误", JOptionPane.ERROR_MESSAGE);
    }
    
    @Override
    public void setDataManager(DicomDataProvider dataManager) {
        this.dataManager = dataManager;
    }
    
    @Override
    public DicomDataProvider getDataManager() {
        return dataManager;
    }
    
    @Override
    public void refreshData() {
    }    
    
    @Override
    public void clearContents() {
    }

    protected void logInfo(String message) {
        LOGGER.info(message);
    }
    
    protected void logWarning(String message) {
        LOGGER.warning(message);
    }
    
    protected void logError(String message, Throwable throwable) {
        LOGGER.severe(message);
        if (throwable != null) {
            LOGGER.severe(throwable.getMessage());
            throwable.printStackTrace();
        }
    }
} 