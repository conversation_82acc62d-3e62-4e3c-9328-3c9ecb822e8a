package com.ge.med.ct.ui.views;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Color;
import java.awt.GridBagLayout;
import java.awt.Insets;
import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JSplitPane;
import javax.swing.border.TitledBorder;

import com.ge.med.ct.dicom.util.DicomDataProvider;
import com.ge.med.ct.ui.base.listeners.AnalysisViewListener;
import com.ge.med.ct.ui.base.views.BaseView;
import com.ge.med.ct.ui.base.views.IAnalysisView;
import com.ge.med.ct.ui.components.CTButton;
import com.ge.med.ct.ui.components.CTTextField;
import com.ge.med.ct.ui.utils.ClassAlias.GBC;
import com.ge.med.ct.ui.utils.UIConstants;
import com.ge.med.ct.ui.widgets.DebugPanel;
import com.ge.med.ct.ui.widgets.PatientPanel;

/**
 * Analysis视图，负责UI展示
 */
public class AnalysisView extends BaseView implements IAnalysisView {
    
    private AnalysisViewListener viewListener;

    private final CTButton btnAccept = new CTButton("Accept");
    private final CTTextField inputField = new CTTextField("", 30);
    private final CTTextField outputField = new CTTextField("", 30);
    private final CTTextField protocolField = new CTTextField("", 30);
    
    private final DebugPanel debugPanel = new DebugPanel();
    private final PatientPanel patientPanel = new PatientPanel();

    public AnalysisView() {
        super(JSplitPane.HORIZONTAL_SPLIT);
        initializeComponents();
        setupLayout();
        setupListeners();
    }

    private void setupListeners() {
        btnAccept.addActionListener(e -> {
            if (viewListener != null) {
                viewListener.onAcceptClicked();
            }
        });

        patientPanel.setSelectionChangeListener((inputPath, outputPath, protocol) -> {
            if (viewListener != null) {
                viewListener.sendCommandArgs(inputPath, outputPath, protocol);
            }
        });
    }

    @Override
    public void setViewListener(AnalysisViewListener listener) {
        this.viewListener = listener;
    }

    @Override
    public void setInputPath(String path) {
        inputField.setText(path);
    }

    @Override
    public void setOutputPath(String path) {
        outputField.setText(path);
    }

    @Override
    public void setProtocol(String protocol) {
        protocolField.setText(protocol);
    }

    @Override
    public String getInputPath() {
        return inputField.getText();
    }

    @Override
    public String getOutputPath() {
        return outputField.getText();
    }

    @Override
    public String getProtocol() {
        return protocolField.getText();
    }

    @Override
    public void appendDebugText(String text) {
        debugPanel.appendText(text);
    }

    @Override
    public void clearDebugText() {
        debugPanel.clearContents();
    }

    @Override
    public void updatePatientData(DicomDataProvider dicomManager) {
        patientPanel.updateContents(dicomManager);
    }

    @Override
    public void clearContents() {
        inputField.setText("");
        outputField.setText("");
        protocolField.setText("");
        patientPanel.clearContents();
        debugPanel.clearContents();
    }

    @Override
    public void showMessage(String message) {
        if (viewListener != null) {
            viewListener.onShowMessage(message);
        }
    }

    @Override
    public void showError(String message) {
        if (viewListener != null) {
            viewListener.onShowError(message);
        }
    }

    private void initializeComponents() {
        setDividerLocation(500);
        setResizeWeight(0.3);
        
        btnAccept.setFont(UIConstants.LARGE_FONT);
        btnAccept.setPreferredSize(new Dimension(150, 38));
    }

    private void setupLayout() {
        // 创建左侧面板
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
        leftPanel.add(patientPanel, BorderLayout.CENTER);
        
        // 创建右侧面板
        JPanel rightPanel = new JPanel(new BorderLayout());
        rightPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
        
        // 创建controlPanel
        JPanel controlPanel = new JPanel();
        controlPanel.setLayout(new GridBagLayout());
        controlPanel.setPreferredSize(new Dimension(0, 208));
        
        // 创建带标题的边框
        TitledBorder titledBorder = BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(),
            "Protocol Command",
            TitledBorder.LEFT,
            TitledBorder.TOP,
            UIConstants.DEFAULT_FONT
        );
        titledBorder.setTitleColor(Color.BLACK);
        controlPanel.setBorder(titledBorder);
        
        // 设置半透明背景
        controlPanel.setOpaque(false);
        controlPanel.setBackground(new Color(255, 255, 255, 128));
        
        GBC labelGbc = new GBC();
        labelGbc.gridx = 0;
        labelGbc.fill = GBC.HORIZONTAL;
        labelGbc.weightx = 0.25;
        labelGbc.insets = new Insets(5, 5, 5, 5);
        labelGbc.anchor = GBC.EAST;

        GBC fieldGbc = new GBC();
        fieldGbc.gridx = 1;
        fieldGbc.fill = GBC.HORIZONTAL;
        fieldGbc.weightx = 0.75;
        fieldGbc.insets = new Insets(5, 5, 5, 5);
        
        JLabel textLabel = new JLabel("-text");
        fieldGbc.gridy = 0;
        controlPanel.add(textLabel, fieldGbc);
        
        labelGbc.gridy = 1;
        JLabel inputLabel = new JLabel("-input:", JLabel.RIGHT);
        controlPanel.add(inputLabel, labelGbc);
        
        fieldGbc.gridy = 1;
        fieldGbc.fill = GBC.HORIZONTAL;
        fieldGbc.anchor = GBC.CENTER;
        controlPanel.add(inputField, fieldGbc);
        
        labelGbc.gridy = 2;
        JLabel outputLabel = new JLabel("-output:", JLabel.RIGHT);
        controlPanel.add(outputLabel, labelGbc);
        
        fieldGbc.gridy = 2;
        controlPanel.add(outputField, fieldGbc);
        
        labelGbc.gridy = 3;
        JLabel protocolLabel = new JLabel("-protocol:", JLabel.RIGHT);
        controlPanel.add(protocolLabel, labelGbc);
        
        fieldGbc.gridy = 3;
        controlPanel.add(protocolField, fieldGbc);
        
        labelGbc.gridy = 4;
        controlPanel.add(new JPanel(), labelGbc);
        
        fieldGbc.gridy = 4;
        fieldGbc.anchor = GBC.EAST;
        fieldGbc.fill = GBC.NONE;
        controlPanel.add(btnAccept, fieldGbc);
        
        debugPanel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(),
            "Debug Output.",
            TitledBorder.LEFT,
            TitledBorder.TOP,
            UIConstants.DEFAULT_FONT
        ));
        
        rightPanel.add(controlPanel, BorderLayout.NORTH);
        rightPanel.add(debugPanel, BorderLayout.CENTER);
        
        setLeftComponent(leftPanel);
        setRightComponent(rightPanel);
    }

    public PatientPanel getPatientPanel() {
        return patientPanel;
    }

    public DebugPanel getDebugPanel() {
        return debugPanel;
    }
}