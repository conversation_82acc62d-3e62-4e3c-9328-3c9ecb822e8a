package com.ge.med.ct.ui.utils;

import java.awt.Component;
import java.awt.FontMetrics;

import javax.swing.JTable;
import javax.swing.JViewport;
import javax.swing.JScrollPane;
import javax.swing.SwingUtilities;
import javax.swing.table.TableColumn;
import javax.swing.table.TableCellRenderer;

/**
 * 表格操作工具类
 * 提供表格相关的通用操作方法
 */
public class TableOperations {
    private static final int MIN_COLUMN_WIDTH = 50;
    private static final int PADDING = 20;
    
    public static int getMaxContentWidth(JTable table, int column) {
        if (table == null || column < 0 || column >= table.getColumnCount()) {
            return MIN_COLUMN_WIDTH;
        }

        int maxWidth = 0;
        TableCellRenderer renderer = table.getColumnModel().getColumn(column).getCellRenderer();
        if (renderer == null) {
            renderer = table.getDefaultRenderer(table.getColumnClass(column));
        }
        
        FontMetrics fontMetrics = table.getFontMetrics(UIConstants.TABLE_CONTENT_FONT);
        
        for (int row = 0; row < table.getRowCount(); row++) {
            Object value = table.getValueAt(row, column);
            if (value != null) {
                String text = value.toString();
                int textWidth = fontMetrics.stringWidth(text);
                
                Component comp = renderer.getTableCellRendererComponent(table, value, false, false, row, column);
                int componentWidth = comp.getPreferredSize().width;
                
                maxWidth = Math.max(maxWidth, Math.max(textWidth, componentWidth));
            }
        }
        
        return maxWidth + PADDING;
    }

    public static int getColumnHeaderWidth(JTable table, int column) {
        if (table == null || column < 0 || column >= table.getColumnCount()) {
            return MIN_COLUMN_WIDTH;
        }
        
        TableCellRenderer headerRenderer = table.getTableHeader().getDefaultRenderer();
        Object headerValue = table.getColumnModel().getColumn(column).getHeaderValue();
        Component headerComp = headerRenderer.getTableCellRendererComponent(
            table, headerValue, false, false, 0, column);
        return headerComp.getPreferredSize().width;
    }
    
    public static void adjustColumnWidths(JTable table) {
        if (table.getColumnCount() == 0 || table.getModel() == null) {
            return;
        }
    
        for (int i = 0; i < table.getColumnCount(); i++) {
            int headerWidth = getColumnHeaderWidth(table, i);
            int contentWidth = getMaxContentWidth(table, i);
            int columnWidth = Math.max(Math.max(headerWidth, contentWidth) + PADDING, MIN_COLUMN_WIDTH);
            
            table.getColumnModel().getColumn(i).setPreferredWidth(columnWidth);
            table.getColumnModel().getColumn(i).setMinWidth(MIN_COLUMN_WIDTH);
        }
    }

    /**
     * 调整指定列的宽度以适应内容
     * @param table 要调整的表格
     * @param column 要调整的列索引
     */
    public static void adjustColumnWidth(JTable table, int column) {
        if (table == null || column < 0 || column >= table.getColumnCount()) return;

        SwingUtilities.invokeLater(() -> {
            int headerWidth = getColumnHeaderWidth(table, column);
            int contentWidth = getMaxContentWidth(table, column);
            int preferredWidth = Math.max(Math.max(headerWidth, contentWidth) + PADDING, MIN_COLUMN_WIDTH);
            
            TableColumn tableColumn = table.getColumnModel().getColumn(column);
            tableColumn.setPreferredWidth(preferredWidth);
            tableColumn.setWidth(preferredWidth);
            
            // 强制更新UI
            updateTableUI(table);
        });
    }

    /**
     * 强制更新表格UI
     */
    private static void updateTableUI(JTable table) {
        if (table.getParent() instanceof JViewport) {
            JViewport viewport = (JViewport) table.getParent();
            if (viewport.getParent() instanceof JScrollPane) {
                JScrollPane scrollPane = (JScrollPane) viewport.getParent();
                scrollPane.revalidate();
                scrollPane.repaint();
            }
        }
        table.revalidate();
        table.repaint();
    }
} 