package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.GradientPaint;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.Action;
import javax.swing.BorderFactory;
import javax.swing.ButtonModel;
import javax.swing.Icon;
import javax.swing.JRadioButton;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义模式单选按钮，具有简单凸起效果
 */
public class CTModeRadioButton extends JRadioButton {

    private static final long serialVersionUID = 1L;
    
    public enum ModeType {
        NORMAL, TEST
    }
    
    private boolean hover = false;
    private final ModeType modeType;
    private static final Color NORMAL_COLOR = new Color(0, 180, 0);     // 绿色
    private static final Color TEST_COLOR = new Color(220, 0, 0);       // 红色
    private static final Color TEXT_COLOR = Color.WHITE;                // 文本颜色
    private static final Color HOVER_BACKGROUND = new Color(90, 100, 140, 80); // 更浅的悬停背景
    
    public CTModeRadioButton(String text, ModeType type, boolean selected) {
        super(text, selected);
        this.modeType = type;
        setup();
    }

    public CTModeRadioButton(String text, ModeType type) {
        super(text);
        this.modeType = type;
        setup();
    }

    public CTModeRadioButton(Action action, ModeType type) {
        super(action);
        this.modeType = type;
        setup();
    }

    public CTModeRadioButton(Icon icon, ModeType type, boolean selected) {
        super(icon, selected);
        this.modeType = type;
        setup();
    }

    public CTModeRadioButton(Icon icon, ModeType type) {
        super(icon);
        this.modeType = type;
        setup();
    }

    public CTModeRadioButton(String text, Icon icon, ModeType type, boolean selected) {
        super(text, icon, selected);
        this.modeType = type;
        setup();
    }

    private void setup() {
        setOpaque(false);
        setForeground(TEXT_COLOR);
        // 增大字体
        setFont(UIConstants.LARGE_FONT.deriveFont(Font.BOLD, 16f));
        // 增大尺寸
        setPreferredSize(new Dimension(130, 35));
        
        // 增大边距，让组件更明显
        setBorder(BorderFactory.createEmptyBorder(6, 12, 6, 12));
        
        // 鼠标监听器处理悬停效果
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                hover = true;
                repaint();
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                hover = false;
                repaint();
            }
        });
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        int width = getWidth();
        int height = getHeight();
        
        // 绘制背景 - 如果悬停或选中则显示背景
        if (hover || isSelected()) {
            // 绘制稍微圆角的背景
            g2.setColor(HOVER_BACKGROUND);
            g2.fillRoundRect(0, 0, width, height, 8, 8);
        }
        
        // 获取按钮模型
        ButtonModel model = getModel();
        
        // 绘制单选按钮圆圈
        int buttonSize = 16; // 按钮尺寸
        int x = 10;
        int y = height / 2 - buttonSize / 2;
        
        // 绘制外圈
        g2.setColor(Color.WHITE);
        g2.fillOval(x, y, buttonSize, buttonSize);
        
        // 绘制内部圆点
        if (model.isSelected()) {
            // 选中状态 - 绘制渐变色圆点
            Color dotColor = (modeType == ModeType.NORMAL) ? NORMAL_COLOR : TEST_COLOR;
            
            // 绘制基本圆点
            GradientPaint gradient = new GradientPaint(
                x + 3, y + 3, dotColor, 
                x + buttonSize - 6, y + buttonSize - 6, 
                dotColor.darker()
            );
            
            g2.setPaint(gradient);
            g2.fillOval(x + 3, y + 3, buttonSize - 6, buttonSize - 6);
        }
        
        // 绘制文本
        int textX = x + buttonSize + 8;
        int textY = (height + g2.getFontMetrics().getAscent() - g2.getFontMetrics().getDescent()) / 2;
        
        g2.setColor(TEXT_COLOR);
        g2.setFont(getFont());
        g2.drawString(getText(), textX, textY);
        
        g2.dispose();
    }
} 