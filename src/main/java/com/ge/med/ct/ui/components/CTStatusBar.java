package com.ge.med.ct.ui.components;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.SwingConstants;
import javax.swing.Timer;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义状态栏，实现CT工作站风格的UI
 */
public class CTStatusBar extends JPanel {
    private static final long serialVersionUID = 1L;
    
    // 组件尺寸
    private static final int HEIGHT = 28;
    
    private JPanel rightPanel;
    
    // 状态元素
    private JLabel statusLabel;
    private JLabel timeLabel;
    private JLabel versionLabel;
    
    // 定时器
    private Timer timer;
    
    /**
     * 创建一个状态栏
     */
    public CTStatusBar() {
        setLayout(new BorderLayout());
        setBackground(UIConstants.TOOLBAR_BACKGROUND);
        setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, UIConstants.BORDER_COLOR));
        setPreferredSize(new Dimension(getWidth(), HEIGHT));
        
        // 状态标签
        statusLabel = new JLabel("就绪");
        statusLabel.setForeground(Color.WHITE);
        statusLabel.setBorder(BorderFactory.createEmptyBorder(2, 10, 2, 10));
        statusLabel.setFont(UIConstants.SMALL_FONT);
        
        // 时间标签
        timeLabel = new JLabel();
        timeLabel.setForeground(Color.WHITE);
        timeLabel.setHorizontalAlignment(SwingConstants.RIGHT);
        timeLabel.setBorder(BorderFactory.createEmptyBorder(2, 10, 2, 10));
        timeLabel.setFont(UIConstants.SMALL_FONT);
        updateTime();
        
        // 版本标签
        versionLabel = new JLabel(UIConstants.APP_VERSION);
        versionLabel.setForeground(Color.WHITE);
        versionLabel.setBorder(BorderFactory.createEmptyBorder(2, 10, 2, 10));
        versionLabel.setFont(UIConstants.SMALL_FONT);
        
        // 右侧面板
        rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
        rightPanel.setOpaque(false);
        rightPanel.add(versionLabel);
        rightPanel.add(timeLabel);
        
        // 添加面板到状态栏
        add(statusLabel, BorderLayout.WEST);
        add(rightPanel, BorderLayout.EAST);
        
        // 启动计时器更新时间
        timer = new Timer(1000, e -> updateTime());
        timer.start();
    }
    
    /**
     * 更新时间显示
     */
    private void updateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        timeLabel.setText(sdf.format(new Date()));
    }
    
    /**
     * 设置状态栏文本
     */
    public void setStatus(String status) {
        statusLabel.setText(status);
    }
    
    /**
     * 获取状态标签
     * 
     * @return 状态标签
     */
    public JLabel getStatusLabel() {
        return statusLabel;
    }
    
    /**
     * 获取时间标签
     * 
     * @return 时间标签
     */
    public JLabel getTimeLabel() {
        return timeLabel;
    }
    
    /**
     * 释放资源
     */
    public void dispose() {
        if (timer != null) {
            timer.stop();
            timer = null;
        }
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g.create();
        
        // 绘制顶部分隔线
        g2d.setColor(UIConstants.BORDER_COLOR.brighter());
        g2d.drawLine(0, 0, getWidth(), 0);
        
        g2d.dispose();
    }
}