package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Insets;
import java.awt.RenderingHints;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;

import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义文本输入框组件，提供现代化的外观和交互效果
 */
public class CTTextField extends JTextField {
    private static final long serialVersionUID = 1L;
    
    private boolean isFocused = false;
    private String placeholder = null;
    private Color placeholderColor = UIConstants.TEXT_LIGHT_COLOR;
    private int borderRadius = UIConstants.DEFAULT_BORDER_RADIUS;
    private Color borderColor = UIConstants.BORDER_COLOR;
    private Color focusedBorderColor = UIConstants.PRIMARY_COLOR;
    
    /**
     * 创建默认文本框
     */
    public CTTextField() {
        this("");
    }
    
    /**
     * 创建带初始文本的文本框
     * 
     * @param text 初始文本
     */
    public CTTextField(String text) {
        super(text);
        configureTextField();
    }
    
    /**
     * 创建指定列数的文本框
     * 
     * @param columns 列数
     */
    public CTTextField(int columns) {
        super(columns);
        configureTextField();
    }
    
    /**
     * 创建带初始文本和指定列数的文本框
     * 
     * @param text 初始文本
     * @param columns 列数
     */
    public CTTextField(String text, int columns) {
        super(text, columns);
        configureTextField();
    }
    
    /**
     * 配置文本框的基本属性
     */
    private void configureTextField() {
        setOpaque(false);
        setBorder(new EmptyBorder(3, 5, 3, 5));
        setFont(UIConstants.NORMAL_FONT);
        setForeground(UIConstants.TEXT_COLOR);
        setBackground(UIConstants.FIELD_BACKGROUND);
        // 设置默认尺寸
        setPreferredSize(new Dimension(150, UIConstants.DEFAULT_BUTTON_HEIGHT));
        
        // 添加焦点监听器
        addFocusListener(new FocusAdapter() {
            @Override
            public void focusGained(FocusEvent e) {
                isFocused = true;
                repaint();
            }
            
            @Override
            public void focusLost(FocusEvent e) {
                isFocused = false;
                repaint();
            }
        });
    }
    
    /**
     * 设置占位文本
     * 
     * @param placeholder 占位文本
     */
    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
        repaint();
    }
    
    /**
     * 获取占位文本
     * 
     * @return 占位文本
     */
    public String getPlaceholder() {
        return placeholder;
    }
    
    /**
     * 设置占位文本颜色
     * 
     * @param color 颜色
     */
    public void setPlaceholderColor(Color color) {
        this.placeholderColor = color;
        repaint();
    }
    
    /**
     * 设置边框颜色
     * 
     * @param color 颜色
     */
    public void setBorderColor(Color color) {
        this.borderColor = color;
        repaint();
    }
    
    /**
     * 设置获焦点时的边框颜色
     * 
     * @param color 颜色
     */
    public void setFocusedBorderColor(Color color) {
        this.focusedBorderColor = color;
        repaint();
    }
    
    /**
     * 设置边框圆角半径
     * 
     * @param radius 圆角半径
     */
    public void setBorderRadius(int radius) {
        this.borderRadius = radius;
        repaint();
    }
    
    /**
     * 获取实际的内边距
     */
    @Override
    public Insets getInsets() {
        return getBorder().getBorderInsets(this);
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制背景
        g2.setColor(getBackground());
        g2.fillRoundRect(0, 0, getWidth(), getHeight(), borderRadius, borderRadius);
        
        // 绘制边框
        g2.setColor(isFocused ? focusedBorderColor : borderColor);
        g2.drawRoundRect(0, 0, getWidth() - 1, getHeight() - 1, borderRadius, borderRadius);
        
        // 调用父类方法绘制文本
        super.paintComponent(g2);
        
        // 绘制占位文本
        if (placeholder != null && getText().isEmpty() && !isFocused) {
            g2.setColor(placeholderColor);
            g2.setFont(getFont());
            Insets insets = getInsets();
            g2.drawString(placeholder, insets.left, getHeight() / 2 + g2.getFontMetrics().getAscent() / 2 - 2);
        }
        
        g2.dispose();
    }
} 