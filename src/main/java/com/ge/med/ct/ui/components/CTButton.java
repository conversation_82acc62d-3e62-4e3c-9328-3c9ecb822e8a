package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JButton;

import com.ge.med.ct.ui.utils.UIConstants;

import javax.swing.BorderFactory;

/**
 * 自定义按钮组件，提供现代化的外观和交互效果
 */
public class CTButton extends JButton {
    private static final long serialVersionUID = 1L;

    // 按钮状态
    private boolean isHovered = false;
    private boolean isPressed = false;
    
    private Color textColor = Color.WHITE;
    // 按钮类型
    public enum ButtonType {
        PRIMARY, SECONDARY, DANGER, SUCCESS
    }
    
    // 事件监听器列表
    private List<ActionListener> actionListeners = new ArrayList<>();

    /**
     * 创建一个默认的CT工作站风格按钮
     */
    public CTButton() {
        this("");
    }

    /**
     * 创建一个带文本的CT工作站风格按钮
     * @param text 按钮文本
     */
    public CTButton(String text) {
        super(text);
        setupButton();
    }

    private void setupButton() {
        setOpaque(false);
        setFocusPainted(false);
        setContentAreaFilled(false);
        setBorderPainted(false);
        setFont(UIConstants.DEFAULT_FONT);
        setForeground(UIConstants.TEXT_COLOR);
        
        // 设置边距
        setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createEmptyBorder(1, 1, 1, 1),
                BorderFactory.createEmptyBorder(4, 10, 4, 10)));
        
        // 添加鼠标事件监听器
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                isHovered = true;
                repaint();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                isHovered = false;
                repaint();
            }

            @Override
            public void mousePressed(MouseEvent e) {
                isPressed = true;
                repaint();
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                isPressed = false;
                repaint();
            }
        });
    }

    /**
     * 设置按钮类型，修改按钮样式
     *
     * @param type 按钮类型
     */
    public void setButtonType(ButtonType type) {
        switch (type) {
            case PRIMARY:
                textColor = Color.WHITE;
                break;
            case SECONDARY:
                new Color(240, 240, 240);
                new Color(220, 220, 220);
                textColor = UIConstants.TEXT_COLOR;
                break;
            case DANGER:
                new Color(198, 40, 40);
                textColor = Color.WHITE;
                break;
            case SUCCESS:
                new Color(46, 125, 50);
                textColor = Color.WHITE;
                break;
        }
        
        setForeground(textColor);
        repaint();
    }

    /**
     * 自定义绘制按钮
     */
    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 确定按钮颜色
        Color buttonColor;
        if (!isEnabled()) {
            buttonColor = new Color(UIConstants.BUTTON_BACKGROUND.getRed(),
                                    UIConstants.BUTTON_BACKGROUND.getGreen(),
                                    UIConstants.BUTTON_BACKGROUND.getBlue(), 150);
        } else if (isPressed) {
            buttonColor = UIConstants.BTN_SECONDARY_COLOR;
        } else if (isHovered) {
            buttonColor = UIConstants.PRIMARY_COLOR;
        } else {
            buttonColor = UIConstants.BUTTON_BACKGROUND;
        }
        
        // 画按钮背景
        g2.setColor(buttonColor);
        g2.fillRect(1, 1, getWidth() - 2, getHeight() - 2);
        
        // 画边框
        g2.setColor(UIConstants.BUTTON_BORDER);
        g2.drawRect(0, 0, getWidth() - 1, getHeight() - 1);
        
        // 绘制3D效果
        g2.setColor(isPressed ? UIConstants.BUTTON_BORDER.darker() : Color.WHITE);
        g2.drawLine(1, 1, getWidth() - 2, 1); // 顶部高光
        g2.drawLine(1, 1, 1, getHeight() - 2); // 左侧高光
        
        g2.setColor(isPressed ? Color.WHITE : UIConstants.BUTTON_BORDER.darker());
        g2.drawLine(getWidth() - 2, 1, getWidth() - 2, getHeight() - 2); // 右侧阴影
        g2.drawLine(1, getHeight() - 2, getWidth() - 2, getHeight() - 2); // 底部阴影
        
        g2.dispose();
        
        // 绘制文字
        super.paintComponent(g);
    }

    /**
     * 触发动作事件
     */
    protected void fireActionPerformed() {
        ActionEvent event = new ActionEvent(this, ActionEvent.ACTION_PERFORMED, getActionCommand());
        
        // 通知所有监听器
        for (ActionListener listener : actionListeners) {
            listener.actionPerformed(event);
        }
        
        // 调用父类方法
        super.fireActionPerformed(event);
    }

    /**
     * 添加动作监听器
     */
    @Override
    public void addActionListener(ActionListener listener) {
        super.addActionListener(listener);
        actionListeners.add(listener);
    }

    /**
     * 移除动作监听器
     */
    @Override
    public void removeActionListener(ActionListener listener) {
        super.removeActionListener(listener);
        actionListeners.remove(listener);
    }

    /**
     * 设置按钮背景颜色
     *
     * @param color 背景颜色
     */
    public void setBackgroundColor(Color color) {
        repaint();
    }

    /**
     * 设置按钮悬停颜色
     *
     * @param color 悬停颜色
     */
    public void setHoverColor(Color color) {
        repaint();
    }
    
    /**
     * 设置按钮圆角半径
     * 
     * @param radius 圆角半径
     */
    public void setCornerRadius(int radius) {
        repaint();
    }
    
    /**
     * 设置为小型按钮
     */
    public void setSmall() {
        setPreferredSize(UIConstants.SMALL_BUTTON_SIZE);
        setFont(UIConstants.SMALL_FONT);
    }
    
    /**
     * 设置为大型按钮
     */
    public void setLarge() {
        setPreferredSize(UIConstants.LARGE_BUTTON_SIZE);
        setFont(UIConstants.TITLE_FONT);
    }

    @Override
    public Dimension getPreferredSize() {
        Dimension size = super.getPreferredSize();
        return new Dimension(Math.max(size.width, 80), Math.max(size.height, 24));
    }
}