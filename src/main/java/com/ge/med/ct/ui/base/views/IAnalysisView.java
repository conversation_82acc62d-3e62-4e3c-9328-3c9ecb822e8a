package com.ge.med.ct.ui.base.views;

import com.ge.med.ct.dicom.util.DicomDataProvider;
import com.ge.med.ct.ui.base.listeners.AnalysisViewListener;

/**
 * Analysis视图接口，定义视图的所有操作
 */
public interface IAnalysisView {
    void setViewListener(AnalysisViewListener listener);
    
    // Input/Output path operations
    void setInputPath(String path);
    void setOutputPath(String path);
    void setProtocol(String protocol);
    String getInputPath();
    String getOutputPath();
    String getProtocol();
    
    // Debug panel operations
    void appendDebugText(String text);
    void clearDebugText();
    
    // Content management
    void clearContents();
    
    // Message display
    void showMessage(String message);
    void showError(String message);
    
    // Patient panel operations
    void updatePatientData(DicomDataProvider dicomManager);
} 