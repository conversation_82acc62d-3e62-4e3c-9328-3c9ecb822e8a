package com.ge.med.ct.ui.presenter;

import com.ge.med.ct.service.ImgLSInvoker;
import com.ge.med.ct.service.AnalysisStatus;
import com.ge.med.ct.ui.base.listeners.AnalysisViewListener;
import com.ge.med.ct.ui.base.views.IAnalysisView;
import com.ge.med.ct.ui.model.AnalysisModel;
import com.ge.med.ct.dicom.util.DicomDataProvider;

import javax.swing.SwingWorker;
import java.util.List;
import java.util.ArrayList;
import java.util.logging.Logger;
import java.io.File;

/**
 * Analysis模块的Presenter，负责协调Model和View
 */
public class AnalysisPresenter implements AnalysisViewListener {
    private static final Logger LOG = Logger.getLogger(AnalysisPresenter.class.getName());
    
    private final AnalysisModel model;
    private final IAnalysisView view;
    private ReportPresenter reportPresenter;
    
    public AnalysisPresenter(IAnalysisView view) {
        this.view = view;
        this.model = new AnalysisModel.Builder().build();
        initialize();
    }
    
    public void setReportPresenter(ReportPresenter reportPresenter) {
        this.reportPresenter = reportPresenter;
    }
    
    private void initialize() {
        // 设置视图监听器
        view.setViewListener(this);
        
        // 绑定模型属性变更，自动更新视图
        model.addPropertyChangeListener(e -> {
            switch (AnalysisModel.Property.valueOf(e.getPropertyName())) {
                case INPUT_PATH:
                    view.setInputPath((String)e.getNewValue());
                    break;
                case OUTPUT_PATH:
                    view.setOutputPath((String)e.getNewValue());
                    break;
                case PROTOCOL:
                    view.setProtocol((String)e.getNewValue());
                    break;
                default:
                    break;
            }
        });
    }
    
    @Override
    public void sendCommandArgs(String inputPath, String outputPath, String protocol) {
        // 只更新模型，视图会通过PropertyChangeListener自动更新
        model.setInputPath(inputPath);
        model.setOutputPath(outputPath);
        model.setProtocol(protocol);
    }
    
    @Override
    public void onAcceptClicked() {
        // 从视图获取当前值
        String input = view.getInputPath();
        String output = view.getOutputPath();
        String protocol = view.getProtocol();
        
        // 确保模型与视图同步
        sendCommandArgs(input, output, protocol);
        
        // 执行分析任务
        executeAnalysisTask(buildCommand(input, output, protocol));
    }
    
    private String buildCommand(String input, String output, String protocol) {
        return new StringBuilder()
            .append("IAUI")
            .append(" -text")
            .append(" -input \"").append(input).append("\"")
            .append(" -output \"").append(output).append("\"")
            .append(" -protocol \"").append(protocol).append("\"")
            .toString();
    }
    
    private List<String> getSeriesImagePaths() {
        List<String> imagePaths = new ArrayList<>();
        // 使用mock目录下的测试图片
        File mockImagesDir = new File("mock/images");
        if (mockImagesDir.exists() && mockImagesDir.isDirectory()) {
            File[] files = mockImagesDir.listFiles((dir, name) -> 
                name.toLowerCase().endsWith(".jpg") || 
                name.toLowerCase().endsWith(".png") || 
                name.toLowerCase().endsWith(".dcm"));
            if (files != null) {
                for (File file : files) {
                    imagePaths.add(file.getAbsolutePath());
                }
            }
        }
        return imagePaths;
    }
    
    private String getMockReportPath() {
        // 使用mock目录下的测试报告
        File mockReportsDir = new File("mock/reports");
        if (!mockReportsDir.exists()) {
            mockReportsDir.mkdirs();
        }
        return new File(mockReportsDir, "analysis_report.log").getAbsolutePath();
    }
    
    private void executeAnalysisTask(String command) {
        new SwingWorker<String, Void>() {
            @Override
            protected String doInBackground() throws Exception {
                view.appendDebugText("Executing command: " + command);
                return ImgLSInvoker.run(command);
            }
            
            @Override
            protected void done() {
                try {
                    String result = get();
                    view.appendDebugText(result);
                    
                    // 解析执行结果
                    AnalysisStatus status = parseAnalysisResult(result);
                    
                    // 如果设置了reportPresenter，添加检查项
                    if (reportPresenter != null) {
                        // 获取当前Series的图片路径列表
                        List<String> imagePaths = getSeriesImagePaths();
                        
                        // 构建检查项标题
                        String title = buildCheckItemTitle();
                        
                        // 使用mock报告路径
                        String reportPath = getMockReportPath();
                        
                        // 创建测试报告内容
                        try {
                            File reportFile = new File(reportPath);
                            if (!reportFile.exists()) {
                                java.io.FileWriter writer = new java.io.FileWriter(reportFile);
                                writer.write("Analysis Report\n");
                                writer.write("==============\n");
                                writer.write("Date: " + new java.util.Date() + "\n");
                                writer.write("Status: " + status + "\n");
                                writer.write("Images analyzed: " + imagePaths.size() + "\n");
                                writer.close();
                            }
                        } catch (Exception e) {
                            LOG.warning("Failed to create mock report: " + e.getMessage());
                        }
                        
                        // 添加检查项到Report视图
                        reportPresenter.onAddCheckItem(title, status, imagePaths, reportPath);
                        
                        // 更新视图状态
                        if (status == AnalysisStatus.PASS) {
                            view.showMessage("Analysis completed successfully: PASS");
                        } else {
                            view.showError("Analysis completed with issues: FAIL");
                        }
                    }
                    
                } catch (Exception e) {
                    LOG.severe("Analysis failed: " + e.getMessage());
                    view.showError("Analysis failed: " + e.getMessage());
                }
            }
        }.execute();
    }
    
    private AnalysisStatus parseAnalysisResult(String result) {
        if (result == null || result.isEmpty()) {
            return AnalysisStatus.FAIL;
        }
        // 转换为小写进行比较
        String lowerResult = result.toLowerCase();
        return (lowerResult.contains("pass") || lowerResult.contains("success") || lowerResult.contains("true")) 
               ? AnalysisStatus.PASS 
               : AnalysisStatus.FAIL;
    }
    
    private String extractSeriesIdFromPath(String path) {
        if (path == null || path.isEmpty()) {
            return null;
        }
        // 从路径中提取seriesId，格式为: /usr/g/sdc_image_pool/images/p{patientId}/e{examId}/s{seriesId}/i{imageId}.CTDC.1
        String[] parts = path.split("/");
        for (int i = 0; i < parts.length; i++) {
            if (parts[i].startsWith("s") && i + 1 < parts.length && parts[i + 1].startsWith("i")) {
                return parts[i].substring(1); // 去掉's'前缀
            }
        }
        return null;
    }
    
    private String buildCheckItemTitle() {
        // 使用简单的测试标题
        return String.format("Analysis Test %d", System.currentTimeMillis() % 1000);
    }
    
    @Override
    public void onShowMessage(String message) {
        LOG.info(message);
    }
    
    @Override
    public void onShowError(String message) {
        LOG.severe(message);
    }
    
    public void setDicomData(DicomDataProvider dicomManager) {
        // 更新模型
        model.setDicomManager(dicomManager);
        model.setHasData(dicomManager != null);
        
        // 更新视图
        view.updatePatientData(dicomManager);
    }
    
    public void updateContents() {
        if (model.getDicomManager() != null) {
            view.updatePatientData(model.getDicomManager());
        }
    }
    
    public void resetContents() {
        model.resetContents();
        view.clearContents();
    }
} 