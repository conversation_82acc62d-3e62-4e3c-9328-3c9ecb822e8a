package com.ge.med.ct.ui.utils;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.JTableHeader;
import java.awt.*;

public class FixTableStyle {
    // 颜色相关常量
    private static final Color HEADER_BACKGROUND = new Color(140, 152, 191);
    private static final Color HEADER_HIGHLIGHT = new Color(255, 255, 255, 100);
    private static final Color HEADER_SHADOW = new Color(73, 87, 131);
    private static final Color SELECTED_BACKGROUND = new Color(51, 51, 153);
    private static final Color ROW_BACKGROUND = new Color(240, 240, 255);
    private static final Color GRID_COLOR = new Color(200, 200, 220);

    // 尺寸相关常量
    private static final int HEADER_HEIGHT = 26;
    private static final int ROW_HEIGHT = 25;

    public static void setupTableStyle(JTable table) {
        if (table == null) {
            return;
        }
        
        // 设置表头样式
        JTableHeader header = table.getTableHeader();
        header.setBackground(HEADER_BACKGROUND);
        header.setForeground(Color.WHITE);
        header.setFont(UIConstants.TABLE_HEADER_FONT);
        header.setDefaultRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                JPanel headerPanel = new JPanel(new BorderLayout()) {
                    @Override
                    protected void paintComponent(Graphics g) {
                        Graphics2D g2 = (Graphics2D) g.create();
                        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                        // 绘制背景
                        g2.setColor(HEADER_BACKGROUND);
                        g2.fillRect(0, 0, getWidth(), getHeight());

                        // 绘制左上浮雕效果
                        g2.setColor(HEADER_HIGHLIGHT);
                        g2.drawLine(0, getHeight()-1, 0, 0);  // 左边
                        g2.drawLine(1, getHeight()-1, 1, 0);  // 左边
                        g2.drawLine(0, 0, getWidth()-1, 0);   // 上边

                        // 绘制右下阴影效果
                        g2.setColor(HEADER_SHADOW);
                        g2.drawLine(getWidth()-1, 0, getWidth()-1, getHeight()-1);  // 右边
                        g2.drawLine(0, getHeight()-1, getWidth()-1, getHeight()-1); // 下边

                        g2.dispose();
                        super.paintComponent(g);
                    }
                };
                

                headerPanel.setOpaque(false);
                JLabel label = new JLabel(value.toString(), SwingConstants.CENTER);
                label.setFont(UIConstants.TABLE_HEADER_FONT);
                label.setForeground(Color.WHITE);
                headerPanel.add(label, BorderLayout.CENTER);
                
                return headerPanel;
            }
        });
        header.setPreferredSize(new Dimension(header.getWidth(), HEADER_HEIGHT));
        
        table.setRowHeight(ROW_HEIGHT);
        table.setShowGrid(false);
        table.setGridColor(GRID_COLOR);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        table.setFont(UIConstants.TABLE_CONTENT_FONT);
        
        // 设置单元格渲染器
        DefaultTableCellRenderer renderer = new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                Color bgColor;
                if (isSelected) {
                    bgColor = SELECTED_BACKGROUND;
                    c.setForeground(Color.WHITE);
                } else {
                    bgColor = row % 2 == 0 ? Color.WHITE : ROW_BACKGROUND;
                    c.setForeground(Color.BLACK);
                }
                c.setBackground(bgColor);
                
                // 设置所有边框颜色与背景色相同
                if (column == 0) {
                    setHorizontalAlignment(LEFT);
                    setBorder(BorderFactory.createMatteBorder(1, 1, 1, 1, bgColor));
                } else {
                    setHorizontalAlignment(CENTER);
                    setBorder(BorderFactory.createMatteBorder(1, 1, 1, 1, bgColor));
                }
                
                return c;
            }
        };
        
        // 应用渲染器到所有列
        for (int i = 0; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setCellRenderer(renderer);
        }
        
        TableOperations.adjustColumnWidths(table);
        
        table.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
        }
}