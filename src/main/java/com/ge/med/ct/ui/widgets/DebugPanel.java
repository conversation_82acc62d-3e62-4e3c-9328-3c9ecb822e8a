package com.ge.med.ct.ui.widgets;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Insets;
import java.awt.Point;
import java.awt.Rectangle;
import java.awt.RenderingHints;

import javax.swing.BorderFactory;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.text.BadLocationException;
import javax.swing.text.DefaultHighlighter;

import com.ge.med.ct.dicom.util.DicomDataProvider;

public class DebugPanel extends JPanel {
    private final CustomTextArea debugTextArea;

    private static final Color BLUE_MARKER = new Color(51, 153, 255);
    private static final Color RED_MARKER = new Color(255, 77, 77);
    private static final Color CYAN_MARKER = new Color(102, 255, 178);

    public DebugPanel() {
        setLayout(new BorderLayout());
        setBorder(BorderFactory.createTitledBorder("Debug Output"));

        debugTextArea = new CustomTextArea();
        debugTextArea.setEditable(false);
        debugTextArea.setEnabled(true);
        debugTextArea.setFocusable(true);
        debugTextArea.setHighlighter(new DefaultHighlighter());

        JScrollPane scrollPane = new JScrollPane(debugTextArea);
        scrollPane.getVerticalScrollBar().setUnitIncrement(16);
        add(scrollPane, BorderLayout.CENTER);
    }

    public void appendText(String text) {
        debugTextArea.appendWithMarker(text + "\n");
        debugTextArea.setCaretPosition(debugTextArea.getDocument().getLength());
    }

    public void updateContents(DicomDataProvider provider) {
        debugTextArea.setText("");
        appendText("Debug Panel: Data provider updated to " + provider.getClass().getSimpleName());
    }

    /**
     * 清空面板内容
     */
    public void clearContents() {
        if (debugTextArea != null) {
            debugTextArea.setText("");
        }
    }

    public JTextArea getDebugTextArea() {
        return debugTextArea;
    }

    private class CustomTextArea extends JTextArea {
        private static final int MARKER_WIDTH = 10;
        private Font normalFont;

        public CustomTextArea() {
            super();
            setMargin(new Insets(0, MARKER_WIDTH + 20, 0, 0));
            normalFont = getFont();
        }

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            try {
                int startOffset = viewToModel(new Point(0, 0));
                int endOffset = viewToModel(new Point(0, getHeight()));
                int startLine = getLineOfOffset(startOffset);
                int endLine = getLineOfOffset(endOffset);

                for (int line = startLine; line <= endLine; line++) {
                    int lineStart = getLineStartOffset(line);
                    String lineText = getText(lineStart, getLineEndOffset(line) - lineStart);
                    Rectangle lineRect = modelToView(lineStart);

                    if (lineRect != null) {
                        // 绘制行号
                        g2d.setColor(Color.GRAY);
                        String lineNumber = String.format("%3d", line + 1);
                        g2d.drawString(lineNumber, MARKER_WIDTH, lineRect.y + lineRect.height - 5);

                        // 绘制标记
                        Color markerColor = getMarkerColor(lineText);
                        if (markerColor != null) {
                            g2d.setColor(markerColor);
                            g2d.fillRect(2, lineRect.y + 2, MARKER_WIDTH - 5, lineRect.height - 5);
                        }
                    }
                }
            } catch (BadLocationException e) {
                e.printStackTrace();
            }

            g2d.dispose();
        }

        public void appendWithMarker(String text) {
            append(text);
            repaint();
        }

        private Color getMarkerColor(String text) {
            String lowerText = text.toLowerCase();
            if (lowerText.contains("-text")) {
                return BLUE_MARKER;
            } else if (lowerText.contains("error") || lowerText.contains("err") || lowerText.contains("fail")) {
                return RED_MARKER;
            } else if (lowerText.contains("success") || lowerText.contains("pass") || lowerText.contains("true")) {
                return CYAN_MARKER;
            }
            return null;
        }
    }
}
