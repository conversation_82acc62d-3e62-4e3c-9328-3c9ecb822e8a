package com.ge.med.ct.ui.widgets;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.util.List;

import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JTextField;

import com.ge.med.ct.dicom.model.DicomExam;
import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.model.DicomSeries;
import com.ge.med.ct.dicom.util.DicomDataProvider;
import com.ge.med.ct.ui.components.CTButton;
import com.ge.med.ct.ui.components.CTJScrollPane;
import com.ge.med.ct.ui.components.CTPanel;
import com.ge.med.ct.ui.components.CTPanel.PanelType;
import com.ge.med.ct.ui.utils.ClassAlias.GBC;
import com.ge.med.ct.ui.utils.FixTableStyle;
import com.ge.med.ct.ui.utils.TableViewDataManager;
import com.ge.med.ct.ui.components.CTTable;

public class PatientPanel extends JPanel {
    public interface SelectionChangeListener {
        void onSelectionChanged(String inputPath, String outputPath, String protocol);
    }

    private SelectionChangeListener selectionChangeListener;
    private CTTable examList;
    private CTTable seriesList;
    private CTTable imageList;

    private CTPanel searchPanel;

    private List<DicomExam> currentExams;
    private List<DicomSeries> currentSeries;
    
    private TableViewDataManager tableViewDataManager;
    private ImageInfoPanel imageInfoPanel;
    private DicomDataProvider dicomDataProvider;

    // 布局常量
    private static final Dimension LABEL_SIZE = new Dimension(80, 25);
    private static final Dimension FIELD_SIZE = new Dimension(150, 25);
    private static final Dimension BUTTON_SIZE = new Dimension(90, 35);
    private static final Insets DEFAULT_INSETS = new Insets(5, 5, 0, 5);

    public PatientPanel() {
        initialize();
    }

    private void initialize() {
        tableViewDataManager = TableViewDataManager.getInstance();
        imageInfoPanel = new ImageInfoPanel();
        
        setLayout(new BorderLayout());
        setLayout(new GridBagLayout());
        GBC gbc = new GBC();

        createSearchPanel(gbc);
        createDataGrids(gbc);
        createImageInfoPanel(gbc);
        applyTableStyles();
    }

    public void updateContents(DicomDataProvider provider) {
        this.dicomDataProvider = provider;
        if (provider != null) {
            if (imageInfoPanel != null) {
                imageInfoPanel.updateDicomProvider(provider);
            }
            refreshData();
        } else {
            clearContents();
        }
    }

    private void applyTableStyles() {
        FixTableStyle.setupTableStyle(examList);
        FixTableStyle.setupTableStyle(seriesList);
        FixTableStyle.setupTableStyle(imageList);
    }

    private void createSearchPanel(GBC gbc) {
        searchPanel = new CTPanel(new GridBagLayout());
        searchPanel.setPreferredSize(new Dimension(0, 120));
        searchPanel.setOpaque(false);

        GBC searchGbc = new GBC();
        searchGbc.insets = DEFAULT_INSETS;
        searchGbc.fill = GBC.HORIZONTAL;
        searchGbc.anchor = GBC.LINE_START;

        JLabel patientNameLabel = createLabel("患者姓名:");
        JTextField patientNameField = createTextField();
        JLabel examIdLabel = createLabel("检查ID:");
        JTextField examIdField = createTextField();
        CTButton queryButton = createQueryButton(patientNameField, examIdField);

        addToPanel(searchPanel, patientNameLabel, searchGbc, 0, 0, 0, 1);
        addToPanel(searchPanel, patientNameField, searchGbc, 1, 0, 1.0, 2);
        addToPanel(searchPanel, examIdLabel, searchGbc, 0, 1, 0, 1);
        addToPanel(searchPanel, examIdField, searchGbc, 1, 1, 1.0, 2);
        
        searchGbc.anchor = GBC.LINE_END;
        addToPanel(searchPanel, queryButton, searchGbc, 2, 2, 0, 1);

        addToPanel(searchPanel, gbc, 0, 0, 1.0, 0.03, GBC.BOTH, new Insets(0, 5, 0, 5));
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setPreferredSize(LABEL_SIZE);
        return label;
    }

    private JTextField createTextField() {
        JTextField field = new JTextField();
        field.setPreferredSize(FIELD_SIZE);
        return field;
    }

    private CTButton createQueryButton(JTextField patientNameField, JTextField examIdField) {
        CTButton button = new CTButton("查询");
        button.setPreferredSize(BUTTON_SIZE);
        button.addActionListener(e -> {
            String patientName = patientNameField.getText().trim();
            String examId = examIdField.getText().trim();

            if (patientName.isEmpty() && examId.isEmpty()) {
                refreshData();
            } else if (dicomDataProvider != null) {
                List<DicomExam> exams = dicomDataProvider.searchExaminations(patientName, examId);
                if (exams != null && !exams.isEmpty()) {
                    updateExamList(exams);
                }
            }
        });
        return button;
    }

    private void createDataGrids(GBC gbc) {
        examList = new CTTable(tableViewDataManager.getExamColumns());
        seriesList = new CTTable(tableViewDataManager.getSeriesColumns());
        imageList = new CTTable(tableViewDataManager.getImageColumns());

        CTPanel examPanel = createTablePanel("检查列表", examList);
        CTPanel seriesPanel = createTablePanel("序列", seriesList);
        CTPanel imagePanel = createTablePanel("图像", imageList);

        setupTableSelectionListeners();

        addToPanel(examPanel, gbc, 0, 1, 0.67, 0.3, GBC.BOTH, DEFAULT_INSETS);
        addToPanel(seriesPanel, gbc, 0, 2, 0.67, 0.3, GBC.BOTH, DEFAULT_INSETS);
        addToPanel(imagePanel, gbc, 0, 3, 0.67, 0.35, GBC.BOTH, DEFAULT_INSETS);
    }

    private CTPanel createTablePanel(String title, CTTable table) {
        CTPanel panel = new CTPanel(PanelType.HEADER);
        panel.setTitle(title);
        panel.setContent(new CTJScrollPane(table));
        return panel;
    }

    private void createImageInfoPanel(GBC gbc) {

        gbc.gridx = 1;
        gbc.gridy = 1;
        gbc.gridheight = 3;
        gbc.weightx = 0.33;
        gbc.weighty = 1.0;
        gbc.fill = GBC.BOTH;
        gbc.insets = new Insets(5, 0, 5, 0);
        add(imageInfoPanel, gbc);
        
        gbc.gridheight = 1;
        gbc.weightx = 0.0;
        gbc.weighty = 0.0;
    }

    private void addToPanel(JPanel panel, JComponent component, GBC gbc, 
            int gridx, int gridy, double weightx, int gridwidth) {
        gbc.gridx = gridx;
        gbc.gridy = gridy;
        gbc.weightx = weightx;
        gbc.gridwidth = gridwidth;
        panel.add(component, gbc);
    }

    private void addToPanel(JComponent component, GBC gbc,
            int gridx, int gridy, double weightx, double weighty, int fill, Insets insets) {
        gbc.gridx = gridx;
        gbc.gridy = gridy;
        gbc.weightx = weightx;
        gbc.weighty = weighty;
        gbc.fill = fill;
        gbc.insets = insets;
        gbc.anchor = GBC.CENTER;
        add(component, gbc);
    }

    public void refreshData() {
        try {
            clearAllLists();

            if (dicomDataProvider == null) {
                System.err.println("警告: DicomDataProvider 未初始化");
                return;
            }

            List<DicomExam> exams = null;
            try {
                exams = dicomDataProvider.getAllExams();
            } catch (Exception e) {
                System.err.println("加载检查记录失败: " + e.getMessage());
                e.printStackTrace();
            }

            if (exams != null && !exams.isEmpty()) {
                updateExamList(exams);
                if (examList.getRowCount() > 0) {
                    examList.setRowSelectionInterval(0, 0);
                }
            } else {
                System.err.println("未找到检查记录或记录为空");
            }

        } catch (Throwable t) {
            System.err.println("刷新数据时发生严重错误: " + t.getMessage());
            t.printStackTrace();
            clearAllLists();
        }
    }

    public void clearAllLists() {
        examList.clearItems();
        seriesList.clearItems();
        imageList.clearItems();
        currentExams = null;
        currentSeries = null;
    }

    private void setupTableSelectionListeners() {
        examList.getSelectionModel().addListSelectionListener(e -> {
            try {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = examList.getSelectedRow();
                    seriesList.clearItems();
                    imageList.clearItems();
                    currentSeries = null;

                    if (selectedRow >= 0 && currentExams != null && selectedRow < currentExams.size()) {
                        DicomExam selectedExam = currentExams.get(selectedRow);
                        if (selectedExam != null && dicomDataProvider != null) {
                            List<DicomSeries> series = dicomDataProvider.getSeriesForExam(selectedExam.getId());
                            updateSeriesList(series);
                            if (seriesList.getRowCount() > 0) {
                                seriesList.setRowSelectionInterval(0, 0);
                            }
                            sendCommandArgs();
                        }
                    }
                }
            } catch (Exception ex) {
                System.err.println("检查选择处理失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });

        seriesList.getSelectionModel().addListSelectionListener(e -> {
            try {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = seriesList.getSelectedRow();
                    imageList.clearItems();

                    if (selectedRow >= 0 && currentSeries != null && selectedRow < currentSeries.size()) {
                        DicomSeries selectedSeries = currentSeries.get(selectedRow);
                        if (selectedSeries != null && dicomDataProvider != null) {
                            List<DicomImage> images = dicomDataProvider.getImagesForSeries(selectedSeries.getId());
                            updateImageList(images);
                            if (imageList.getRowCount() > 0) {
                                imageList.setRowSelectionInterval(0, 0);
                            }
                            sendCommandArgs();
                        }
                    }
                }
            } catch (Exception ex) {
                System.err.println("序列选择处理失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });

        imageList.getSelectionModel().addListSelectionListener(e -> {
            try {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = imageList.getSelectedRow();
                    if (selectedRow >= 0 && currentSeries != null) {
                        DicomSeries selectedSeries = currentSeries.get(0);
                        if (selectedSeries != null && dicomDataProvider != null) {
                            List<DicomImage> images = dicomDataProvider.getImagesForSeries(selectedSeries.getId());
                            if (selectedRow < images.size()) {
                                DicomImage selectedImage = images.get(selectedRow);
                                imageInfoPanel.updateImageInfo(selectedImage);
                            }
                        }
                    }
                }
            } catch (Exception ex) {
                System.err.println("图像信息更新失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
    }

    private void updateExamList(List<DicomExam> exams) {
        if (exams != null) {
            currentExams = exams;
            examList.updateData(tableViewDataManager.convertExamData(exams));
        }
    }

    private void updateSeriesList(List<DicomSeries> series) {
        if (series != null) {
            currentSeries = series;
            seriesList.updateData(tableViewDataManager.convertSeriesData(series));
        }
    }

    private void updateImageList(List<DicomImage> images) {
        if (images != null) {
            imageList.updateData(tableViewDataManager.convertImageData(images));
        }
    }

    public CTTable getExamList() {
        return examList;
    }

    public CTTable getSeriesList() {
        return seriesList;
    }

    public CTTable getImageList() {
        return imageList;
    }

    public void clearContents() {
        if (examList != null) {
            examList.clearItems();
        }
        if (seriesList != null) {
            seriesList.clearItems();
        }
        if (imageList != null) {
            imageList.clearItems();
        }
        if (imageInfoPanel != null) {
            imageInfoPanel.clearContents();
        }
        currentExams = null;
        currentSeries = null;
        dicomDataProvider = null;
    }

    public void setSelectionChangeListener(SelectionChangeListener listener) {
        this.selectionChangeListener = listener;
    }

    private void notifySelectionChanged(String inputPath, String outputPath, String protocol) {
        if (selectionChangeListener != null) {
            selectionChangeListener.onSelectionChanged(inputPath, outputPath, protocol);
        }
    }

    private void sendCommandArgs() {
        try {
            int examRow = examList.getSelectedRow();
            int seriesRow = seriesList.getSelectedRow();

            if (examRow >= 0 && seriesRow >= 0 && currentExams != null && currentSeries != null) {
                DicomExam selectedExam = currentExams.get(examRow);
                DicomSeries selectedSeries = currentSeries.get(seriesRow);

                if (selectedExam != null && selectedSeries != null) {
                    // 获取当前Series的图像列表
                    List<DicomImage> images = dicomDataProvider.getImagesForSeries(selectedSeries.getId());
                    if (images != null && !images.isEmpty()) {
                        // 使用选中的图像（如果没有选中则使用第一个）
                        DicomImage selectedImage = images.get(0);

                        // 构建输入路径
                        String inputPath = String.format("/usr/g/sdc_image_pool/images/p%s/e%s/s%s/i%s.CTDC.1",
                                selectedExam.getPatientID(),
                                selectedExam.getId(),
                                selectedSeries.getId(),
                                selectedImage.getId());

                        // 构建输出路径
                        String outputPath = String.format("/usr/g/bin/rep_series%s.log",
                                selectedSeries.getId());

                        // 构建协议字符串 - 使用简单的序号作为QA编号
                        int qaNumber = (examRow % 5) + 1;
                        int protocolNumber = (examRow % 10) + 1;
                        String protocol = String.format("QA%d|ImgSer%sQA|%d",
                                qaNumber,
                                selectedSeries.getId(),
                                protocolNumber);

                        notifySelectionChanged(inputPath, outputPath, protocol);
                    }
                }
            }
        } catch (Exception ex) {
            System.err.println("更新路径和协议失败: " + ex.getMessage());
            ex.printStackTrace();
        }
    }
}