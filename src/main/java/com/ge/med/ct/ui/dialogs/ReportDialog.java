package com.ge.med.ct.ui.dialogs;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Frame;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

import javax.swing.BorderFactory;
import javax.swing.JDialog;
import javax.swing.JPanel;
import javax.swing.SwingUtilities;
import javax.swing.WindowConstants;

import com.ge.med.ct.ui.components.CTButton;
import com.ge.med.ct.ui.components.CTLabel;
import com.ge.med.ct.ui.utils.UIConstants;

public class ReportDialog extends JDialog {
    private static final long serialVersionUID = 1L;
    private static final int DEFAULT_WIDTH = 400;
    private static final int DEFAULT_HEIGHT = 300;
    
    private final JPanel contentPanel;
    private final JPanel buttonPanel;
    
    public ReportDialog(Frame parent, String title) {
        super(parent, title, true);
        
        // 基本设置
        setResizable(true);
        setDefaultCloseOperation(WindowConstants.DISPOSE_ON_CLOSE);
        setPreferredSize(new Dimension(DEFAULT_WIDTH, DEFAULT_HEIGHT));
        setLayout(new BorderLayout());
        
        // 添加窗口关闭监听器
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                dispose();
            }
        });
        
        // 创建内容面板
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        contentPanel.setBackground(Color.WHITE);
        add(contentPanel, BorderLayout.CENTER);
        
        // 创建按钮面板
        buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        buttonPanel.setBackground(UIConstants.BACKGROUND_COLOR);
        add(buttonPanel, BorderLayout.SOUTH);
        
        // 居中显示
        setLocationRelativeTo(parent);
    }
    
    public void setContent(Component component) {
        contentPanel.removeAll();
        contentPanel.add(component, BorderLayout.CENTER);
        contentPanel.revalidate();
        contentPanel.repaint();
    }
    
    public void addButton(CTButton button) {
        buttonPanel.add(button);
        buttonPanel.revalidate();
        buttonPanel.repaint();
    }
    
    public static void showMessage(Component parent, String title, String message) {
        Frame frame = (Frame) SwingUtilities.getWindowAncestor(parent);
        ReportDialog dialog = new ReportDialog(frame, title);
        
        // 创建消息面板
        JPanel messagePanel = new JPanel(new BorderLayout());
        messagePanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        messagePanel.setBackground(Color.WHITE);
        
        CTLabel messageLabel = new CTLabel(message);
        messagePanel.add(messageLabel, BorderLayout.CENTER);
        
        dialog.setContent(messagePanel);
        
        // 添加确定按钮
        CTButton okButton = new CTButton("确定");
        okButton.setPreferredSize(new Dimension(80, 30));
        okButton.addActionListener(e -> dialog.dispose());
        dialog.addButton(okButton);
        
        dialog.pack();
        dialog.setLocationRelativeTo(parent);
        dialog.setVisible(true);
    }
    
    public static void showConfirm(Component parent, String title, String message, Runnable onConfirm) {
        Frame frame = (Frame) SwingUtilities.getWindowAncestor(parent);
        ReportDialog dialog = new ReportDialog(frame, title);
        
        // 创建消息面板
        JPanel messagePanel = new JPanel(new BorderLayout());
        messagePanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        messagePanel.setBackground(Color.WHITE);
        
        CTLabel messageLabel = new CTLabel(message);
        messagePanel.add(messageLabel, BorderLayout.CENTER);
        
        dialog.setContent(messagePanel);
        
        // 添加确定按钮
        CTButton okButton = new CTButton("确定");
        okButton.setPreferredSize(new Dimension(80, 30));
        okButton.addActionListener(e -> {
            if (onConfirm != null) {
                onConfirm.run();
            }
            dialog.dispose();
        });
        dialog.addButton(okButton);
        
        // 添加取消按钮
        CTButton cancelButton = new CTButton("取消");
        cancelButton.setPreferredSize(new Dimension(80, 30));
        cancelButton.addActionListener(e -> dialog.dispose());
        dialog.addButton(cancelButton);
        
        dialog.pack();
        dialog.setLocationRelativeTo(parent);
        dialog.setVisible(true);
    }
} 