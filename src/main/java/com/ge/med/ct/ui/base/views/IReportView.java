package com.ge.med.ct.ui.base.views;

import java.util.List;
import com.ge.med.ct.service.AnalysisStatus;
import com.ge.med.ct.ui.base.listeners.ReportViewListener;
import com.ge.med.ct.ui.components.CTListView;

/**
 * Report视图接口，定义视图的所有操作
 */
public interface IReportView {
    void setViewListener(ReportViewListener listener);
    
    // Content operations
    void displayReport(String content);
    void clearContents();
    
    // Check items operations
    void addCheckItem(String title, AnalysisStatus status);
    void addCheckItem(String title, AnalysisStatus status, List<String> imagePaths, String reportFile);
    CTListView getCheckItemsListView();
    
    // Message display
    void showMessage(String message);
    void showError(String message);
    
    /**
     * 重置图片面板状态，包括清空图片和重置索引
     */
    void resetImagePanel();
    
    /**
     * 加载并显示图片列表
     * @param imagePaths 图片路径列表
     */
    void loadImages(List<String> imagePaths);
} 