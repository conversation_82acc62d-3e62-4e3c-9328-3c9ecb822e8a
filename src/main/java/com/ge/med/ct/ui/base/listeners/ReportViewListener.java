package com.ge.med.ct.ui.base.listeners;

import java.util.List;
import com.ge.med.ct.service.AnalysisStatus;
import com.ge.med.ct.ui.components.CTListView;

/**
 * Report视图事件监听器，定义视图事件的处理方法
 */
public interface ReportViewListener {
    void onItemSelected(CTListView.ListItem item);
    void onGeneratePdfReport();
    void onDisplayReport(String content);
    void onLoadImages(List<String> imagePaths);
    void onShowMessage(String message);
    void onShowError(String message);
    void onAddCheckItem(String title, AnalysisStatus status);
    void onAddCheckItem(String title, AnalysisStatus status, List<String> imagePaths, String reportFile);
} 