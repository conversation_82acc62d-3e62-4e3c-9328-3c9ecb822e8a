package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Component;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Insets;
import java.awt.Rectangle;
import java.awt.RenderingHints;

import javax.swing.Icon;
import javax.swing.JTabbedPane;
import javax.swing.UIManager;
import javax.swing.border.EmptyBorder;
import javax.swing.plaf.basic.BasicTabbedPaneUI;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义选项卡面板组件，提供现代化的外观和交互效果
 */
public class CTTabbedPane extends JTabbedPane {
    private static final long serialVersionUID = 1L;
    
    // 外观配置
    private Color selectedTabColor = UIConstants.PRIMARY_COLOR;
    private Color selectedTextColor = UIConstants.PRIMARY_COLOR;
    private Color unselectedTextColor = UIConstants.TEXT_COLOR;
    private Color tabAreaBackground = Color.WHITE;
    private Color contentAreaBackground = Color.WHITE;
    private int tabHeight = 36;
    private int tabInsets = UIConstants.DEFAULT_PADDING;
    private boolean showTabSeparators = false;
    private boolean useCustomUI = true;
    
    /**
     * 创建默认的选项卡面板
     */
    public CTTabbedPane() {
        configureTabPane();
    }
    
    /**
     * 创建指定位置的选项卡面板
     * 
     * @param tabPlacement 选项卡位置
     */
    public CTTabbedPane(int tabPlacement) {
        super(tabPlacement);
        configureTabPane();
    }
    
    /**
     * 配置选项卡面板的基本属性
     */
    private void configureTabPane() {
        setFont(UIConstants.NORMAL_FONT);
        setForeground(unselectedTextColor);
        setBackground(contentAreaBackground);
        
        // 设置内容区域边距
        setBorder(new EmptyBorder(1, 1, 1, 1));
        
        // 设置自定义UI
        if (useCustomUI) {
            setUI(new CTTabbedPaneUI());
        }
        
        // 设置选项卡放置方式为顶部
        setTabPlacement(JTabbedPane.TOP);
        
        // 设置一行显示所有选项卡
        setTabLayoutPolicy(JTabbedPane.SCROLL_TAB_LAYOUT);
    }
    
    /**
     * 设置选中选项卡颜色
     * 
     * @param color 颜色
     */
    public void setSelectedTabColor(Color color) {
        this.selectedTabColor = color;
        repaint();
    }
    
    /**
     * 设置选中文本颜色
     * 
     * @param color 颜色
     */
    public void setSelectedTextColor(Color color) {
        this.selectedTextColor = color;
        repaint();
    }
    
    /**
     * 设置未选中文本颜色
     * 
     * @param color 颜色
     */
    public void setUnselectedTextColor(Color color) {
        this.unselectedTextColor = color;
        repaint();
    }
    
    /**
     * 设置选项卡区域背景色
     * 
     * @param color 颜色
     */
    public void setTabAreaBackground(Color color) {
        this.tabAreaBackground = color;
        repaint();
    }
    
    /**
     * 设置内容区域背景色
     * 
     * @param color 颜色
     */
    public void setContentAreaBackground(Color color) {
        this.contentAreaBackground = color;
        setBackground(color);
        repaint();
    }
    
    /**
     * 设置选项卡高度
     * 
     * @param height 高度
     */
    public void setTabHeight(int height) {
        this.tabHeight = height;
        repaint();
    }
    
    /**
     * 设置选项卡内边距
     * 
     * @param insets 内边距
     */
    public void setTabInsets(int insets) {
        this.tabInsets = insets;
        repaint();
    }
    
    /**
     * 设置是否显示选项卡分隔线
     * 
     * @param show 是否显示
     */
    public void setShowTabSeparators(boolean show) {
        this.showTabSeparators = show;
        repaint();
    }
    
    /**
     * 设置是否使用自定义UI
     * 
     * @param use 是否使用
     */
    public void setUseCustomUI(boolean use) {
        this.useCustomUI = use;
        if (useCustomUI) {
            setUI(new CTTabbedPaneUI());
        } else {
            setUI(UIManager.getUI(this));
        }
    }
    
    /**
     * 自定义选项卡面板UI实现
     */
    class CTTabbedPaneUI extends BasicTabbedPaneUI {
        @Override
        protected void installDefaults() {
            super.installDefaults();
            this.tabInsets = new Insets(CTTabbedPane.this.tabInsets, 
                                        CTTabbedPane.this.tabInsets, 
                                        CTTabbedPane.this.tabInsets, 
                                        CTTabbedPane.this.tabInsets);
            selectedTabPadInsets = new Insets(0, 0, 0, 0);
            contentBorderInsets = new Insets(1, 1, 1, 1);
        }
        
        @Override
        protected int calculateTabHeight(int tabPlacement, int tabIndex, int fontHeight) {
            return tabHeight;
        }
        
        @Override
        protected void paintTabArea(Graphics g, int tabPlacement, int selectedIndex) {
            // 绘制选项卡区域背景
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            Rectangle tabAreaRect = new Rectangle(0, 0, tabPane.getWidth(), calculateTabAreaHeight(tabPlacement, runCount, maxTabHeight));
            g2.setColor(tabAreaBackground);
            g2.fillRect(tabAreaRect.x, tabAreaRect.y, tabAreaRect.width, tabAreaRect.height);
            
            // 绘制选项卡
            super.paintTabArea(g, tabPlacement, selectedIndex);
            
            g2.dispose();
        }
        
        @Override
        protected void paintTabBorder(Graphics g, int tabPlacement, int tabIndex, int x, int y, int w, int h, boolean isSelected) {
            if (isSelected) {
                // 选中选项卡下方绘制指示线
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                g2.setColor(selectedTabColor);
                int indicatorHeight = 3;
                g2.fillRect(x, y + h - indicatorHeight, w, indicatorHeight);
                
                g2.dispose();
            } else if (showTabSeparators && tabIndex < tabPane.getTabCount() - 1) {
                // 绘制选项卡分隔线
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setColor(UIConstants.BORDER_COLOR);
                g2.drawLine(x + w, y + 4, x + w, y + h - 4);
                g2.dispose();
            }
        }
        
        @Override
        protected void paintText(Graphics g, int tabPlacement, Font font, FontMetrics metrics, int tabIndex, String title, Rectangle textRect, boolean isSelected) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            g2.setFont(font);
            g2.setColor(isSelected ? selectedTextColor : unselectedTextColor);
            
            // 调整文本位置
            int textY = textRect.y + metrics.getAscent();
            g2.drawString(title, textRect.x, textY);
            
            g2.dispose();
        }
        
        @Override
        protected void paintFocusIndicator(Graphics g, int tabPlacement, Rectangle[] rects, int tabIndex, Rectangle iconRect, Rectangle textRect, boolean isSelected) {
            // 不绘制焦点指示器
        }
        
        @Override
        protected void paintContentBorder(Graphics g, int tabPlacement, int selectedIndex) {
            // 绘制内容区域边框
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            Insets insets = tabPane.getInsets();
            int x = insets.left;
            int y = insets.top;
            int w = tabPane.getWidth() - insets.left - insets.right;
            int h = tabPane.getHeight() - insets.top - insets.bottom;
            
            // 调整y位置到选项卡下方
            switch (tabPlacement) {
                case TOP:
                    y += calculateTabAreaHeight(tabPlacement, runCount, maxTabHeight);
                    h -= calculateTabAreaHeight(tabPlacement, runCount, maxTabHeight);
                    break;
                case BOTTOM:
                    h -= calculateTabAreaHeight(tabPlacement, runCount, maxTabHeight);
                    break;
                case LEFT:
                    x += calculateTabAreaWidth(tabPlacement, runCount, maxTabWidth);
                    w -= calculateTabAreaWidth(tabPlacement, runCount, maxTabWidth);
                    break;
                case RIGHT:
                    w -= calculateTabAreaWidth(tabPlacement, runCount, maxTabWidth);
                    break;
            }
            
            // 绘制内容区域背景
            g2.setColor(contentAreaBackground);
            g2.fillRect(x, y, w, h);
            
            // 绘制边框
            g2.setColor(UIConstants.BORDER_COLOR);
            g2.drawRect(x, y, w - 1, h - 1);
            
            g2.dispose();
        }
        
        @Override
        protected void paintTab(Graphics g, int tabPlacement, Rectangle[] rects, int tabIndex, Rectangle iconRect, Rectangle textRect) {
            super.paintTab(g, tabPlacement, rects, tabIndex, iconRect, textRect);
        }
        
        @Override
        protected void layoutLabel(int tabPlacement, FontMetrics metrics, int tabIndex, String title, Icon icon, Rectangle tabRect, Rectangle iconRect, Rectangle textRect, boolean isSelected) {
            super.layoutLabel(tabPlacement, metrics, tabIndex, title, icon, tabRect, iconRect, textRect, isSelected);
        }
    }
    
    /**
     * 添加选项卡
     * 
     * @param title 选项卡标题
     * @param component 关联组件
     */
    @Override
    public void addTab(String title, Component component) {
        super.addTab(title, component);
    }
    
    /**
     * 添加带图标的选项卡
     * 
     * @param title 选项卡标题
     * @param icon 选项卡图标
     * @param component 关联组件
     */
    @Override
    public void addTab(String title, Icon icon, Component component) {
        super.addTab(title, icon, component);
    }
    
    /**
     * 添加选项卡并返回索引
     * 
     * @param title 选项卡标题
     * @param component 关联组件
     * @return 选项卡索引
     */
    public int addTabWithIndex(String title, Component component) {
        addTab(title, component);
        return getTabCount() - 1;
    }
    
    /**
     * 添加带图标的选项卡并返回索引
     * 
     * @param title 选项卡标题
     * @param icon 选项卡图标
     * @param component 关联组件
     * @return 选项卡索引
     */
    public int addTabWithIndex(String title, Icon icon, Component component) {
        addTab(title, icon, component);
        return getTabCount() - 1;
    }
} 