package com.ge.med.ct.ui.views;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.net.URL;
import java.util.List;
import java.util.logging.Logger;

import javax.swing.*;
import javax.swing.border.EmptyBorder;

import com.ge.med.ct.service.AnalysisStatus;
import com.ge.med.ct.ui.base.listeners.ReportViewListener;
import com.ge.med.ct.ui.base.views.BaseView;
import com.ge.med.ct.ui.base.views.IReportView;
import com.ge.med.ct.ui.components.*;
import com.ge.med.ct.ui.components.CTPanel.PanelType;
import com.ge.med.ct.ui.dialogs.ReportDialog;
import com.ge.med.ct.ui.widgets.ImagePanel;
import com.ge.med.ct.ui.utils.ReportFormatter;

public class ReportResultView extends BaseView implements IReportView {
    private static final Logger LOGGER = Logger.getLogger(ReportResultView.class.getName());
    private static final double LEFT_RIGHT_RATIO = 2.0 / 7.0;
    private static final String PASS_ICON_PATH = "/assets/icons/pass.png";
    private static final String FAIL_ICON_PATH = "/assets/icons/fail.png";

    private ImageIcon passIcon;
    private ImageIcon failIcon;

    private JTextArea repTextArea;
    private CTPanel reportPanel;
    private CTPanel paperPanel;
    private CTListView checkedListView;
    private ImagePanel imagePanel;
    private ReportViewListener viewListener;

    public ReportResultView() {
        super(JSplitPane.HORIZONTAL_SPLIT);
        
        loadIcons();
        setupLayout();
        configureComponents();
        
        setDividerSize(5);
        setResizeWeight(LEFT_RIGHT_RATIO);
        setContinuousLayout(true);
        
        addComponentListener(new ComponentAdapter() {
            @Override
            public void componentResized(ComponentEvent e) {
                updateLayout();
            }
        });
    }

    private void setupLayout() {
        initializeComponents();
        JPanel leftPanel = createLeftPanel();
        reportPanel.getContentPanel().add(paperPanel, BorderLayout.CENTER);
        setLeftComponent(leftPanel);
        setRightComponent(reportPanel);
    }
    
    private void initializeComponents() {
        imagePanel = new ImagePanel("analysis images");
        imagePanel.setMinimumSize(new Dimension(200, 150));

        repTextArea = new JTextArea();
        repTextArea.setEditable(false);
        repTextArea.setLineWrap(false);
        repTextArea.setWrapStyleWord(true);
        repTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 16));
        repTextArea.setBorder(BorderFactory.createEmptyBorder(5, 20, 5, 20));

        reportPanel = new CTPanel(PanelType.HEADER);
        reportPanel.setTitle("报告详情");
        reportPanel.getContentPanel().setLayout(new BorderLayout());
        reportPanel.setPadding(0);
        
        JScrollPane scrollPane = new JScrollPane(repTextArea);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        
        paperPanel = new CTPanel(PanelType.PAPER);
        paperPanel.setBorderWidth(0);
        paperPanel.setHasShadow(false);
        paperPanel.getContentPanel().add(scrollPane, BorderLayout.CENTER);

        checkedListView = new CTListView("check items");
        checkedListView.setBorder(null);
    }
    
    private JPanel createLeftPanel() {
        JPanel leftPanel = new JPanel() {
            @Override
            public void doLayout() {
                layoutLeftPanel(this);
            }
        };
        leftPanel.setLayout(null);
        leftPanel.setBorder(new EmptyBorder(5, 5, 10, 5));
        leftPanel.add(checkedListView);
        leftPanel.add(imagePanel);
        return leftPanel;
    }

    private void layoutLeftPanel(JPanel panel) {
        if (panel.getComponentCount() < 2) {
            return;
        }

        int availableHeight = panel.getHeight();
        int availableWidth = panel.getWidth();
        int gap = 5;

        int totalParts = 3;
        int topHeight = (availableHeight - gap) / totalParts;
        int bottomHeight = (availableHeight - gap) * 2 / totalParts;

        panel.getComponent(0).setBounds(0, 0, availableWidth, topHeight);
        panel.getComponent(1).setBounds(0, topHeight + gap, availableWidth, bottomHeight);
    }

    private void configureComponents() {
        checkedListView.setDefaultIcon(passIcon);
        checkedListView.setFailIcon(failIcon);
        checkedListView.setOnItemSelected(item -> {
            if (viewListener != null) {
                viewListener.onItemSelected(item);
            }
        });
    }

    private void updateLayout() {
        int width = getWidth();
        setDividerLocation((int) (width * LEFT_RIGHT_RATIO));
        if (getLeftComponent() instanceof JPanel) {
            ((JPanel) getLeftComponent()).doLayout();
        }
    }

    @Override
    public void displayReport(String content) {
        if (content == null || content.isEmpty()) {
            repTextArea.setText("");
            return;
        }
        
        String fcontents = ReportFormatter.format(content);
        repTextArea.setText(fcontents);
        repTextArea.setCaretPosition(0);
    }

    private void loadIcons() {
        try {
            URL passIconUrl = ReportResultView.class.getResource(PASS_ICON_PATH);
            if (passIconUrl != null) {
                passIcon = new ImageIcon(passIconUrl);
            }
            URL failIconUrl = ReportResultView.class.getResource(FAIL_ICON_PATH);
            if (failIconUrl != null) {
                failIcon = new ImageIcon(failIconUrl);
            }
        } catch (Exception e) {
            LOGGER.warning("加载图标时出错: " + e.getMessage());
        }
    }

    @Override
    public void addNotify() {
        super.addNotify();
        SwingUtilities.invokeLater(() -> {
            int totalWidth = getWidth();
            setDividerLocation((int) (totalWidth * LEFT_RIGHT_RATIO));
        });
    }

    @Override
    public void setViewListener(ReportViewListener listener) {
        this.viewListener = listener;
    }

    @Override
    public void loadImages(List<String> imagePaths) {
        if (imagePaths == null || imagePaths.isEmpty()) {
            imagePanel.clearImages();
            return;
        }
        
        if (imagePanel.hasImages() && imagePanel.hasSameImagePaths(imagePaths)) {
            return;
        }
        
        SwingUtilities.invokeLater(() -> imagePanel.loadImages(imagePaths));
    }

    @Override
    public void showMessage(String message) {
        if (viewListener != null) {
            viewListener.onShowMessage(message);
        } else {
            ReportDialog.showMessage(this, "提示", message);
        }
    }

    @Override
    public void showError(String message) {
        if (viewListener != null) {
            viewListener.onShowError(message);
        } else {
            ReportDialog.showMessage(this, "错误", message);
        }
    }

    @Override
    public void clearContents() {
        checkedListView.clearItems();
        repTextArea.setText("");
        imagePanel.clearImages();
    }

    @Override
    public void addCheckItem(String title, AnalysisStatus status) {
        checkedListView.addItem(getIconForStatus(status), title, status);
    }

    @Override
    public void addCheckItem(String title, AnalysisStatus status, List<String> imagePaths, String reportFile) {
        CTListView.ListItem item = new CTListView.ListItem(getIconForStatus(status), title, status, imagePaths, reportFile);
        checkedListView.addItem(item);
        
        if (checkedListView.getItemCount() == 1) {
            checkedListView.getList().setSelectedIndex(0);
        }
    }

    @Override
    public CTListView getCheckItemsListView() {
        return checkedListView;
    }

    private ImageIcon getIconForStatus(AnalysisStatus status) {
        if (status == null) {
            return null;
        }
        return status == AnalysisStatus.PASS ? passIcon : failIcon;
    }

    @Override
    public void resetImagePanel() {
        imagePanel.clearImages();
    }
}
