package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.JRadioButton;
import javax.swing.border.EmptyBorder;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义单选按钮组件，提供现代化的外观和交互效果
 */
public class CTRadioButton extends JRadioButton {
    private static final long serialVersionUID = 1L;
    
    private boolean isHovered = false;
    private int radioSize = 18;
    private int padding = UIConstants.DEFAULT_PADDING;
    
    // 颜色配置
    private Color uncheckedBorderColor = UIConstants.BORDER_COLOR;
    private Color uncheckedFillColor = Color.WHITE;
    private Color checkedFillColor = UIConstants.PRIMARY_COLOR;
    private Color dotColor = Color.WHITE;
    private Color hoverColor = new Color(230, 230, 230);
    private Color textColor = UIConstants.TEXT_COLOR;
    private Color disabledTextColor = UIConstants.TEXT_LIGHT_COLOR;
    
    /**
     * 创建默认单选按钮
     */
    public CTRadioButton() {
        configureRadioButton();
    }
    
    /**
     * 创建带文本的单选按钮
     * 
     * @param text 单选按钮文本
     */
    public CTRadioButton(String text) {
        super(text);
        configureRadioButton();
    }
    
    /**
     * 创建带文本和初始选中状态的单选按钮
     * 
     * @param text 单选按钮文本
     * @param selected 是否选中
     */
    public CTRadioButton(String text, boolean selected) {
        super(text, selected);
        configureRadioButton();
    }
    
    /**
     * 配置单选按钮基本属性
     */
    private void configureRadioButton() {
        setOpaque(false);
        setFocusPainted(false);
        setBorderPainted(false);
        setContentAreaFilled(false);
        setBorder(new EmptyBorder(padding, padding + radioSize + 4, padding, padding));
        setFont(UIConstants.NORMAL_FONT);
        setForeground(textColor);
        
        // 鼠标事件监听
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                if (isEnabled()) {
                    isHovered = true;
                    repaint();
                }
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                isHovered = false;
                repaint();
            }
            
            @Override
            public void mousePressed(MouseEvent e) {
                if (isEnabled()) {
                    repaint();
                }
            }
            
            @Override
            public void mouseReleased(MouseEvent e) {
                repaint();
            }
        });
    }
    
    /**
     * 设置单选按钮大小
     * 
     * @param size 大小
     */
    public void setRadioSize(int size) {
        this.radioSize = size;
        repaint();
    }
    
    /**
     * 设置未选中时的边框颜色
     * 
     * @param color 颜色
     */
    public void setUncheckedBorderColor(Color color) {
        this.uncheckedBorderColor = color;
        repaint();
    }
    
    /**
     * 设置未选中时的填充颜色
     * 
     * @param color 颜色
     */
    public void setUncheckedFillColor(Color color) {
        this.uncheckedFillColor = color;
        repaint();
    }
    
    /**
     * 设置选中时的填充颜色
     * 
     * @param color 颜色
     */
    public void setCheckedFillColor(Color color) {
        this.checkedFillColor = color;
        repaint();
    }
    
    /**
     * 设置选中点的颜色
     * 
     * @param color 颜色
     */
    public void setDotColor(Color color) {
        this.dotColor = color;
        repaint();
    }
    
    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        setForeground(enabled ? textColor : disabledTextColor);
        repaint();
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制单选按钮
        int x = padding;
        int y = (getHeight() - radioSize) / 2;
        
        // 绘制悬停效果
        if (isHovered && isEnabled()) {
            g2.setColor(hoverColor);
            g2.fillOval(x - 2, y - 2, radioSize + 4, radioSize + 4);
        }
        
        // 绘制按钮外圈
        if (isSelected()) {
            g2.setColor(isEnabled() ? checkedFillColor : checkedFillColor.darker());
            g2.fillOval(x, y, radioSize, radioSize);
        } else {
            g2.setColor(isEnabled() ? uncheckedFillColor : uncheckedFillColor.darker());
            g2.fillOval(x, y, radioSize, radioSize);
            g2.setColor(isEnabled() ? uncheckedBorderColor : uncheckedBorderColor.darker());
            g2.drawOval(x, y, radioSize - 1, radioSize - 1);
        }
        
        // 绘制选中标记
        if (isSelected()) {
            drawDot(g2, x, y);
        }
        
        // 绘制文本
        g2.setColor(getForeground());
        g2.setFont(getFont());
        String text = getText();
        if (text != null && !text.isEmpty()) {
            int textX = x + radioSize + 6;
            int textY = (getHeight() + g2.getFontMetrics().getAscent() - g2.getFontMetrics().getDescent()) / 2;
            g2.drawString(text, textX, textY);
        }
        
        g2.dispose();
    }
    
    /**
     * 绘制选中点
     */
    private void drawDot(Graphics2D g2, int x, int y) {
        g2.setColor(dotColor);
        int dotSize = radioSize / 2;
        int dotX = x + (radioSize - dotSize) / 2;
        int dotY = y + (radioSize - dotSize) / 2;
        g2.fillOval(dotX, dotY, dotSize, dotSize);
    }
    
    @Override
    public Dimension getPreferredSize() {
        Dimension size = super.getPreferredSize();
        return new Dimension(size.width, Math.max(size.height, radioSize + 2 * padding));
    }
} 