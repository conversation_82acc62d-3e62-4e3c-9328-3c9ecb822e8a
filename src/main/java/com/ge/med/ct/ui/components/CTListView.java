package com.ge.med.ct.ui.components;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.Point;
import java.awt.Rectangle;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import javax.swing.BorderFactory;
import javax.swing.DefaultListCellRenderer;
import javax.swing.DefaultListModel;
import javax.swing.ImageIcon;
import javax.swing.JList;
import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import com.ge.med.ct.service.AnalysisStatus;
import com.ge.med.ct.ui.utils.UIConstants;

import javax.swing.JLabel;
import javax.swing.ListCellRenderer;

/**
 * 一个完整的列表视图组件，用于显示带图标、标题和状态的项目。
 * 集成了原来的IconListItem、IconListRenderer和CTListBox功能。
 * 继承自CTPanel，可以显示或隐藏标题头部。
 */
public class CTListView extends CTPanel {
    private static final long serialVersionUID = 1L;
    
    /**
     * 表示列表中的一个项目
     */
    public static class ListItem implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private ImageIcon icon;
        private String title;
        private AnalysisStatus status;
        private List<String> imagePaths;
        private String logFile;
        
        /**
         * 创建新列表项
         */
        public ListItem(ImageIcon icon, String title, AnalysisStatus status, 
                        List<String> imagePaths, String logFile) {
            this.icon = icon;
            this.title = Objects.requireNonNull(title, "Title cannot be null");
            this.status = Objects.requireNonNull(status, "Status cannot be null");
            setImagePaths(imagePaths);
            this.logFile = logFile;
        }
        
        /**
         * 创建简单列表项
         */
        public static ListItem create(String title, AnalysisStatus status) {
            return new ListItem(null, title, status, null, null);
        }
        
        /**
         * 创建带图标的列表项
         */
        public static ListItem createWithIcon(ImageIcon icon, String title, AnalysisStatus status) {
            return new ListItem(icon, title, status, null, null);
        }
        
        public ImageIcon getIcon() { return icon; }
        
        public ListItem setIcon(ImageIcon icon) {
            this.icon = icon;
            return this;
        }
        
        public String getTitle() { return title; }
        
        public ListItem setTitle(String title) {
            this.title = Objects.requireNonNull(title, "Title cannot be null");
            return this;
        }
        
        public AnalysisStatus getStatus() { return status; }
        
        public ListItem setStatus(AnalysisStatus status) {
            this.status = Objects.requireNonNull(status, "Status cannot be null");
            return this;
        }
        
        public String getStatusString() { return status.toString(); }
        
        public List<String> getImagePaths() {
            return imagePaths != null ? Collections.unmodifiableList(imagePaths) : null;
        }
        
        public String getRepFile() { return logFile; }
        
        public ListItem setLogFile(String logFile) {
            this.logFile = logFile;
            return this;
        }
        
        public ListItem setImagePaths(List<String> imagePaths) {
            this.imagePaths = imagePaths != null ? new ArrayList<>(imagePaths) : null;
            return this;
        }
        
        public ListItem addImagePath(String imagePath) {
            if (imagePath == null) {
                return this;
            }
            
            if (imagePaths == null) {
                imagePaths = new ArrayList<>();
            }
            
            imagePaths.add(imagePath);
            return this;
        }
        
        public boolean hasImages() {
            return imagePaths != null && !imagePaths.isEmpty();
        }
        
        public boolean hasRepFile() {
            return logFile != null && !logFile.isEmpty();
        }
        
        @Override
        public String toString() {
            return title + " [" + status + "]" + 
                   (hasImages() ? " [" + imagePaths.size() + " images]" : "") +
                   (hasRepFile() ? " [log]" : "");
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            
            ListItem other = (ListItem) obj;
            return Objects.equals(title, other.title) &&
                   status == other.status &&
                   Objects.equals(logFile, other.logFile);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(title, status, logFile);
        }
    }
    
    /**
     * 列表项的渲染器
     */
    private class ItemRenderer implements ListCellRenderer<ListItem> {
        private final DefaultListCellRenderer defaultRenderer;
        
        public ItemRenderer() {
            defaultRenderer = new DefaultListCellRenderer();
        }
        
        @Override
        public Component getListCellRendererComponent(JList<? extends ListItem> list, ListItem value,
                int index, boolean isSelected, boolean cellHasFocus) {
            JLabel label = (JLabel) defaultRenderer.getListCellRendererComponent(list, value, index,
                    isSelected, cellHasFocus);
            
            if (value != null) {
                label.setIcon(value.getIcon());
                label.setText(value.getTitle());
                label.setFont(UIConstants.LIST_ITEM_FONT);
                
                if (isSelected) {
                    label.setBackground(UIConstants.LIST_SELECTED_ROW_COLOR);
                } else {
                    // 使用交替行颜色
                    if (index % 2 == 0) {
                        label.setBackground(UIConstants.LIST_HEADER_COLOR);
                    } else {
                        label.setBackground(UIConstants.LIST_ALTERNATE_ROW_COLOR);
                    }
                }
                
                label.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));
                label.setPreferredSize(new Dimension(280, 36));
            }
            
            return label;
        }
    }
    
    // CTListView的主要实现部分
    
    private final JList<ListItem> list;
    private final DefaultListModel<ListItem> listModel;
    private ImageIcon defaultIcon;
    private ImageIcon failIcon;
    private Consumer<ListItem> onItemSelected;
    private JScrollPane scrollPane;
    private boolean headerVisible = true;
    private final List<ItemSelectedListener> listeners;
    
    /**
     * 创建带标题的列表视图
     */
    public CTListView(String title) {
        super(PanelType.HEADER);
        setTitle(title);
        
        // 初始化模型和列表
        listModel = new DefaultListModel<>();
        list = new JList<>(listModel);
        list.setCellRenderer(new ItemRenderer());
        
        // 配置列表外观和行为
        configureList();
        
        // 创建滚动面板，并移除边框内边距
        scrollPane = new JScrollPane(list);
        scrollPane.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
        scrollPane.getVerticalScrollBar().setUI(new CTScrollBarUI());
        scrollPane.getHorizontalScrollBar().setUI(new CTScrollBarUI());
        
        // 添加滚动面板到内容区域
        getContentPanel().add(scrollPane, BorderLayout.CENTER);
        
        listeners = new ArrayList<>();
    }
    
    /**
     * 设置标题头部是否可见
     */
    public CTListView setHeaderVisible(boolean visible) {
        this.headerVisible = visible;
        
        // 直接通过父类的组件控制显示/隐藏
        if (getHeaderPanel() != null) {
            getHeaderPanel().setVisible(visible);
        }
        
        revalidate();
        repaint();
        return this;
    }
    
    /**
     * 检查标题头部是否可见
     */
    public boolean isHeaderVisible() {
        return headerVisible;
    }
    
    /**
     * 配置列表的外观和行为
     */
    private void configureList() {
        list.setFixedCellHeight(38);
        list.setVisibleRowCount(10);
        list.setOpaque(true);
        list.setBackground(UIConstants.LIST_BACKGROUND_COLOR);
        list.setForeground(UIConstants.TEXT_COLOR);
        list.setBorder(BorderFactory.createEmptyBorder());
        list.setSelectionBackground(UIConstants.LIST_SELECTED_ROW_COLOR);
        list.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // 设置字体
        list.setFont(UIConstants.LIST_ITEM_FONT);
        
        // 默认使用正常鼠标指针
        list.setCursor(Cursor.getPredefinedCursor(Cursor.DEFAULT_CURSOR));
        
        // 添加选择监听器
        list.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                ListItem selectedItem = list.getSelectedValue();
                if (selectedItem != null) {
                    notifyItemSelected(selectedItem);
                    if (onItemSelected != null) {
                        SwingUtilities.invokeLater(() -> onItemSelected.accept(selectedItem));
                    }
                }
            }
        });
        
        // 添加鼠标监听器
        list.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseExited(MouseEvent e) {
                list.setCursor(Cursor.getPredefinedCursor(Cursor.DEFAULT_CURSOR));
            }
            
            @Override
            public void mouseClicked(MouseEvent e) {
                // 只处理点击事件，不干扰滚动条
                handleMouseEvent(e);
            }
        });
        
        // 添加鼠标移动监听器，检测是否悬停在有效项目上
        list.addMouseMotionListener(new java.awt.event.MouseMotionAdapter() {
            @Override
            public void mouseMoved(MouseEvent e) {
                handleMouseEvent(e);
            }
        });
        
        // 添加键盘导航支持
        list.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    notifyItemSelected();
                }
            }
        });
    }
    
    // 抽取鼠标处理逻辑，避免干扰滚动操作
    private void handleMouseEvent(MouseEvent e) {
        // 获取鼠标位置对应的项索引
        int index = list.locationToIndex(e.getPoint());
        
        // 只有当鼠标在有效索引范围内时才可能显示手型
        if (index >= 0 && index < listModel.getSize()) {
            // 计算项的可见边界
            Rectangle cellBounds = list.getCellBounds(index, index);
            
            // 确保鼠标在项的可见边界内，而不是在滚动条上
            if (cellBounds != null && cellBounds.contains(e.getPoint())) {
                // 查询滚动条是否可见且鼠标是否在滚动条区域
                boolean isOverScrollBar = isMouseOverScrollbar(e.getPoint());
                
                // 只有当鼠标不在滚动条区域时才显示手型
                if (!isOverScrollBar) {
                    list.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
                    return;
                }
            }
        }
        
        // 所有其他情况都使用默认光标
        list.setCursor(Cursor.getPredefinedCursor(Cursor.DEFAULT_CURSOR));
    }
    
    // 检查鼠标是否在滚动条区域
    private boolean isMouseOverScrollbar(Point mousePoint) {
        if (scrollPane == null) return false;
        
        // 转换坐标系从list到scrollPane
        Point convertedPoint = SwingUtilities.convertPoint(list, mousePoint, scrollPane);
        
        // 检查是否在垂直滚动条区域
        if (scrollPane.getVerticalScrollBar().isVisible()) {
            Rectangle scrollBarBounds = scrollPane.getVerticalScrollBar().getBounds();
            if (scrollBarBounds.contains(convertedPoint)) {
                return true;
            }
        }
        
        // 检查是否在水平滚动条区域
        if (scrollPane.getHorizontalScrollBar().isVisible()) {
            Rectangle scrollBarBounds = scrollPane.getHorizontalScrollBar().getBounds();
            if (scrollBarBounds.contains(convertedPoint)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 设置默认图标
     */
    public CTListView setDefaultIcon(ImageIcon icon) {
        this.defaultIcon = icon;
        return this;
    }
    
    /**
     * 设置失败图标
     */
    public CTListView setFailIcon(ImageIcon icon) {
        this.failIcon = icon;
        return this;
    }
    
    /**
     * 设置项目选择回调
     */
    public CTListView setOnItemSelected(Consumer<ListItem> callback) {
        this.onItemSelected = callback;
        return this;
    }
    
    /**
     * 设置标题，重写父类方法并返回this对象
     */
    @Override
    public void setTitle(String title) {
        super.setTitle(title);
    }
    
    /**
     * 链式设置标题方法
     */
    public CTListView withTitle(String title) {
        setTitle(title);
        return this;
    }
    
    /**
     * 通知项目被选中
     */
    private void notifyItemSelected() {
        if (onItemSelected != null) {
            ListItem selectedItem = getSelectedItem();
            if (selectedItem != null) {
                SwingUtilities.invokeLater(() -> onItemSelected.accept(selectedItem));
            }
        }
    }
    
    /**
     * 添加带标题和状态的项目
     */
    public CTListView addItem(String title, AnalysisStatus status) {
        ListItem item = new ListItem(null, title, status, null, null);
        if (status == AnalysisStatus.PASS) {
            item.setIcon(defaultIcon);
        } else {
            item.setIcon(failIcon);
        }
        listModel.addElement(item);
        return this;
    }
    
    /**
     * 添加带图标、标题和状态的项目
     */
    public CTListView addItem(ImageIcon icon, String title, AnalysisStatus status) {
        listModel.addElement(new ListItem(icon, title, status, null, null));
        return this;
    }
    
    /**
     * 添加带标题、状态、图像路径和日志文件的项目
     */
    public CTListView addItem(String title, AnalysisStatus status, List<String> imagePaths, String logFile) {
        listModel.addElement(new ListItem(defaultIcon, title, status, imagePaths, logFile));
        return this;
    }
    
    /**
     * 添加自定义项目
     */
    public CTListView addItem(ListItem item) {
        if (item != null) {
            listModel.addElement(item);
        }
        return this;
    }
    
    /**
     * 一次性添加多个项目
     */
    public CTListView addItems(Collection<ListItem> items) {
        if (items != null) {
            for (ListItem item : items) {
                if (item != null) {
                    listModel.addElement(item);
                }
            }
        }
        return this;
    }
    
    /**
     * 清除所有项目
     */
    public CTListView clearItems() {
        listModel.clear();
        return this;
    }
    
    /**
     * 移除选中项目
     */
    public CTListView removeSelectedItem() {
        int selectedIndex = list.getSelectedIndex();
        if (selectedIndex >= 0) {
            listModel.remove(selectedIndex);
        }
        return this;
    }
    
    /**
     * 刷新数据并选择第一个项目（如果有）
     */
    public CTListView refreshData() {
        if (listModel.getSize() > 0) {
            list.setSelectedIndex(0);
            // 选择监听器会处理通知
        }
        return this;
    }
    
    /**
     * 获取当前选中的项目
     */
    public ListItem getSelectedItem() {
        int selectedIndex = list.getSelectedIndex();
        if (selectedIndex >= 0 && selectedIndex < listModel.getSize()) {
            return listModel.getElementAt(selectedIndex);
        }
        return null;
    }
    
    /**
     * 获取项目数量
     */
    public int getItemCount() {
        return listModel.getSize();
    }
    
    /**
     * 检查列表是否为空
     */
    public boolean isItemListEmpty() {
        return listModel.isEmpty();
    }
    
    /**
     * 获取底层列表模型
     */
    public DefaultListModel<ListItem> getListModel() {
        return listModel;
    }
    
    /**
     * 获取底层JList组件
     */
    public JList<ListItem> getList() {
        return list;
    }
    
    public void addOnItemSelectedListener(ItemSelectedListener listener) {
        listeners.add(listener);
    }
    
    private void notifyItemSelected(ListItem item) {
        for (ItemSelectedListener listener : listeners) {
            listener.onItemSelected(item);
        }
    }
    
    public interface ItemSelectedListener {
        void onItemSelected(ListItem item);
    }
}