package com.ge.med.ct.ui.presenter;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.ge.med.ct.service.AnalysisStatus;
import com.ge.med.ct.ui.base.listeners.ReportViewListener;
import com.ge.med.ct.ui.base.views.IReportView;
import com.ge.med.ct.ui.components.CTListView;
import com.ge.med.ct.ui.model.ReportModel;
import com.ge.med.ct.dicom.util.DicomDataProvider;
import javax.swing.SwingUtilities;

public class ReportPresenter implements ReportViewListener {
    private static final Logger LOGGER = Logger.getLogger(ReportPresenter.class.getName());
    
    private final ReportModel model;
    private final IReportView view;
    
    public ReportPresenter(IReportView view) {
        this.model = new ReportModel();
        this.view = view;
        initialize();
    }
    
    private void initialize() {
        view.setViewListener(this);
        
        // 绑定模型属性变更
        model.addPropertyChangeListener(e -> {
            switch (e.getPropertyName()) {
                case "reportContent":
                    view.displayReport((String)e.getNewValue());
                    break;
                case "imagePaths":
                    @SuppressWarnings("unchecked")
                    List<String> paths = (List<String>)e.getNewValue();
                    view.loadImages(paths);
                    break;
            }
        });
    }
    
    @Override
    public void onItemSelected(CTListView.ListItem item) {
        if (item == null) {
            LOGGER.warning("Attempted to select null item");
            return;
        }
        
        LOGGER.info("Loading item: " + item);
        model.setSelectedItem(item);
        model.setHasData(true);
        
        // 清空当前显示内容
        view.resetImagePanel();
        
        CompletableFuture.runAsync(() -> {
            try {
                // 加载图像
                if (item.hasImages()) {
                    List<String> imagePaths = item.getImagePaths();
                    if (imagePaths == null || imagePaths.isEmpty()) {
                        throw new IllegalStateException("图像路径列表为空或无效");
                    }
                    LOGGER.info("Loading " + imagePaths.size() + " images");
                    // 直接调用view的loadImages方法，不经过model
                    SwingUtilities.invokeLater(() -> {
                        view.loadImages(imagePaths);
                    });
                } else {
                    LOGGER.warning("No images associated with item");
                    SwingUtilities.invokeLater(() -> {
                        view.showMessage("该项目没有关联图像");
                    });
                }

                // 加载报告
                if (item.hasRepFile()) {
                    String repFile = item.getRepFile();
                    LOGGER.info("Loading report file: " + repFile);
                    String content = readRepFile(repFile);
                    SwingUtilities.invokeLater(() -> {
                        model.setReportContent(content);
                    });
                } else {
                    LOGGER.warning("No report file associated with item");
                    SwingUtilities.invokeLater(() -> {
                        view.showError("该项目没有关联的报告文件");
                        model.setReportContent("");
                    });
                }
            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "文件读取错误", e);
                SwingUtilities.invokeLater(() -> {
                    view.showError("文件读取失败: " + e.getMessage());
                    model.setHasData(false);
                });
            } catch (IllegalStateException e) {
                LOGGER.log(Level.SEVERE, "数据状态错误", e);
                SwingUtilities.invokeLater(() -> {
                    view.showError("数据状态错误: " + e.getMessage());
                    model.setHasData(false);
                });
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "加载数据时发生未知错误", e);
                SwingUtilities.invokeLater(() -> {
                    view.showError("加载数据失败: " + e.getMessage());
                    model.setHasData(false);
                });
            }
        });
    }
    
    @Override
    public void onGeneratePdfReport() {
        LOGGER.info("PDF report generation requested");
        // TODO: Implement PDF report generation
    }
    
    @Override
    public void onDisplayReport(String content) {
        if (content != null) {
            view.displayReport(content);
        }
    }
    
    @Override
    public void onLoadImages(List<String> imagePaths) {
        if (imagePaths != null && !imagePaths.isEmpty()) {
            model.setImagePaths(imagePaths);
        }
    }
    
    @Override
    public void onShowMessage(String message) {
        view.showMessage(message);
    }
    
    @Override
    public void onShowError(String message) {
        view.showError(message);
    }
    
    @Override
    public void onAddCheckItem(String title, AnalysisStatus status) {
        view.addCheckItem(title, status);
    }
    
    @Override
    public void onAddCheckItem(String title, AnalysisStatus status, List<String> imagePaths, String reportFile) {
        view.addCheckItem(title, status, imagePaths, reportFile);
    }
    
    private String readRepFile(String filePath) throws IOException {
        if (filePath == null || filePath.trim().isEmpty()) {
            LOGGER.severe("尝试读取空的文件路径");
            throw new IOException("无效的文件路径");
        }

        File file = new File(filePath);
        if (!file.exists()) {
            LOGGER.severe("文件不存在: " + filePath);
            throw new IOException("文件不存在: " + filePath);
        }
        if (!file.isFile()) {
            LOGGER.severe("路径不是一个文件: " + filePath);
            throw new IOException("路径不是一个文件: " + filePath);
        }
        if (!file.canRead()) {
            LOGGER.severe("文件无法读取: " + filePath);
            throw new IOException("文件无法读取，请检查权限: " + filePath);
        }

        try {
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "读取文件内容失败: " + filePath, e);
            throw new IOException("读取文件内容失败: " + e.getMessage(), e);
        }
    }
    
    public void setDicomData(DicomDataProvider dicomManager) {
        model.setDicomManager(dicomManager);
        model.setHasData(dicomManager != null);
    }
} 