package com.ge.med.ct.ui.model;

import com.ge.med.ct.dicom.util.DicomDataProvider;
import com.ge.med.ct.ui.base.BaseModel;

public class AnalysisModel extends BaseModel {
    public enum Property {
        INPUT_PATH,
        OUTPUT_PATH,
        PROTOCOL,
        HAS_DATA
    }
    
    private String inputPath = "";
    private String outputPath = "";
    private String protocol = "";
    private boolean hasData = false;
    private DicomDataProvider dicomManager;
    
    // 私有构造方法，强制使用Builder
    private AnalysisModel() {}
    
    public void setInputPath(String inputPath) {
        String oldValue = this.inputPath;
        this.inputPath = inputPath;
        firePropertyChange(Property.INPUT_PATH.name(), oldValue, inputPath);
    }
    
    public String getInputPath() {
        return inputPath;
    }
    
    public void setOutputPath(String outputPath) {
        String oldValue = this.outputPath;
        this.outputPath = outputPath;
        firePropertyChange(Property.OUTPUT_PATH.name(), oldValue, outputPath);
    }
    
    public String getOutputPath() {
        return outputPath;
    }
    
    public void setProtocol(String protocol) {
        String oldValue = this.protocol;
        this.protocol = protocol;
        firePropertyChange(Property.PROTOCOL.name(), oldValue, protocol);
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public void setHasData(boolean hasData) {
        boolean oldValue = this.hasData;
        this.hasData = hasData;
        firePropertyChange(Property.HAS_DATA.name(), oldValue, hasData);
    }
    
    public boolean hasData() {
        return hasData;
    }
    
    public void setDicomManager(DicomDataProvider dicomManager) {
        this.dicomManager = dicomManager;
    }
    
    public DicomDataProvider getDicomManager() {
        return dicomManager;
    }
    
    public void resetContents() {
        setInputPath("");
        setOutputPath("");
        setProtocol("");
        setHasData(false);
    }
    
    public static class Builder {
        private final AnalysisModel model;
        
        public Builder() {
            model = new AnalysisModel();
        }
        
        public Builder inputPath(String inputPath) {
            model.setInputPath(inputPath);
            return this;
        }
        
        public Builder outputPath(String outputPath) {
            model.setOutputPath(outputPath);
            return this;
        }
        
        public Builder protocol(String protocol) {
            model.setProtocol(protocol);
            return this;
        }
        
        public Builder dicomManager(DicomDataProvider dicomManager) {
            model.setDicomManager(dicomManager);
            return this;
        }
        
        public AnalysisModel build() {
            return model;
        }
    }
} 