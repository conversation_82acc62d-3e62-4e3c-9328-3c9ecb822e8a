package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

import javax.swing.JCheckBox;
import javax.swing.border.EmptyBorder;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义复选框组件，提供现代化的外观和交互效果
 */
public class CTCheckBox extends JCheckBox {
    private static final long serialVersionUID = 1L;

    private boolean isHovered = false;
    private int borderRadius = 3;
    private int checkBoxSize = 18;
    private int padding = UIConstants.DEFAULT_PADDING;
    
    // 颜色配置
    private Color uncheckedBorderColor = UIConstants.BORDER_COLOR;
    private Color uncheckedFillColor = Color.WHITE;
    private Color checkedFillColor = UIConstants.PRIMARY_COLOR;
    private Color checkMarkColor = Color.WHITE;
    private Color hoverColor = new Color(230, 230, 230);
    private Color textColor = UIConstants.TEXT_COLOR;
    private Color disabledTextColor = UIConstants.TEXT_LIGHT_COLOR;
    
    /**
     * 创建默认复选框
     */
    public CTCheckBox() {
        configureCheckBox();
    }
    
    /**
     * 创建带文本的复选框
     * 
     * @param text 复选框文本
     */
    public CTCheckBox(String text) {
        super(text);
        configureCheckBox();
    }
    
    /**
     * 创建带文本和初始选中状态的复选框
     * 
     * @param text 复选框文本
     * @param selected 是否选中
     */
    public CTCheckBox(String text, boolean selected) {
        super(text, selected);
        configureCheckBox();
    }
    
    /**
     * 配置复选框基本属性
     */
    private void configureCheckBox() {
        setOpaque(false);
        setFocusPainted(false);
        setBorderPainted(false);
        setContentAreaFilled(false);
        setBorder(new EmptyBorder(padding, padding + checkBoxSize + 4, padding, padding));
        setFont(UIConstants.NORMAL_FONT);
        setForeground(textColor);
        
        // 鼠标事件监听
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                if (isEnabled()) {
                    isHovered = true;
                    repaint();
                }
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                isHovered = false;
                repaint();
            }
            
            @Override
            public void mousePressed(MouseEvent e) {
                if (isEnabled()) {
                    repaint();
                }
            }
            
            @Override
            public void mouseReleased(MouseEvent e) {
                repaint();
            }
        });
    }
    
    /**
     * 设置复选框大小
     * 
     * @param size 大小
     */
    public void setCheckBoxSize(int size) {
        this.checkBoxSize = size;
        repaint();
    }
    
    /**
     * 设置边框圆角半径
     * 
     * @param radius 圆角半径
     */
    public void setBorderRadius(int radius) {
        this.borderRadius = radius;
        repaint();
    }
    
    /**
     * 设置未选中时的边框颜色
     * 
     * @param color 颜色
     */
    public void setUncheckedBorderColor(Color color) {
        this.uncheckedBorderColor = color;
        repaint();
    }
    
    /**
     * 设置未选中时的填充颜色
     * 
     * @param color 颜色
     */
    public void setUncheckedFillColor(Color color) {
        this.uncheckedFillColor = color;
        repaint();
    }
    
    /**
     * 设置选中时的填充颜色
     * 
     * @param color 颜色
     */
    public void setCheckedFillColor(Color color) {
        this.checkedFillColor = color;
        repaint();
    }
    
    /**
     * 设置选中标记的颜色
     * 
     * @param color 颜色
     */
    public void setCheckMarkColor(Color color) {
        this.checkMarkColor = color;
        repaint();
    }
    
    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        setForeground(enabled ? textColor : disabledTextColor);
        repaint();
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制复选框
        int x = padding;
        int y = (getHeight() - checkBoxSize) / 2;
        
        // 绘制悬停效果
        if (isHovered && isEnabled()) {
            g2.setColor(hoverColor);
            g2.fillRoundRect(x - 2, y - 2, checkBoxSize + 4, checkBoxSize + 4, borderRadius + 2, borderRadius + 2);
        }
        
        // 绘制复选框背景
        if (isSelected()) {
            g2.setColor(isEnabled() ? checkedFillColor : checkedFillColor.darker());
            g2.fillRoundRect(x, y, checkBoxSize, checkBoxSize, borderRadius, borderRadius);
        } else {
            g2.setColor(isEnabled() ? uncheckedFillColor : uncheckedFillColor.darker());
            g2.fillRoundRect(x, y, checkBoxSize, checkBoxSize, borderRadius, borderRadius);
            g2.setColor(isEnabled() ? uncheckedBorderColor : uncheckedBorderColor.darker());
            g2.drawRoundRect(x, y, checkBoxSize - 1, checkBoxSize - 1, borderRadius, borderRadius);
        }
        
        // 绘制选中标记
        if (isSelected()) {
            drawCheckMark(g2, x, y);
        }
        
        // 绘制文本
        g2.setColor(getForeground());
        g2.setFont(getFont());
        String text = getText();
        if (text != null && !text.isEmpty()) {
            int textX = x + checkBoxSize + 6;
            int textY = (getHeight() + g2.getFontMetrics().getAscent() - g2.getFontMetrics().getDescent()) / 2;
            g2.drawString(text, textX, textY);
        }
        
        g2.dispose();
    }
    
    /**
     * 绘制选中标记
     */
    private void drawCheckMark(Graphics2D g2, int x, int y) {
        g2.setColor(checkMarkColor);
        
        // 计算复选标记的点
        int size = checkBoxSize - 6;
        int startX = x + 3;
        int startY = y + 3;
        
        // 绘制对勾形状
        int[] xPoints = {startX, startX + size / 2, startX + size};
        int[] yPoints = {startY + size / 2, startY + size, startY};
        g2.setStroke(new java.awt.BasicStroke(2));
        g2.drawPolyline(xPoints, yPoints, 3);
    }
    
    @Override
    public Dimension getPreferredSize() {
        Dimension size = super.getPreferredSize();
        return new Dimension(size.width, Math.max(size.height, checkBoxSize + 2 * padding));
    }
} 