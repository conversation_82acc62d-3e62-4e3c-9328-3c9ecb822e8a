package com.ge.med.ct.ui.components;

import java.awt.Component;
import javax.swing.BorderFactory;
import javax.swing.JSplitPane;

/**
 * 自定义分割面板，使用CT工作站风格
 */
public class CTJSplitPane extends JSplitPane {
    
    /**
     * 创建一个自定义分割面板
     */
    public CTJSplitPane() {
        setupSplitPane();
    }
    
    /**
     * 创建一个包含指定组件的自定义分割面板
     * @param leftComponent 左侧组件
     * @param rightComponent 右侧组件
     */
    public CTJSplitPane(Component leftComponent, Component rightComponent) {
        super(JSplitPane.HORIZONTAL_SPLIT, leftComponent, rightComponent);
        setupSplitPane();
    }
    
    /**
     * 创建一个具有指定方向的自定义分割面板
     * @param orientation 分割方向（JSplitPane.HORIZONTAL_SPLIT 或 JSplitPane.VERTICAL_SPLIT）
     */
    public CTJSplitPane(int orientation) {
        super(orientation);
        setupSplitPane();
    }
    
    /**
     * 创建一个具有指定方向和组件的自定义分割面板
     * @param orientation 分割方向
     * @param leftComponent 左侧组件
     * @param rightComponent 右侧组件
     */
    public CTJSplitPane(int orientation, Component leftComponent, Component rightComponent) {
        super(orientation, leftComponent, rightComponent);
        setupSplitPane();
    }

    /**
     * 设置分割面板的样式
     */
    private void setupSplitPane() {
        // 移除边框
        setBorder(BorderFactory.createEmptyBorder());
        
        // 设置分割条样式
        setDividerSize(2);
        
        // 设置透明背景
        setOpaque(false);
        
        // 设置分割条位置
        setDividerLocation(0.5);  // 默认在中间
        
        // 设置调整权重
        setResizeWeight(0.5);  // 默认两边平均分配
        
        // 设置连续布局
        setContinuousLayout(true);
    }
} 