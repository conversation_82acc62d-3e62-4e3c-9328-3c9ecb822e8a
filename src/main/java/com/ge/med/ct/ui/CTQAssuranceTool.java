package com.ge.med.ct.ui;

import java.awt.BorderLayout;
import java.awt.CardLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.util.List;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.JOptionPane;
import com.ge.med.ct.ui.base.CTLookAndFeel;
import com.ge.med.ct.ui.components.CTButton;
import com.ge.med.ct.ui.components.CTPanel;
import com.ge.med.ct.ui.components.CTStatusBar;
import com.ge.med.ct.ui.utils.UIConstants;
import com.ge.med.ct.ui.views.AnalysisView;
import com.ge.med.ct.ui.views.ReportResultView;
import com.ge.med.ct.ui.presenter.AnalysisPresenter;
import com.ge.med.ct.ui.presenter.ReportPresenter;
import com.ge.med.ct.dicom.DicomDataManager;

public class CTQAssuranceTool extends JFrame {

    private AnalysisView analysisView;
    private AnalysisPresenter analysisPresenter;
    private ReportResultView reportView;
    private ReportPresenter reportPresenter;
    private CardLayout contentLayout;
    private JPanel directPanel;
    private JPanel menuBar;
    private CTPanel contentPanel;
    private CTStatusBar statusBar;
    private CTButton analysisButton;
    private CTButton reportButton;
    
    private DicomDataManager dicomManager;

    public CTQAssuranceTool() {
        try {
            CTLookAndFeel.applyLookAndFeel();
            SwingUtilities.updateComponentTreeUI(this);

            setTitle("CT Quality Assurance Tool");
            setLayout(new BorderLayout());
            setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            setSize(1150, 800);
            setLocationRelativeTo(null);
            getContentPane().setBackground(UIConstants.BACKGROUND_COLOR);

            initializeUI();
            initializeDicomManager();
        } catch (Exception ex) {
            handleError("Error in CTQualityAssuranceTool constructor", ex);
        }
    }

    private void initializeUI() {
        try {
            // 创建按钮
            createButtons();
            
            // 创建菜单栏
            createMenuBar();
            
            // 创建内容面板
            createContentPanel();
            
            // 创建状态栏
            createStatusBar();
            
            // 添加组件
            add(menuBar, BorderLayout.NORTH);
            add(contentPanel, BorderLayout.CENTER);
            add(statusBar, BorderLayout.SOUTH);

            contentLayout.show(directPanel, "analysis");
            statusBar.setStatus("分析视图已加载");
        } catch (Exception ex) {
            handleError("Error initializing UI", ex);
        }
    }
    
    private void createButtons() {
        analysisButton = new CTButton("分析");
        analysisButton.setPreferredSize(new Dimension(100, 38));
        analysisButton.addActionListener(e -> switchToView("analysis"));

        reportButton = new CTButton("报告");
        reportButton.setPreferredSize(new Dimension(100, 38));
        reportButton.addActionListener(e -> switchToView("report"));
    }
    
    private void createMenuBar() {
        menuBar = new JPanel();
        menuBar.setLayout(new FlowLayout(FlowLayout.LEFT, 20, 10));
        menuBar.setPreferredSize(new Dimension(0, 65));
        menuBar.setBackground(UIConstants.TOOLBAR_BACKGROUND);
        menuBar.add(analysisButton);
        menuBar.add(reportButton);
    }
    
    private void createContentPanel() {
        contentLayout = new CardLayout();
        
        // 创建分析视图和Presenter
        analysisView = new AnalysisView();
        analysisPresenter = new AnalysisPresenter(analysisView);
        
        // 创建报告视图和Presenter
        reportView = new ReportResultView();
        reportPresenter = new ReportPresenter(reportView);
        analysisPresenter.setReportPresenter(reportPresenter);

        directPanel = new JPanel(contentLayout);
        directPanel.setBackground(UIConstants.PANEL_BACKGROUND_COLOR);
        directPanel.add(analysisView, "analysis");
        directPanel.add(reportView, "report");

        contentPanel = new CTPanel();
        contentPanel.setContent(directPanel);
    }
    
    private void createStatusBar() {
        statusBar = new CTStatusBar();
        statusBar.setPreferredSize(new Dimension(0, 30));
        statusBar.setFont(UIConstants.LARGE_FONT.deriveFont(13f));
        statusBar.setBackground(UIConstants.TOOLBAR_BACKGROUND);
        statusBar.setForeground(UIConstants.TEXT_COLOR);
    }

    private void initializeDicomManager() {
        statusBar.setStatus("正在初始化DICOM数据...");
        setControlsEnabled(false);

        new SwingWorker<DicomDataManager, String>() {
            @Override
            protected DicomDataManager doInBackground() throws Exception {
                try {
                    publish("正在扫描DICOM目录...");
                    dicomManager = DicomDataManager.getInstance();
                    dicomManager.scanAndLoadData();
                    dicomManager.setStatusCallback(this::publish);
                    dicomManager.startFileMonitoring();
                    return dicomManager;
                } catch (Exception e) {
                    publish("数据初始化失败: " + e.getMessage());
                    throw e;
                }
            }

            @Override
            protected void process(List<String> chunks) {
                if (!chunks.isEmpty()) {
                    statusBar.setStatus(chunks.get(chunks.size() - 1));
                }
            }

            @Override
            protected void done() {
                try {
                    // 通过 Presenter 更新数据和视图
                    analysisPresenter.setDicomData(dicomManager);
                    reportPresenter.setDicomData(dicomManager);
                    directPanel.repaint();
                    statusBar.setStatus("DICOM数据初始化完成");
                } catch (Exception e) {
                    handleError("Error initializing data", e);
                } finally {
                    setControlsEnabled(true);
                }
            }
        }.execute();
    }

    private void setControlsEnabled(boolean enabled) {
        analysisButton.setEnabled(enabled);
        reportButton.setEnabled(enabled);
        if (analysisView != null) {
            analysisView.setEnabled(enabled);
        }
        if (reportView != null) {
            reportView.setEnabled(enabled);
        }
    }

    private void switchToView(String viewName) {
        try {
            contentLayout.show(directPanel, viewName);
            
            if ("report".equals(viewName)) {
                statusBar.setStatus("已切换到报告视图");
            } else if ("analysis".equals(viewName)) {
                statusBar.setStatus("已切换到分析视图");
            }
            
            directPanel.revalidate();
            directPanel.repaint();
        } catch (Exception e) {
            handleError("Error switching to view '" + viewName + "'", e);
        }
    }
    
    private void handleError(String message, Exception ex) {
        System.err.println(message + ": " + ex.getMessage());
        ex.printStackTrace();
        statusBar.setStatus(message + ": " + ex.getMessage());
        JOptionPane.showMessageDialog(this, 
            message + ": " + ex.getMessage(),
            "Application Error", 
            JOptionPane.ERROR_MESSAGE);
    }

    public static void main(String[] args) {
        try {
            SwingUtilities.invokeLater(() -> {
                try {
                    new CTQAssuranceTool().setVisible(true);
                } catch (Exception ex) {
                    System.err.println("Error starting CTQualityAssuranceTool: " + ex.getMessage());
                    ex.printStackTrace();
                    JOptionPane.showMessageDialog(null, 
                        "Error starting the application: " + ex.getMessage(),
                        "Application Error", 
                        JOptionPane.ERROR_MESSAGE);
                }
            });
        } catch (Exception ex) {
            System.err.println("Critical error in main thread: " + ex.getMessage());
            ex.printStackTrace();
        }
    }
}
