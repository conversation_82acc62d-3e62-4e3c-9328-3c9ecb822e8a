package com.ge.med.ct.ui.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.Properties;
import java.io.InputStream;
import java.io.IOException;

import com.ge.med.ct.dicom.model.DicomExam;
import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.model.DicomSeries;
import com.ge.med.ct.dicom.tag.DicomTag;

/**
 * 数据管理器，用于统一管理应用中的各类数据提供者、数据访问组件和表格数据转换
 */
public class TableViewDataManager {
    private static final Logger LOGGER = Logger.getLogger(TableViewDataManager.class.getName());
    private static final TableViewDataManager INSTANCE = new TableViewDataManager();
    
    // 存储数据提供者
    private final Map<Class<?>, Object> dataProviders = new HashMap<>();
    // 存储数据管理组件
    private final Map<String, Object> dataComponents = new HashMap<>();
    
    // 表格列定义
    private String[] examColumns;
    private String[] seriesColumns;
    private String[] imageColumns;
    
    private TableViewDataManager() {
        loadColumnDefinitions();
    }

    public String[] getExamColumns() {
        return examColumns;
    }
    
    public String[] getSeriesColumns() {
        return seriesColumns;
    }
    
    public String[] getImageColumns() {
        return imageColumns;
    }
    
    public static TableViewDataManager getInstance() {
        return INSTANCE;
    }

    /**
     * 清除所有注册的数据提供者和管理组件
     */
    public void clearAll() {
        dataProviders.clear();
        dataComponents.clear();
        LOGGER.info("Cleared all data providers and components");
    }

    /**
     * 将检查数据列表转换为表格数据
     * 
     * @param exams 检查数据列表
     * @return 表格数据向量
     */
    public Vector<Vector<String>> convertExamData(List<DicomExam> exams) {
        return convertToTableData(exams, exam -> {
            Vector<String> row = new Vector<>();
            row.add(exam.getTagValue(DicomTag.PatientID.getTagId()));
            row.add(exam.getTagValue(DicomTag.PatientName.getTagId()));
            row.add(exam.getTagValue(DicomTag.AccessionNumber.getTagId()));
            row.add(exam.getTagValue(DicomTag.StudyDate.getTagId()));
            row.add(exam.getTagValue(DicomTag.StudyTime.getTagId()));
            row.add(exam.getTagValue(DicomTag.StudyDescription.getTagId()));
            row.add(exam.getTagValue(DicomTag.Modality.getTagId()));
            return row;
        });
    }
    
    /**
     * 将序列数据列表转换为表格数据
     * 
     * @param seriesList 序列数据列表
     * @return 表格数据向量
     */
    public Vector<Vector<String>> convertSeriesData(List<DicomSeries> seriesList) {
        return convertToTableData(seriesList, series -> {
            Vector<String> row = new Vector<>();
            row.add(series.getTagValue(DicomTag.SeriesInstanceUID.getTagId()));
            row.add(series.getTagValue(DicomTag.SeriesNumber.getTagId()));
            row.add(series.getTagValue(DicomTag.SeriesDate.getTagId()));
            row.add(series.getTagValue(DicomTag.SeriesTime.getTagId()));
            row.add(series.getTagValue(DicomTag.SeriesDescription.getTagId()));
            row.add(series.getTagValue(DicomTag.BodyPartExamined.getTagId()));
            row.add(series.getTagValue(DicomTag.Modality.getTagId()));
            return row;
        });
    }
    
    /**
     * 将图像数据列表转换为表格数据
     * 
     * @param images 图像数据列表
     * @return 表格数据向量
     */
    public Vector<Vector<String>> convertImageData(List<DicomImage> images) {
        return convertToTableData(images, image -> {
            Vector<String> row = new Vector<>();
            row.add(image.getTagValue(DicomTag.SOPInstanceUID.getTagId()));
            row.add(image.getTagValue(DicomTag.SeriesInstanceUID.getTagId()));
            row.add(image.getTagValue(DicomTag.InstanceNumber.getTagId()));
            row.add(image.getTagValue(DicomTag.ImageDate.getTagId()));
            row.add(image.getTagValue(DicomTag.ImageTime.getTagId()));
            row.add(image.getTagValue(DicomTag.ImagePositionPatient.getTagId()));
            row.add(image.getTagValue(DicomTag.SliceLocation.getTagId()));
            row.add(image.getTagValue(DicomTag.ImageComments.getTagId()));
            return row;
        });
    }
    
    /**
     * 通用数据转换方法
     * 
     * @param <T> 数据类型
     * @param dataList 数据列表
     * @param rowMapper 行数据映射函数
     * @return 表格数据向量
     */
    private <T> Vector<Vector<String>> convertToTableData(List<T> dataList, Function<T, Vector<String>> rowMapper) {
        Vector<Vector<String>> tableData = new Vector<>();
        if (dataList != null) {
            for (T data : dataList) {
                tableData.add(rowMapper.apply(data));
            }
        }
        return tableData;
    }
    
    /**
     * 获取列名数组
     * 
     * @param columns 列定义数组
     * @return 列名数组
     */
    public String[] getColumnNames(String[] columns) {
        return columns;
    }
        
    private void loadColumnDefinitions() {
        Properties props = new Properties();
        try (InputStream is = getClass().getResourceAsStream("/config/application.properties")) {
            if (is != null) {
                props.load(is);
                examColumns = props.getProperty("table.exam.columns", "").split(",");
                seriesColumns = props.getProperty("table.series.columns", "").split(",");
                imageColumns = props.getProperty("table.image.columns", "").split(",");
            } else {
                LOGGER.warning("Configuration file not found.");
            }
        } catch (IOException e) {
            LOGGER.warning("Failed to load column definitions: " + e.getMessage());
        }
    }

} 