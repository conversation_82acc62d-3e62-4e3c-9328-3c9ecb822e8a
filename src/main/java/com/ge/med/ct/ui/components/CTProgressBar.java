package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JComponent;
import javax.swing.Timer;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义进度条组件，提供现代化的外观和交互效果
 */
public class CTProgressBar extends JComponent {
    private static final long serialVersionUID = 1L;
    
    public enum ProgressType {
        DEFAULT, SUCCESS, WARNING, ERROR
    }
    
    // 进度相关属性
    private int minimum = 0;
    private int maximum = 100;
    private int value = 0;
    private boolean indeterminate = false;
    private String text = null;
    private boolean showPercent = true;
    
    // 外观相关属性
    private ProgressType type = ProgressType.DEFAULT;
    private int borderRadius = UIConstants.DEFAULT_BORDER_RADIUS;
    private Color backgroundColor = new Color(220, 220, 220);
    private Color progressColor = UIConstants.PRIMARY_COLOR;
    private Color borderColor = null;  // 不绘制边框
    
    // 动画相关
    private Timer indeterminateTimer;
    private int indeterminatePosition = 0;
    private static final int INDETERMINATE_WIDTH = 50;
    
    /**
     * 创建默认进度条
     */
    public CTProgressBar() {
        setPreferredSize(new Dimension(200, 20));
        setFont(UIConstants.SMALL_FONT);
        setForeground(Color.WHITE);
        initializeTimer();
    }
    
    /**
     * 创建指定进度类型的进度条
     * 
     * @param type 进度条类型
     */
    public CTProgressBar(ProgressType type) {
        this();
        setType(type);
    }
    
    /**
     * 初始化不确定模式的动画计时器
     */
    private void initializeTimer() {
        indeterminateTimer = new Timer(15, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                indeterminatePosition = (indeterminatePosition + 1) % getWidth();
                repaint();
            }
        });
    }
    
    /**
     * 设置进度条类型
     * 
     * @param type 进度条类型
     */
    public void setType(ProgressType type) {
        this.type = type;
        
        switch (type) {
            case DEFAULT:
                progressColor = UIConstants.PRIMARY_COLOR;
                break;
            case SUCCESS:
                progressColor = UIConstants.SUCCESS_COLOR;
                break;
            case WARNING:
                progressColor = UIConstants.WARNING_COLOR;
                break;
            case ERROR:
                progressColor = UIConstants.ERROR_COLOR;
                break;
        }
        
        repaint();
    }
    
    /**
     * 获取进度条类型
     * 
     * @return 进度条类型
     */
    public ProgressType getType() {
        return type;
    }
    
    /**
     * 设置最小值
     * 
     * @param min 最小值
     */
    public void setMinimum(int min) {
        this.minimum = min;
        repaint();
    }
    
    /**
     * 获取最小值
     * 
     * @return 最小值
     */
    public int getMinimum() {
        return minimum;
    }
    
    /**
     * 设置最大值
     * 
     * @param max 最大值
     */
    public void setMaximum(int max) {
        this.maximum = max;
        repaint();
    }
    
    /**
     * 获取最大值
     * 
     * @return 最大值
     */
    public int getMaximum() {
        return maximum;
    }
    
    /**
     * 设置当前值
     * 
     * @param value 当前值
     */
    public void setValue(int value) {
        this.value = Math.max(minimum, Math.min(maximum, value));
        repaint();
    }
    
    /**
     * 获取当前值
     * 
     * @return 当前值
     */
    public int getValue() {
        return value;
    }
    
    /**
     * 设置是否为不确定模式
     * 
     * @param indeterminate 是否为不确定模式
     */
    public void setIndeterminate(boolean indeterminate) {
        this.indeterminate = indeterminate;
        
        if (indeterminate) {
            if (!indeterminateTimer.isRunning()) {
                indeterminateTimer.start();
            }
        } else {
            if (indeterminateTimer.isRunning()) {
                indeterminateTimer.stop();
            }
        }
        
        repaint();
    }
    
    /**
     * 是否为不确定模式
     * 
     * @return 是否为不确定模式
     */
    public boolean isIndeterminate() {
        return indeterminate;
    }
    
    /**
     * 设置边框圆角半径
     * 
     * @param radius 圆角半径
     */
    public void setBorderRadius(int radius) {
        this.borderRadius = radius;
        repaint();
    }
    
    /**
     * 设置显示文本
     * 
     * @param text 显示文本
     */
    public void setText(String text) {
        this.text = text;
        repaint();
    }
    
    /**
     * 获取显示文本
     * 
     * @return 显示文本
     */
    public String getText() {
        return text;
    }
    
    /**
     * 设置是否显示百分比
     * 
     * @param showPercent 是否显示百分比
     */
    public void setShowPercent(boolean showPercent) {
        this.showPercent = showPercent;
        repaint();
    }
    
    /**
     * 是否显示百分比
     * 
     * @return 是否显示百分比
     */
    public boolean isShowPercent() {
        return showPercent;
    }
    
    /**
     * 设置进度条颜色
     * 
     * @param color 颜色
     */
    public void setProgressColor(Color color) {
        this.progressColor = color;
        repaint();
    }
    
    /**
     * 设置背景颜色
     * 
     * @param color 颜色
     */
    public void setBackgroundColor(Color color) {
        this.backgroundColor = color;
        repaint();
    }
    
    /**
     * 设置边框颜色
     * 
     * @param color 颜色
     */
    public void setBorderColor(Color color) {
        this.borderColor = color;
        repaint();
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        int width = getWidth();
        int height = getHeight();
        
        // 绘制背景
        g2.setColor(backgroundColor);
        g2.fillRoundRect(0, 0, width, height, borderRadius, borderRadius);
        
        // 绘制进度
        if (indeterminate) {
            drawIndeterminateProgress(g2, width, height);
        } else {
            drawDeterminateProgress(g2, width, height);
        }
        
        // 绘制边框
        if (borderColor != null) {
            g2.setColor(borderColor);
            g2.drawRoundRect(0, 0, width - 1, height - 1, borderRadius, borderRadius);
        }
        
        // 绘制文本
        drawProgressText(g2, width, height);
        
        g2.dispose();
    }
    
    /**
     * 绘制确定进度
     */
    private void drawDeterminateProgress(Graphics2D g2, int width, int height) {
        if (value > minimum) {
            int progressWidth = (int) ((float) (value - minimum) / (maximum - minimum) * width);
            
            if (progressWidth > 0) {
                g2.setColor(progressColor);
                
                if (progressWidth >= width) {
                    // 完整进度条使用圆角矩形
                    g2.fillRoundRect(0, 0, width, height, borderRadius, borderRadius);
                } else {
                    // 部分进度条，左侧圆角、右侧平角
                    g2.fillRoundRect(0, 0, progressWidth * 2, height, borderRadius, borderRadius);
                    g2.fillRect(progressWidth, 0, progressWidth, height);
                }
            }
        }
    }
    
    /**
     * 绘制不确定进度
     */
    private void drawIndeterminateProgress(Graphics2D g2, int width, int height) {
        g2.setColor(progressColor);
        
        // 在不确定模式下绘制动画效果
        int x = indeterminatePosition;
        int barWidth = Math.min(INDETERMINATE_WIDTH, width / 2);
        
        // 确保动画完整显示
        if (x > width) {
            x = -barWidth;
        }
        
        // 绘制移动的进度条段
        g2.fillRoundRect(x, 0, barWidth, height, borderRadius, borderRadius);
    }
    
    /**
     * 绘制进度文本
     */
    private void drawProgressText(Graphics2D g2, int width, int height) {
        String displayText = null;
        
        if (text != null) {
            displayText = text;
        } else if (showPercent && !indeterminate) {
            int percent = (int) ((float) (value - minimum) / (maximum - minimum) * 100);
            displayText = percent + "%";
        }
        
        if (displayText != null) {
            g2.setFont(getFont());
            FontMetrics fm = g2.getFontMetrics();
            int textWidth = fm.stringWidth(displayText);
            int textHeight = fm.getHeight();
            int textX = (width - textWidth) / 2;
            int textY = (height - textHeight) / 2 + fm.getAscent();
            
            g2.setColor(getForeground());
            g2.drawString(displayText, textX, textY);
        }
    }
    
    @Override
    public void removeNotify() {
        super.removeNotify();
        // 停止计时器
        if (indeterminateTimer != null && indeterminateTimer.isRunning()) {
            indeterminateTimer.stop();
        }
    }
} 