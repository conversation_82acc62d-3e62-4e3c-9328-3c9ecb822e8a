package com.ge.med.ct.ui.widgets;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.imageio.ImageIO;
import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;

import com.ge.med.ct.ui.components.CTPanel;
import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 图像显示面板，用于显示图像并提供导航控制
 */
public class ImagePanel extends CTPanel {
    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = Logger.getLogger(ImagePanel.class.getName());
    private static final Color CONTROL_PANEL_BG = new Color(240, 240, 240, 220);
    
    // 图像状态
    private int currentIndex = 0;
    private BufferedImage currentImage;
    private List<BufferedImage> images = new ArrayList<>();
    private List<String> currentImagePaths = new ArrayList<>();
    
    // UI组件
    private final JPanel imageDisplayPanel;
    private final JPanel controlPanel;
    private JButton prevButton;
    private JButton nextButton;
    private JLabel indexLabel;
    
    // 后台加载
    private final ExecutorService imageLoader = Executors.newSingleThreadExecutor();
    private Future<?> currentLoading;
    private boolean isLoading = false;
    private final BufferedImage loadingImage;

    /**
     * 创建无标题图像面板
     */
    public ImagePanel() {
        super(PanelType.FLAT);
        imageDisplayPanel = createImageDisplayPanel();
        controlPanel = createControlPanel();
        loadingImage = createLoadingImage();
        
        setupLayout();
    }

    /**
     * 创建有标题图像面板
     */
    public ImagePanel(String title) {
        super(title);
        imageDisplayPanel = createImageDisplayPanel();
        controlPanel = createControlPanel();
        loadingImage = createLoadingImage();
        
        setupLayout();
    }

    /**
     * 创建图像显示面板
     */
    private JPanel createImageDisplayPanel() {
        JPanel panel = new JPanel(new BorderLayout()) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                
                if (currentImage == null) return;
                
                Graphics2D g2d = (Graphics2D) g;
                configureGraphics(g2d);
                
                // 计算缩放和位置
                int pWidth = getWidth();
                int pHeight = getHeight();
                int iWidth = currentImage.getWidth();
                int iHeight = currentImage.getHeight();
                
                if (iWidth <= 0 || iHeight <= 0) return;
                
                double scale = Math.min((double) pWidth / iWidth, (double) pHeight / iHeight);
                int sWidth = (int) (iWidth * scale);
                int sHeight = (int) (iHeight * scale);
                int x = (pWidth - sWidth) / 2;
                int y = (pHeight - sHeight) / 2;
                
                g2d.drawImage(currentImage, x, y, sWidth, sHeight, null);
            }
        };
        panel.setOpaque(false);
        return panel;
    }
    
    /**
     * 配置绘图上下文以获得最佳质量
     */
    private void configureGraphics(Graphics2D g2d) {
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
    }

    /**
     * 创建控制面板
     */
    private JPanel createControlPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(CONTROL_PANEL_BG);
        panel.setOpaque(true);
        
        // 创建导航按钮
        prevButton = new JButton("←");
        prevButton.addActionListener(e -> showPreviousImage());
        
        nextButton = new JButton("→");
        nextButton.addActionListener(e -> showNextImage());
        
        // 创建索引标签
        indexLabel = new JLabel("0/0", SwingConstants.CENTER);
        indexLabel.setFont(UIConstants.IMAGE_INDEX_FONT);
        
        // 添加到面板
        panel.add(prevButton, BorderLayout.WEST);
        panel.add(indexLabel, BorderLayout.CENTER);
        panel.add(nextButton, BorderLayout.EAST);
        panel.setVisible(true);
        
        return panel;
    }
    
    /**
     * 创建加载指示图像
     */
    private BufferedImage createLoadingImage() {
        BufferedImage img = new BufferedImage(100, 100, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = img.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setColor(new Color(240, 240, 240, 180));
        g2d.fillRect(0, 0, 100, 100);
        g2d.setColor(Color.GRAY);
        g2d.drawString("加载中...", 25, 50);
        g2d.dispose();
        return img;
    }
    
    /**
     * 设置面板布局
     */
    private void setupLayout() {
        // 设置主布局
        getContentPanel().setLayout(new BorderLayout());
        getContentPanel().add(imageDisplayPanel, BorderLayout.CENTER);
        getContentPanel().add(controlPanel, BorderLayout.SOUTH);
        
        // 初始状态下隐藏导航按钮
        setNavigationVisible(false);
    }
    
    /**
     * 设置导航按钮的可见性
     */
    private void setNavigationVisible(boolean visible) {
        prevButton.setVisible(visible);
        nextButton.setVisible(visible);
    }
    
    /**
     * 更新图片索引标签
     */
    private void updateIndexLabel() {
        String text = images.isEmpty() ? "0/0" : (currentIndex + 1) + "/" + images.size();
        indexLabel.setText(text);
    }
    
    /**
     * 加载指定路径的图片
     */
    public void loadImages(List<String> imagePaths) {
        // 检查参数
        if (imagePaths == null || imagePaths.isEmpty()) {
            clearImages();
            return;
        }
        
        // 避免重复加载
        if (hasSameImagePaths(imagePaths)) {
            return;
        }
        
        // 取消当前加载任务
        cancelCurrentLoading();
        
        // 保存路径以便比较
        currentImagePaths = new ArrayList<>(imagePaths);
        
        // 如果当前没有图像，显示加载中图像
        if (currentImage == null) {
            currentImage = loadingImage;
            imageDisplayPanel.repaint();
        }
        
        // 开始异步加载
        isLoading = true;
        imageDisplayPanel.setIgnoreRepaint(true);
        
        currentLoading = imageLoader.submit(() -> loadImagesAsync(imagePaths));
    }
    
    /**
     * 异步加载图片
     */
    private void loadImagesAsync(List<String> paths) {
        try {
            final List<BufferedImage> loadedImages = new ArrayList<>();
            boolean hasLoadedAny = false;
            
            // 预加载所有图片
            for (String path : paths) {
                BufferedImage img = loadImageFromFile(path);
                if (img != null) {
                    loadedImages.add(img);
                    hasLoadedAny = true;
                }
            }
            
            // 最终状态值
            final boolean loadSuccess = hasLoadedAny;
            
            // 在UI线程更新界面
            SwingUtilities.invokeLater(() -> {
                try {
                    updateImagesInUI(loadedImages, loadSuccess);
                } finally {
                    // 恢复UI状态
                    isLoading = false;
                    imageDisplayPanel.setIgnoreRepaint(false);
                    imageDisplayPanel.revalidate();
                    imageDisplayPanel.repaint();
                }
            });
        } catch (Exception ex) {
            LOGGER.log(Level.SEVERE, "图片加载过程出错", ex);
            SwingUtilities.invokeLater(() -> {
                isLoading = false;
                imageDisplayPanel.setIgnoreRepaint(false);
                showErrorMessage("加载图片时出错");
            });
        }
    }
    
    /**
     * 从文件加载单张图片
     */
    private BufferedImage loadImageFromFile(String path) {
        try {
            File file = new File(path);
            if (!file.exists() || !file.isFile()) {
                LOGGER.warning("图片文件不存在: " + path);
                return null;
            }
            
            // 加载原图
            BufferedImage original = ImageIO.read(file);
            if (original == null) return null;
            
            // 预缩放以提高性能
            return createOptimizedImage(original);
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "加载图片文件失败: " + path, e);
            return null;
        }
    }
    
    /**
     * 创建优化后的图像
     */
    private BufferedImage createOptimizedImage(BufferedImage src) {
        // 计算合适的大小
        int maxWidth = 800;
        int srcWidth = src.getWidth();
        int srcHeight = src.getHeight();
        
        if (srcWidth <= maxWidth) return src;
        
        // 计算缩放大小
        int targetWidth = maxWidth;
        int targetHeight = (int)(srcHeight * ((double)targetWidth / srcWidth));
        
        // 创建缩放后的图像
        BufferedImage scaled = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = scaled.createGraphics();
        configureGraphics(g2d);
        g2d.drawImage(src, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();
        
        return scaled;
    }
    
    /**
     * 在UI线程更新加载的图片
     */
    private void updateImagesInUI(List<BufferedImage> loadedImages, boolean success) {
        if (success) {
            images = loadedImages;
            currentIndex = 0;
            currentImage = images.get(0);
            updateIndexLabel();
            setNavigationVisible(images.size() > 1);
        } else {
            clearImages();
            showMessage("没有可显示的图片");
        }
    }
    
    /**
     * 取消当前的加载任务
     */
    private void cancelCurrentLoading() {
        if (currentLoading != null && !currentLoading.isDone()) {
            currentLoading.cancel(true);
        }
    }
    
    /**
     * 显示上一张图片
     */
    private void showPreviousImage() {
        if (images.isEmpty()) return;
        
        imageDisplayPanel.setIgnoreRepaint(true);
        try {
            currentIndex = (currentIndex - 1 + images.size()) % images.size();
            currentImage = images.get(currentIndex);
            updateIndexLabel();
        } finally {
            imageDisplayPanel.setIgnoreRepaint(false);
            imageDisplayPanel.repaint();
        }
    }
    
    /**
     * 显示下一张图片
     */
    private void showNextImage() {
        if (images.isEmpty()) return;
        
        imageDisplayPanel.setIgnoreRepaint(true);
        try {
            currentIndex = (currentIndex + 1) % images.size();
            currentImage = images.get(currentIndex);
            updateIndexLabel();
        } finally {
            imageDisplayPanel.setIgnoreRepaint(false);
            imageDisplayPanel.repaint();
        }
    }
    
    /**
     * 清除所有图片
     */
    public void clearImages() {
        cancelCurrentLoading();
        
        currentImage = null;
        images.clear();
        currentImagePaths.clear();
        currentIndex = 0;
        
        updateIndexLabel();
        setNavigationVisible(false);
        imageDisplayPanel.repaint();
    }
    
    /**
     * 检查是否有加载的图片
     */
    public boolean hasImages() {
        return !images.isEmpty();
    }
    
    /**
     * 检查是否具有相同的图片路径
     */
    public boolean hasSameImagePaths(List<String> paths) {
        if (currentImagePaths.size() != (paths != null ? paths.size() : 0)) {
            return false;
        }
        
        for (int i = 0; i < currentImagePaths.size(); i++) {
            if (!Objects.equals(currentImagePaths.get(i), paths.get(i))) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 显示消息
     */
    public void showMessage(String message) {
        clearImages();
        
        JLabel label = new JLabel(message, SwingConstants.CENTER);
        label.setFont(UIConstants.DEFAULT_FONT);
        label.setForeground(UIConstants.TEXT_COLOR);
        
        getContentPanel().removeAll();
        getContentPanel().setLayout(new BorderLayout());
        getContentPanel().add(label, BorderLayout.CENTER);
        getContentPanel().add(controlPanel, BorderLayout.SOUTH);
        
        revalidate();
        repaint();
    }
    
    /**
     * 显示错误消息
     */
    public void showErrorMessage(String message) {
        clearImages();
        
        JLabel label = new JLabel(message, SwingConstants.CENTER);
        label.setFont(UIConstants.DEFAULT_FONT);
        label.setForeground(Color.RED);
        
        getContentPanel().removeAll();
        getContentPanel().setLayout(new BorderLayout());
        getContentPanel().add(label, BorderLayout.CENTER);
        getContentPanel().add(controlPanel, BorderLayout.SOUTH);
        
        revalidate();
        repaint();
    }
}