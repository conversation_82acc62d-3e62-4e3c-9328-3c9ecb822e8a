package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Component;
import javax.swing.BorderFactory;
import javax.swing.JScrollPane;

/**
 * 自定义滚动面板，使用CT工作站风格
 */
public class CTJScrollPane extends JScrollPane {
    
    /**
     * 创建一个自定义滚动面板
     */
    public CTJScrollPane() {
        setupScrollPane();
    }
    
    /**
     * 创建一个包含指定组件的自定义滚动面板
     * 
     * @param view 要在滚动面板中显示的组件
     */
    public CTJScrollPane(Component view) {
        super(view);
        setupScrollPane();
    }
    
    /**
     * 创建一个具有指定滚动条策略的自定义滚动面板
     * 
     * @param view 要在滚动面板中显示的组件
     * @param vsbPolicy 垂直滚动条策略
     * @param hsbPolicy 水平滚动条策略
     */
    public CTJScrollPane(Component view, int vsbPolicy, int hsbPolicy) {
        super(view, vsbPolicy, hsbPolicy);
        setupScrollPane();
    }
    

    /**
     * 设置滚动面板的样式
     */
    private void setupScrollPane() {
        // 移除边框
        setBorder(BorderFactory.createEtchedBorder());
        
        // 设置滚动条UI
        getVerticalScrollBar().setUI(new CTScrollBarUI());
        getHorizontalScrollBar().setUI(new CTScrollBarUI());
        
        // 设置滚动条宽度
        getVerticalScrollBar().setPreferredSize(new java.awt.Dimension(10, 0));
        getHorizontalScrollBar().setPreferredSize(new java.awt.Dimension(0, 10));
        
        // 设置滚动速度
        getVerticalScrollBar().setUnitIncrement(16);
        getHorizontalScrollBar().setUnitIncrement(16);
        
        // 设置透明背景
        setOpaque(false);
        getViewport().setOpaque(false);
    }
}