package com.ge.med.ct.ui.model;

import com.ge.med.ct.dicom.util.DicomDataProvider;
import com.ge.med.ct.ui.base.BaseModel;
import com.ge.med.ct.ui.components.CTListView;
import java.util.List;

public class ReportModel extends BaseModel {
    private static final String PROP_SELECTED_ITEM = "selectedItem";
    private static final String PROP_REPORT_CONTENT = "reportContent";
    private static final String PROP_IMAGE_PATHS = "imagePaths";
    private static final String PROP_HAS_DATA = "hasData";
    
    private CTListView.ListItem selectedItem;
    private String reportContent;
    private List<String> imagePaths;
    private boolean hasData;
    private DicomDataProvider dicomManager;
    
    public void setSelectedItem(CTListView.ListItem item) {
        CTListView.ListItem oldValue = this.selectedItem;
        this.selectedItem = item;
        firePropertyChange(PROP_SELECTED_ITEM, oldValue, item);
        
        if (item != null) {
            setImagePaths(item.getImagePaths());
            setReportContent(item.getRepFile());
        }
    }
    
    public CTListView.ListItem getSelectedItem() {
        return selectedItem;
    }
    
    public void setReportContent(String content) {
        String oldValue = this.reportContent;
        this.reportContent = content;
        firePropertyChange(PROP_REPORT_CONTENT, oldValue, content);
    }
    
    public String getReportContent() {
        return reportContent;
    }
    
    public void setImagePaths(List<String> paths) {
        List<String> oldValue = this.imagePaths;
        this.imagePaths = paths;
        firePropertyChange(PROP_IMAGE_PATHS, oldValue, paths);
    }
    
    public List<String> getImagePaths() {
        return imagePaths;
    }
    
    public void setHasData(boolean hasData) {
        boolean oldValue = this.hasData;
        this.hasData = hasData;
        firePropertyChange(PROP_HAS_DATA, oldValue, hasData);
    }
    
    public boolean hasData() {
        return hasData;
    }
    
    public void setDicomManager(DicomDataProvider dicomManager) {
        this.dicomManager = dicomManager;
    }
    
    public DicomDataProvider getDicomManager() {
        return dicomManager;
    }
    
    public void resetContents() {
        setSelectedItem(null);
        setReportContent("");
        setImagePaths(null);
        setHasData(false);
    }
} 