package com.ge.med.ct.ui.utils;

import java.awt.Color;
import java.awt.Font;
import java.awt.Dimension;
import java.awt.GraphicsEnvironment;

/**
 * 集中管理UI常量，提供统一的颜色、字体和尺寸定义
 */
public class UIConstants {
    // 应用程序名称和版本
    public static final String APP_NAME = "CT Quality Assurance Tool";
    public static final String APP_VERSION = "1.0.0";
    
    // 颜色常量 - 根据图片中的CT工作站界面颜色
    public static final Color PRIMARY_COLOR = new Color(97, 107, 157);  // 蓝灰色主色调
    public static final Color BTN_SECONDARY_COLOR = new Color(69, 85, 135); // 深蓝灰色按钮背景
    public static final Color BACKGROUND_COLOR = new Color(173, 183, 215); // 淡蓝灰色背景
    public static final Color HEADER_BACKGROUND_COLOR = new Color(42, 57, 107); // 深蓝色头部背景
    public static final Color PANEL_BACKGROUND_COLOR = new Color(199, 209, 227); // 面板背景色
    public static final Color TEXT_COLOR = Color.BLACK;
    public static final Color TEXT_BRIGHT_COLOR = Color.WHITE;
    public static final Color TEXT_LIGHT_COLOR = new Color(42, 57, 107); // 蓝色文本
    public static final Color BORDER_COLOR = new Color(111, 122, 155); // 边框颜色
    public static final Color SUCCESS_COLOR = new Color(56, 142, 60);   // 成功提示色
    public static final Color WARNING_COLOR = new Color(245, 124, 0);  // 警告提示色
    public static final Color ERROR_COLOR = new Color(211, 47, 47);    // 错误提示色
    
    // 表格颜色
    public static final Color TABLE_HEADER_COLOR = new Color(111, 122, 155); // 表头背景色
    public static final Color TABLE_SELECTED_ROW_COLOR = new Color(255, 235, 156); // 选中行颜色（淡金色）
    public static final Color TABLE_ALTERNATE_ROW_COLOR = new Color(217, 225, 241); // 交替行颜色
    
    // 界面特定颜色
    public static final Color TOOLBAR_BACKGROUND = new Color(97, 107, 157); // 工具栏背景
    public static final Color MENUBAR_COLOR = new Color(42, 57, 107);      // 菜单栏背景色，较深蓝色
    public static final Color BUTTON_BACKGROUND = new Color(154, 166, 195); // 按钮背景色
    public static final Color BUTTON_BORDER = new Color(111, 122, 155); // 按钮边框色
    public static final Color FIELD_BACKGROUND = new Color(217, 225, 241); // 输入框背景色
    
    // 列表颜色
    public static final Color LIST_HEADER_COLOR = new Color(111, 122, 155); // 列表头部背景色
    public static final Color LIST_SELECTED_ROW_COLOR = new Color(255, 235, 156); // 选中行颜色（淡金色）
    public static final Color LIST_ALTERNATE_ROW_COLOR = new Color(217, 225, 241); // 交替行颜色
    public static final Color LIST_BACKGROUND_COLOR = new Color(240, 240, 242); // 列表背景颜色
    
    // 字体常量 - 使用支持中文的字体
    // 字体优先级列表
    private static final String[] FONT_NAMES = {
        "Microsoft YaHei", // 微软雅黑
        "SimSun",          // 宋体
        "Arial",           // 通用英文字体
    };

    // 获取支持中文的字体名称
    private static final String CHINESE_FONT_NAME = findChineseFont();

    // 基础字体定义
    public static final Font HEADER_FONT = new Font(CHINESE_FONT_NAME, Font.BOLD, 16);
    public static final Font TITLE_FONT = new Font(CHINESE_FONT_NAME, Font.BOLD, 20);
    public static final Font NORMAL_FONT = new Font(CHINESE_FONT_NAME, Font.PLAIN, 14);
    public static final Font SMALL_FONT = new Font(CHINESE_FONT_NAME, Font.PLAIN, 12);
    
    public static final Font DEFAULT_FONT = new Font(CHINESE_FONT_NAME, Font.PLAIN, 13);
    public static final Font LARGE_FONT = new Font(CHINESE_FONT_NAME, Font.PLAIN, 16);
    public static final Font REPORT_FONT = new Font(CHINESE_FONT_NAME, Font.PLAIN, 16);
    
    // 表格字体
    public static final Font TABLE_HEADER_FONT = new Font(CHINESE_FONT_NAME, Font.BOLD, 12);
    public static final Font TABLE_CONTENT_FONT = new Font(CHINESE_FONT_NAME, Font.PLAIN, 15);
    public static final Font IMAGE_INDEX_FONT = new Font(CHINESE_FONT_NAME, Font.PLAIN, 14);

    // 列表字体
    public static final Font LIST_ITEM_FONT = DEFAULT_FONT;
    
    // 尺寸常量
    public static final int DEFAULT_MARGIN = 12;
    public static final int DEFAULT_PADDING = 8;
    public static final int DEFAULT_BORDER_RADIUS = 4;
    public static final int DEFAULT_BUTTON_HEIGHT = 32;
    public static final int DEFAULT_COMPONENT_HEIGHT = 28;
    public static final int DEFAULT_ICON_SIZE = 16;
    public static final Dimension DEFAULT_BUTTON_SIZE = new Dimension(120, DEFAULT_BUTTON_HEIGHT);
    public static final Dimension SMALL_BUTTON_SIZE = new Dimension(80, DEFAULT_BUTTON_HEIGHT);
    public static final Dimension LARGE_BUTTON_SIZE = new Dimension(160, DEFAULT_BUTTON_HEIGHT);
    
    // 图标尺寸
    public static final int ICON_SMALL = 16;
    public static final int ICON_MEDIUM = 24;
    public static final int ICON_LARGE = 32;
    
    // 窗口尺寸
    public static final Dimension DEFAULT_WINDOW_SIZE = new Dimension(1024, 768);
    public static final Dimension DIALOG_SIZE = new Dimension(400, 300);
    
    // 动画常量
    public static final int ANIMATION_DURATION = 200; // 毫秒
    
    /**
     * 查找系统支持的中文字体
     */
    private static String findChineseFont() {
        // 尝试系统中定义的每一个字体
        for (String fontName : FONT_NAMES) {
            try {
                Font testFont = new Font(fontName, Font.PLAIN, 12);
                if (testFont.canDisplay('中') && testFont.canDisplay('文')) {
                    return fontName;
                }
            } catch (Exception e) {
                // 忽略不可用的字体
            }
        }
        
        // 如果以上都失败，尝试系统所有字体
        Font[] allFonts = GraphicsEnvironment.getLocalGraphicsEnvironment().getAllFonts();
        for (Font font : allFonts) {
            if (font.canDisplay('中') && font.canDisplay('文')) {
                return font.getName();
            }
        }
        
        // 兜底方案：使用Java默认字体
        return Font.SANS_SERIF;
    }
    
    private UIConstants() {
        // 防止实例化
    }
} 