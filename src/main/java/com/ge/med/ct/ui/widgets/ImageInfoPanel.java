package com.ge.med.ct.ui.widgets;

import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.Insets;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;

import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.tag.DicomTag;
import com.ge.med.ct.ui.components.CTPanel;
import com.ge.med.ct.ui.components.CTJScrollPane;
import com.ge.med.ct.ui.utils.ClassAlias.GBC;
import com.ge.med.ct.ui.utils.UIConstants;
import com.ge.med.ct.dicom.util.DicomDataProvider;

public class ImageInfoPanel extends CTPanel {
    private static final Insets DEFAULT_INSETS = new Insets(5, 5, 0, 5);
    private static final Insets SECTION_INSETS = new Insets(10, 5, 0, 5);

    private DicomDataProvider dicomDataProvider;
    
    public ImageInfoPanel() {
        super(PanelType.HEADER);
        setTitle("图像信息");
        createContent();
    }

    private void createContent() {
        setPreferredSize(new Dimension(0, 0));
        initializeContent();
    }

    private void initializeContent() {
        JPanel contentPanel = new JPanel(new GridBagLayout());
        contentPanel.setOpaque(false);

        CTJScrollPane scrollPane = new CTJScrollPane(contentPanel);
        scrollPane.setOpaque(false);
        scrollPane.getViewport().setOpaque(false);
        scrollPane.setBorder(null);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        
        setContent(scrollPane);
    }

    public void updateDicomProvider(DicomDataProvider provider) {
        this.dicomDataProvider = provider;
    }

    public void updateImageInfo(DicomImage image) {
        if (image == null || dicomDataProvider == null) {
            return;
        }

        JPanel contentPanel = new JPanel(new GridBagLayout());
        contentPanel.setOpaque(false);
        
        GBC gbc = new GBC();
        gbc.insets = DEFAULT_INSETS;
        gbc.fill = GBC.HORIZONTAL;
        gbc.anchor = GBC.WEST;
        gbc.weightx = 1.0;
        
        int currentRow = 0;

        // 添加患者信息部分
        addSectionHeader(contentPanel, "患者信息", currentRow++, gbc);
        addInfoRow(contentPanel, "患者ID", getTagValue(image, DicomTag.PatientID), currentRow++, gbc);
        addInfoRow(contentPanel, "患者姓名", getTagValue(image, DicomTag.PatientName), currentRow++, gbc);
        addInfoRow(contentPanel, "出生日期", getTagValue(image, DicomTag.PatientBirthDate), currentRow++, gbc);
        addInfoRow(contentPanel, "性别", getTagValue(image, DicomTag.PatientSex), currentRow++, gbc);
        addInfoRow(contentPanel, "体重", getTagValue(image, DicomTag.PatientWeight), currentRow++, gbc);
        addInfoRow(contentPanel, "年龄", getTagValue(image, DicomTag.PatientAge), currentRow++, gbc);
        addInfoRow(contentPanel, "身高", getTagValue(image, DicomTag.PatientSize), currentRow++, gbc);
        currentRow++;

        // 添加检查信息部分
        addSectionHeader(contentPanel, "检查信息", currentRow++, gbc);
        addInfoRow(contentPanel, "检查ID", getTagValue(image, DicomTag.StudyID), currentRow++, gbc);
        addInfoRow(contentPanel, "检查日期", getTagValue(image, DicomTag.StudyDate), currentRow++, gbc);
        addInfoRow(contentPanel, "检查时间", getTagValue(image, DicomTag.StudyTime), currentRow++, gbc);
        addInfoRow(contentPanel, "检查描述", getTagValue(image, DicomTag.StudyDescription), currentRow++, gbc);
        addInfoRow(contentPanel, "检查号", getTagValue(image, DicomTag.AccessionNumber), currentRow++, gbc);
        addInfoRow(contentPanel, "设备类型", getTagValue(image, DicomTag.Modality), currentRow++, gbc);
        currentRow++;

        // 添加序列信息部分
        addSectionHeader(contentPanel, "序列信息", currentRow++, gbc);
        addInfoRow(contentPanel, "序列号", getTagValue(image, DicomTag.SeriesNumber), currentRow++, gbc);
        addInfoRow(contentPanel, "序列描述", getTagValue(image, DicomTag.SeriesDescription), currentRow++, gbc);
        addInfoRow(contentPanel, "检查部位", getTagValue(image, DicomTag.BodyPartExamined), currentRow++, gbc);
        currentRow++;

        // 添加图像信息部分
        addSectionHeader(contentPanel, "图像信息", currentRow++, gbc);
        addInfoRow(contentPanel, "图像类型", getTagValue(image, DicomTag.ImageType), currentRow++, gbc);
        addInfoRow(contentPanel, "行数", getTagValue(image, DicomTag.Rows), currentRow++, gbc);
        addInfoRow(contentPanel, "列数", getTagValue(image, DicomTag.Columns), currentRow++, gbc);

        // 添加设备信息部分
        addSectionHeader(contentPanel, "设备信息", currentRow++, gbc);

        CTJScrollPane scrollPane = new CTJScrollPane(contentPanel);
        scrollPane.setOpaque(false);
        scrollPane.getViewport().setOpaque(false);
        scrollPane.setBorder(null);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);

        setContent(scrollPane);
        revalidate();
        repaint();
    }

    private String getTagValue(DicomImage image, DicomTag tag) {
        if (image == null || tag == null || dicomDataProvider == null) {
            return "";
        }
        String value = dicomDataProvider.getTagValue(image.getId(), tag.getTagId());
        return value != null ? value : "";
    }

    private void addSectionHeader(JPanel panel, String title, int row, GBC gbc) {
        JLabel headerLabel = new JLabel(title);
        headerLabel.setFont(UIConstants.TABLE_HEADER_FONT);
        headerLabel.setForeground(UIConstants.TEXT_LIGHT_COLOR);
        
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.insets = SECTION_INSETS;
        panel.add(headerLabel, gbc);
        
        gbc.insets = DEFAULT_INSETS;
        gbc.gridwidth = 1;
    }

    private void addInfoRow(JPanel panel, String label, String value, int row, GBC gbc) {
        JLabel labelComponent = new JLabel(label + ":");
        labelComponent.setFont(UIConstants.TABLE_CONTENT_FONT);
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        panel.add(labelComponent, gbc);

        JLabel valueComponent = new JLabel(value != null ? value : "");
        valueComponent.setFont(UIConstants.TABLE_CONTENT_FONT);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        panel.add(valueComponent, gbc);
    }

    public void clearContents() {
        if (getContentPanel() != null) {
            getContentPanel().removeAll();
        }
        dicomDataProvider = null;
        revalidate();
        repaint();
    }
} 