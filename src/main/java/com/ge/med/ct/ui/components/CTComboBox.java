package com.ge.med.ct.ui.components;

import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Rectangle;
import java.awt.RenderingHints;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;

import javax.swing.BorderFactory;
import javax.swing.DefaultListCellRenderer;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JScrollPane;
import javax.swing.JComponent;
import javax.swing.JList;
import javax.swing.border.EmptyBorder;
import javax.swing.plaf.basic.BasicComboBoxUI;
import javax.swing.plaf.basic.BasicComboPopup;
import javax.swing.plaf.basic.ComboPopup;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 自定义下拉选择框组件，提供现代化的外观和交互效果
 * 
 * @param <E> 列表项类型
 */
public class CTComboBox<E> extends JComboBox<E> {
    private static final long serialVersionUID = 1L;
    
    private boolean isFocused = false;
    private int borderRadius = UIConstants.DEFAULT_BORDER_RADIUS;
    private Color borderColor = UIConstants.BORDER_COLOR;
    private Color focusedBorderColor = UIConstants.PRIMARY_COLOR;
    private Color arrowColor = UIConstants.TEXT_COLOR;
    
    /**
     * 创建默认的空下拉框
     */
    public CTComboBox() {
        configureComboBox();
    }
    
    /**
     * 创建包含指定项的下拉框
     * 
     * @param items 列表项数组
     */
    public CTComboBox(E[] items) {
        super(items);
        configureComboBox();
    }
    
    /**
     * 配置下拉框的基本属性
     */
    private void configureComboBox() {
        setOpaque(false);
        setBorder(new EmptyBorder(UIConstants.DEFAULT_PADDING, 
                UIConstants.DEFAULT_PADDING + 2, 
                UIConstants.DEFAULT_PADDING, 
                UIConstants.DEFAULT_PADDING + 2));
        setFont(UIConstants.NORMAL_FONT);
        setForeground(UIConstants.TEXT_COLOR);
        setBackground(Color.WHITE);
        
        // 设置默认尺寸
        setPreferredSize(new Dimension(150, UIConstants.DEFAULT_BUTTON_HEIGHT));
        
        // 设置自定义UI
        setUI(new CTComboBoxUI());
        
        // 设置自定义渲染器
        setRenderer(new CTComboBoxRenderer());
        
        // 添加焦点监听器
        addFocusListener(new FocusAdapter() {
            @Override
            public void focusGained(FocusEvent e) {
                isFocused = true;
                repaint();
            }
            
            @Override
            public void focusLost(FocusEvent e) {
                isFocused = false;
                repaint();
            }
        });
    }
    
    /**
     * 设置边框颜色
     * 
     * @param color 颜色
     */
    public void setBorderColor(Color color) {
        this.borderColor = color;
        repaint();
    }
    
    /**
     * 设置获焦点时的边框颜色
     * 
     * @param color 颜色
     */
    public void setFocusedBorderColor(Color color) {
        this.focusedBorderColor = color;
        repaint();
    }
    
    /**
     * 设置箭头颜色
     * 
     * @param color 颜色
     */
    public void setArrowColor(Color color) {
        this.arrowColor = color;
        repaint();
    }
    
    /**
     * 设置边框圆角半径
     * 
     * @param radius 圆角半径
     */
    public void setBorderRadius(int radius) {
        this.borderRadius = radius;
        repaint();
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制背景
        g2.setColor(getBackground());
        g2.fillRoundRect(0, 0, getWidth(), getHeight(), borderRadius, borderRadius);
        
        // 绘制边框
        g2.setColor(isFocused ? focusedBorderColor : borderColor);
        g2.drawRoundRect(0, 0, getWidth() - 1, getHeight() - 1, borderRadius, borderRadius);
        
        g2.dispose();
        
        super.paintComponent(g);
    }
    
    /**
     * 自定义下拉框UI实现
     */
    private class CTComboBoxUI extends BasicComboBoxUI {
        @Override
        protected ComboPopup createPopup() {
            BasicComboPopup popup = new BasicComboPopup(comboBox) {
                private static final long serialVersionUID = 1L;
                
                @Override
                protected JScrollPane createScroller() {
                    JScrollPane scroller = super.createScroller();
                    scroller.getVerticalScrollBar().setUI(new CTScrollBarUI());
                    scroller.setBorder(BorderFactory.createEmptyBorder());
                    return scroller;
                }
                
                @Override
                protected void configureList() {
                    super.configureList();
                    list.setFont(UIConstants.NORMAL_FONT);
                    list.setSelectionBackground(UIConstants.PRIMARY_COLOR);
                    list.setSelectionForeground(Color.WHITE);
                    list.setBorder(BorderFactory.createEmptyBorder(2, 2, 2, 2));
                }
                
                @Override
                public void show() {
                    setSize(Math.max(comboBox.getWidth(), getPreferredSize().width), 
                            getPreferredSize().height);
                    super.show();
                }
            };
            
            popup.setBorder(BorderFactory.createLineBorder(UIConstants.BORDER_COLOR));
            return popup;
        }
        
        @Override
        protected JButton createArrowButton() {
            JButton button = super.createArrowButton();
            button.setOpaque(false);
            button.setBorder(BorderFactory.createEmptyBorder());
            button.setBackground(new Color(0, 0, 0, 0));
            return button;
        }
        
        @Override
        public void paintCurrentValueBackground(Graphics g, Rectangle bounds, boolean hasFocus) {
            // 不绘制选中项背景，由组件自己处理
        }
        
        @Override
        public void paint(Graphics g, JComponent c) {
            super.paint(g, c);
            
            // 绘制箭头
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            int arrowSize = 8;
            int x = c.getWidth() - arrowSize - 8;
            int y = c.getHeight() / 2 - arrowSize / 2;
            
            g2.setColor(arrowColor);
            g2.translate(x, y);
            g2.fillPolygon(
                    new int[] {0, arrowSize, arrowSize / 2}, 
                    new int[] {0, 0, arrowSize / 2}, 
                    3);
            
            g2.dispose();
        }
    }
    
    /**
     * 自定义项渲染器
     */
    private class CTComboBoxRenderer extends DefaultListCellRenderer {
        private static final long serialVersionUID = 1L;
        
        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value, 
                int index, boolean isSelected, boolean cellHasFocus) {
            
            Component c = super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
            
            if (c instanceof JComponent) {
                ((JComponent) c).setBorder(new EmptyBorder(5, 8, 5, 8));
            }
            
            if (index == -1 && !isSelected) {
                // 未打开下拉列表时的显示项
                c.setForeground(UIConstants.TEXT_COLOR);
                c.setBackground(Color.WHITE);
            }
            
            return c;
        }
    }
}