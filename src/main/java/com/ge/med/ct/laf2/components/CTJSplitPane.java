package com.ge.med.ct.laf2.components;

import javax.swing.*;
import java.awt.*;

/**
 * 自定义分割面板，使用CT工作站风格
 */
public class CTJSplitPane extends JSplitPane {

    public CTJSplitPane() {
        setupSplitPane();
    }

    public CTJSplitPane(Component leftComponent, Component rightComponent) {
        super(JSplitPane.HORIZONTAL_SPLIT, leftComponent, rightComponent);
        setupSplitPane();
    }

    public CTJSplitPane(int orientation) {
        super(orientation);
        setupSplitPane();
    }

    public CTJSplitPane(int orientation, Component leftComponent, Component rightComponent) {
        super(orientation, leftComponent, rightComponent);
        setupSplitPane();
    }

    private void setupSplitPane() {
        setBorder(BorderFactory.createEmptyBorder());
        setDividerSize(2);
        setOpaque(false);
        setDividerLocation(0.5);
        setResizeWeight(0.5);
        setContinuousLayout(true);
    }

    /**
     * 设置分割方向
     * @param orientation 分割方向，应当为以下之一: JSplitPane.HORIZONTAL_SPLIT, JSplitPane.VERTICAL_SPLIT
     */
    @Override
    public void setOrientation(int orientation) {
        if (orientation != JSplitPane.HORIZONTAL_SPLIT && orientation != JSplitPane.VERTICAL_SPLIT) {
            throw new IllegalArgumentException("方向参数必须为 JSplitPane.HORIZONTAL_SPLIT 或 JSplitPane.VERTICAL_SPLIT");
        }
        super.setOrientation(orientation);
    }
}