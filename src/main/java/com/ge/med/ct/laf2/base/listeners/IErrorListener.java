package com.ge.med.ct.laf2.base.listeners;

/**
 * 错误监听器接口
 * 用于统一处理组件中的错误
 */
public interface IErrorListener {
    
    /**
     * 当发生错误时调用
     * @param message 错误消息
     * @param error 异常对象
     */
    void onError(String message, Throwable error);
    
    /**
     * 当发生错误时调用（无异常对象）
     * @param message 错误消息
     */
    default void onError(String message) {
        onError(message, null);
    }
}
