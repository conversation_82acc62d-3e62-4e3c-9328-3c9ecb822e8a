package com.ge.med.ct.laf2.model;

import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.laf2.base.BaseModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AnalysisModel extends BaseModel {
    public void setDicomProvider(DicomDataProvider provider) {
    }

    public boolean hasData() {
        return hasData;
    }

    public enum Property {
        INPUT_PATH,
        OUTPUT_PATH,
        PROTOCOL,
        HAS_DATA,
        IMAGE_PATHS
    }

    private String inputPath = "";
    private String outputPath = "";
    private String protocol = "";
    private boolean hasData = false;
    private List<String> imagePaths = new ArrayList<>();

    private AnalysisModel() {
    }

    public void setInputPath(String inputPath) {
        String oldValue = this.inputPath;
        this.inputPath = inputPath;
        firePropertyChange(Property.INPUT_PATH.name(), oldValue, inputPath);
    }

    public String getInputPath() {
        return inputPath;
    }

    public void setOutputPath(String outputPath) {
        String oldValue = this.outputPath;
        this.outputPath = outputPath;
        firePropertyChange(Property.OUTPUT_PATH.name(), oldValue, outputPath);
    }

    public String getOutputPath() {
        return outputPath;
    }

    public void setProtocol(String protocol) {
        String oldValue = this.protocol;
        this.protocol = protocol;
        firePropertyChange(Property.PROTOCOL.name(), oldValue, protocol);
    }

    public String getProtocol() {
        return protocol;
    }

    public void setHasData(boolean hasData) {
        boolean oldValue = this.hasData;
        this.hasData = hasData;
        firePropertyChange(Property.HAS_DATA.name(), oldValue, hasData);
    }

    public void setImagePaths(List<String> imagePaths) {
        List<String> oldValue = this.imagePaths;
        this.imagePaths = new ArrayList<>(imagePaths);
        firePropertyChange(Property.IMAGE_PATHS.name(), oldValue, this.imagePaths);
    }

    public void resetContents() {
        setInputPath("");
        setOutputPath("");
        setProtocol("");
        setHasData(false);
        setImagePaths(Collections.emptyList());
    }

    public List<String> getImagePaths() {
        return Collections.unmodifiableList(imagePaths);
    }

    public String getReportPath() {
        return outputPath;
    }

    public static class Builder {
        private final AnalysisModel model;

        public Builder() {
            model = new AnalysisModel();
        }

        public Builder outputPath(String outputPath) {
            model.setOutputPath(outputPath);
            return this;
        }

        public Builder protocol(String protocol) {
            model.setProtocol(protocol);
            return this;
        }

        public Builder imagePaths(List<String> imagePaths) {
            model.setImagePaths(imagePaths);
            return this;
        }

        public AnalysisModel build() {
            return model;
        }
    }
}