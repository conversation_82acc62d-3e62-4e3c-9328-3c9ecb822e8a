package com.ge.med.ct.laf2.base.views;

import com.ge.med.ct.analysis.model.AnalysisResult;
import com.ge.med.ct.laf2.base.listeners.IReportViewListener;
import java.util.List;

/**
 * 报告视图接口
 */
public interface IReportView extends IBaseView<IReportViewListener> {
    /**
     * 添加检查项
     * 
     * @param itemId   项的唯一标识
     * @param title    标题
     * @param response 分析结果
     */
    void addCheckItem(String itemId, String title, AnalysisResult response);


    /**
     * 显示报告内容
     * @param content 报告内容
     */
    void displayReport(String content);

    /**
     * 加载图像
     * @param imagePaths 图像路径列表
     */
    void loadImages(List<String> imagePaths);

    /**
     * 设置数据状态
     * @param hasData 是否有数据
     */
    void setHasData(boolean hasData);

    /**
     * 设置加载状态
     * @param loading 是否正在加载
     */
    void setLoading(boolean loading);
}