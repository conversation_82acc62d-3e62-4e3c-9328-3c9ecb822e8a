package com.ge.med.ct.laf2.model;

/**
 * 协议类型类
 * 根据command line mode文档中的协议类型表格定义
 */
public class ProtocolTypeMode {
    /**
     * 图像支持类型枚举
     */
    public enum ImageSupport {
        SINGLE("单图像"),
        MULTI("多图像");

        private final String description;

        ImageSupport(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    private final String protocolString;
    private final String objectType;
    private final String description;
    private final ImageSupport imageSupport;

    /**
     * 构造函数
     * @param protocolString 协议字符串
     * @param objectType 对象类型
     * @param description 描述
     * @param imageSupport 图像支持类型
     */
    public ProtocolTypeMode(String protocolString, String objectType, String description, ImageSupport imageSupport) {
        this.protocolString = protocolString;
        this.objectType = objectType;
        this.description = description;
        this.imageSupport = imageSupport;
    }

    /**
     * 获取协议字符串
     * @return 协议字符串
     */
    public String getProtocolString() {
        return protocolString;
    }

    /**
     * 获取对象类型
     * @return 对象类型
     */
    public String getObjectType() {
        return objectType;
    }

    /**
     * 获取描述
     * @return 描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取图像支持类型
     * @return 图像支持类型
     */
    public ImageSupport getImageSupport() {
        return imageSupport;
    }

    /**
     * 返回用于显示的字符串
     * @return 显示字符串
     */
    @Override
    public String toString() {
        // 返回简单的字符串，因为我们使用HTML表格来显示内容
        return protocolString;
    }

    /**
     * 获取所有支持的协议类型
     * @return 协议类型数组
     */
    public static ProtocolTypeMode[] getAllProtocolTypes() {
        return new ProtocolTypeMode[] {
            new ProtocolTypeMode("MEANS", "Means", "分析图像均值，评估图像均匀性", ImageSupport.SINGLE),
            new ProtocolTypeMode("SERIES_MEANS", "SeriesMeans", "分析多张图像的均值并比较", ImageSupport.MULTI),
            new ProtocolTypeMode("STREAK", "Streak", "检测条纹伪影，评估图像质量", ImageSupport.SINGLE),
            new ProtocolTypeMode("RING", "Ring", "检测环形伪影，评估图像质量", ImageSupport.SINGLE),
            new ProtocolTypeMode("CLUMP", "Clump", "分析图像中的团块", ImageSupport.SINGLE),
            new ProtocolTypeMode("CENTER_SMUDGE", "CenterSmudge", "检测中心区域的污点伪影", ImageSupport.SINGLE),
            new ProtocolTypeMode("LARGE_CENTER_SMUDGE", "LargeCenterSmudge", "检测大型中心污点伪影", ImageSupport.SINGLE),
            new ProtocolTypeMode("CENTER_ARTIFACT", "CentreArtifact", "检测中心区域的伪影", ImageSupport.SINGLE),
            new ProtocolTypeMode("CENTER_SPOT", "CentreSpot", "检测中心点伪影", ImageSupport.SINGLE),
            new ProtocolTypeMode("BAND", "BandArtifact", "检测带状伪影", ImageSupport.SINGLE),
            new ProtocolTypeMode("QA1", "MTF", "分析MTF（调制传递函数）", ImageSupport.SINGLE),
            new ProtocolTypeMode("QA2", "MTFContrast2", "分析MTF和对比度（QA2标准）", ImageSupport.SINGLE),
            new ProtocolTypeMode("QA3", "MTFContrast2", "分析MTF和对比度（QA3标准）", ImageSupport.SINGLE),
            new ProtocolTypeMode("LCD", "Lcd", "评估低对比度检测能力", ImageSupport.SINGLE),
            new ProtocolTypeMode("MTF10_50", "ImageResolution", "分析图像分辨率（MTF因子为10）", ImageSupport.SINGLE),
            new ProtocolTypeMode("MTF4_50", "ImageResolution", "分析图像分辨率（MTF因子为4）", ImageSupport.SINGLE),
            new ProtocolTypeMode("P35_NOISE", "MTFContrast2", "分析P35噪声", ImageSupport.SINGLE)
        };
    }
}
