package com.ge.med.ct.laf2.base.listeners;

/**
 * DICOM信息查看视图监听器接口
 */
public interface IDicomInsightViewListener extends IViewListener {
    /**
     * 处理DICOM文件加载完成事件
     * @param filePath 文件路径
     * @param tagCount 标签数量
     */
    void onDicomFileLoaded(String filePath, int tagCount);
    
    /**
     * 处理目录加载完成事件
     * @param directoryPath 目录路径
     * @param fileCount 文件数量
     */
    void onDirectoryLoaded(String directoryPath, int fileCount);
}
