package com.ge.med.ct.laf2.theming;

import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.laf2.theming.themes.CTTheme;
import com.ge.med.ct.laf2.theming.themes.LightTheme;
import com.ge.med.ct.laf2.theming.themes.DarkTheme;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 主题管理器
 * 负责管理主题的注册、切换和监听
 */
public class ThemeManager {
    private static ThemeManager instance;
    private final Map<String, Theme> themes = new HashMap<>();
    private Theme currentTheme;
    private final List<ThemeChangeListener> listeners = new ArrayList<>();
    private final ConfigManager configManager = ConfigManager.getInstance();

    // 主题变更监听器接口
    public interface ThemeChangeListener {
        void onThemeChanged(Theme newTheme);
    }

    private ThemeManager() {
        // 注册所有主题
        registerTheme(new CTTheme());
        registerTheme(new LightTheme());
        registerTheme(new DarkTheme());

        // 从配置中获取当前主题
        String themeName = configManager.getString("ui.theme", "ct-theme");
        currentTheme = themes.getOrDefault(themeName, themes.get("ct-theme"));
    }

    public static synchronized ThemeManager getInstance() {
        if (instance == null) {
            instance = new ThemeManager();
        }
        return instance;
    }

    public void registerTheme(Theme theme) {
        Objects.requireNonNull(theme, "Theme cannot be null");
        themes.put(theme.getName(), theme);
    }

    public Theme getCurrentTheme() {
        return currentTheme;
    }

    public List<Theme> getAllThemes() {
        return new ArrayList<>(themes.values());
    }

    public boolean switchTheme(String themeName) {
        Objects.requireNonNull(themeName, "Theme name cannot be null");

        if (themes.containsKey(themeName) && !themeName.equals(currentTheme.getName())) {
            Theme newTheme = themes.get(themeName);
            currentTheme = newTheme;

            // 保存主题设置到配置
            configManager.setProperty("ui.theme", themeName);

            // 通知所有监听器
            for (ThemeChangeListener listener : listeners) {
                listener.onThemeChanged(newTheme);
            }

            return true;
        }
        return false;
    }

    public void addThemeChangeListener(ThemeChangeListener listener) {
        Objects.requireNonNull(listener, "Listener cannot be null");
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    public void removeThemeChangeListener(ThemeChangeListener listener) {
        Objects.requireNonNull(listener, "Listener cannot be null");
        listeners.remove(listener);
    }
}
