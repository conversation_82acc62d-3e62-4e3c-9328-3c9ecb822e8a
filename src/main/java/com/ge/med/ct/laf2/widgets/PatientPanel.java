package com.ge.med.ct.laf2.widgets;

import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.cfg.table.TableColumnManager;
import com.ge.med.ct.cfg.table.TableColumn;
import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.laf2.base.listeners.IErrorListener;
import com.ge.med.ct.laf2.components.CTButton;
import com.ge.med.ct.laf2.components.CTPanel;
import com.ge.med.ct.laf2.components.CTTable;
import com.ge.med.ct.laf2.presenter.PatientPresenter;
import com.ge.med.ct.laf2.utils.ClassAlias.GBC;
import com.ge.med.ct.laf2.utils.ClassAlias.GBLayout;
import com.ge.med.ct.laf2.utils.ComponentFactory;
import com.ge.med.ct.laf2.utils.LayoutWorker;
import com.ge.med.ct.laf2.utils.MessageDisplayer;
import com.ge.med.ct.laf2.views.IPatientView;
import com.ge.med.ct.service.TableDataEngine;
import com.ge.med.ct.service.TableOperations;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

/**
 * 患者面板组件
 * 显示DICOM检查、序列和图像数据，并提供查询和选择功能
 */
public class PatientPanel extends JPanel implements IPatientView {
    private static final Insets DEFAULT_INSETS = new Insets(5, 5, 0, 5);

    private final CTTable examList;
    private final CTTable seriesList;
    private final CTTable imageList;

    private final ImageInfoPanel imageInfoPanel;
    private final JTextField patientNameField;
    private final JTextField examIdField;

    // 服务
    private final ConfigManager configManager;
    private final TableColumnManager tableColumnManager;
    private final TableDataEngine tableDataEngine;
    private final PatientPresenter presenter;

    // 列名到显示名的映射缓存
    private final Map<String, Map<String, String>> columnDisplayMaps = new HashMap<>();

    // 监听器
    private IErrorListener errorListener;

    public PatientPanel() {
        this.configManager = ConfigManager.getInstance();
        this.tableColumnManager = configManager.getTableColumnManager();
        this.tableDataEngine = new TableDataEngine(tableColumnManager);

        this.imageInfoPanel = new ImageInfoPanel();
        this.patientNameField = ComponentFactory.createTextField();
        this.examIdField = ComponentFactory.createTextField();

        this.examList = createTable(TableColumnManager.TABLE_EXAM);
        this.seriesList = createTable(TableColumnManager.TABLE_SERIES);
        this.imageList = createTable(TableColumnManager.TABLE_IMAGE);
        
        // 创建Presenter并绑定当前视图
        this.presenter = new PatientPresenter(this);

        initializeLayout();
        setupTableSelectionListeners();
    }

    private void initializeLayout() {
        setLayout(new GBLayout());
        createSearchPanel();
        createDataGrids();
        createImageInfoPanel();
    }

    private CTTable createTable(String tableName) {
        CTTable table = new CTTable();
        table.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        return table;
    }

    private void createSearchPanel() {
        CTPanel searchPanel = new CTPanel(new GBLayout());
        searchPanel.setPreferredSize(new Dimension(0, 120));
        searchPanel.setOpaque(false);

        JLabel patientNameLabel = ComponentFactory.createLabel("患者姓名:");
        JLabel examIdLabel = ComponentFactory.createLabel("检查ID:");
        CTButton queryButton = ComponentFactory.createButton("查询", e -> performSearch());
        CTButton clearButton = ComponentFactory.createButton("清理", e -> clearSearchFields());

        LayoutWorker helper = new LayoutWorker(searchPanel);
        helper.insets(DEFAULT_INSETS).fill(GridBagConstraints.HORIZONTAL).anchor(GridBagConstraints.LINE_START);

        helper.position(0, 0).add(patientNameLabel);
        helper.position(1, 0).weight(1.0, 0).size(2, 1).add(patientNameField);
        helper.position(0, 1).weight(0, 0).size(1, 1).add(examIdLabel);
        helper.position(1, 1).weight(1.0, 0).size(2, 1).add(examIdField);
        helper.anchor(GridBagConstraints.LINE_END);
        helper.position(1, 2).weight(0, 0).size(1, 1).add(clearButton);
        helper.position(2, 2).weight(0, 0).size(1, 1).add(queryButton);

        LayoutWorker mainHelper = new LayoutWorker(this);
        mainHelper.position(0, 0).weight(1.0, 0.05).size(2, 1).fill(GBC.BOTH)
                .insets(new Insets(0, 5, 0, 0)).add(searchPanel);
    }

    private void performSearch() {
        String patientName = patientNameField.getText().trim();
        String examId = examIdField.getText().trim();
        presenter.searchExams(patientName, examId);
    }

    private void clearSearchFields() {
        patientNameField.setText("");
        examIdField.setText("");
    }

    private void createDataGrids() {
        CTPanel examPanel = ComponentFactory.createTablePanel("检查列表", examList);
        CTPanel seriesPanel = ComponentFactory.createTablePanel("序列", seriesList);
        CTPanel imagePanel = ComponentFactory.createTablePanel("图像", imageList);

        LayoutWorker helper = new LayoutWorker(this);
        helper.fill(GBC.BOTH).insets(DEFAULT_INSETS);
        helper.position(0, 1).weight(0.67, 0.35).size(1, 1).add(examPanel);
        helper.position(0, 2).weight(0.67, 0.3).size(1, 1).add(seriesPanel);
        helper.position(0, 3).weight(0.67, 0.35).size(1, 1).add(imagePanel);
    }

    private void createImageInfoPanel() {
        LayoutWorker helper = new LayoutWorker(this);
        helper.position(1, 1).size(1, 3).weight(0.33, 1.0).fill(GBC.BOTH)
                .insets(new Insets(5, 0, 0, 0)).add(imageInfoPanel);
    }

    private void setupTableSelectionListeners() {
        examList.getSelectionModel().addListSelectionListener(e -> {
            if (e.getValueIsAdjusting())
                return;
            try {
                presenter.handleExamSelection(examList.getSelectedRow());
            } catch (Exception ex) {
                showError("检查选择失败", ex);
            }
        });

        seriesList.getSelectionModel().addListSelectionListener(e -> {
            if (e.getValueIsAdjusting())
                return;
            try {
                presenter.handleSeriesSelection(seriesList.getSelectedRow());
            } catch (Exception ex) {
                showError("序列选择失败", ex);
            }
        });

        imageList.getSelectionModel().addListSelectionListener(e -> {
            if (e.getValueIsAdjusting() || imageList.getSelectedRow() == -1)
                return;
            try {
                presenter.handleImageSelection(imageList.getSelectedRow());
            } catch (Exception ex) {
                showError("图像选择失败", ex);
            }
        });
    }

    /**
     * 设置选择变化监听器
     * 
     * @param listener 选择变化监听器
     */
    public void setSelectionChangeListener(PatientPresenter.SelectionChangeListener listener) {
        presenter.setSelectionChangeListener(listener);
    }

    /**
     * 设置错误监听器
     * 
     * @param listener 错误监听器
     */
    public void setErrorListener(IErrorListener listener) {
        this.errorListener = listener;
    }

    /**
     * 更新DICOM数据
     * 
     * @param provider DICOM数据提供者
     */
    public void updateContents(DicomDataProvider provider) {
        presenter.setDicomDataProvider(provider);
    }

    /**
     * 重新加载面板数据
     */
    public void reload() {
        // 清除列名映射缓存
        columnDisplayMaps.clear();
        presenter.loadData();
    }

    //------------- PatientView接口实现 --------------

    @Override
    public void updateExamList(List<DicomExam> exams) {
        Vector<Vector<String>> data = tableDataEngine.convertExamData(exams);
        updateTable(examList, data, TableColumnManager.TABLE_EXAM);
    }

    @Override
    public void updateSeriesList(List<DicomSeries> series) {
        Vector<Vector<String>> data = tableDataEngine.convertSeriesData(series);
        updateTable(seriesList, data, TableColumnManager.TABLE_SERIES);
    }

    @Override
    public void updateImageList(List<DicomImage> images) {
        Vector<Vector<String>> data = tableDataEngine.convertImageData(images);
        updateTable(imageList, data, TableColumnManager.TABLE_IMAGE);
    }
    
    /**
     * 更新表格数据
     * 
     * @param table 要更新的表格
     * @param data 表格数据
     * @param tableType 表格类型
     */
    private void updateTable(CTTable table, Vector<Vector<String>> data, String tableType) {
        Vector<String> displayNames = getColumnDisplayNames(tableType);
        table.updateData(new DefaultTableModel(data, displayNames));
        TableOperations.adjustColumnWidthsAsync(table);
    }

    @Override
    public void updateImageInfo(String filePath) {
        if (!filePath.isEmpty()) {
            imageInfoPanel.loadDicomFile(filePath);
        }
    }

    @Override
    public void selectExam(int index) {
        selectTableRow(examList, index);
    }

    @Override
    public void selectSeries(int index) {
        selectTableRow(seriesList, index);
    }

    @Override
    public void selectImage(int index) {
        selectTableRow(imageList, index);
    }

    /**
     * 选择表格行
     * 
     * @param table 要操作的表格
     * @param index 要选择的行索引
     */
    private void selectTableRow(JTable table, int index) {
        if (table.getRowCount() > index && index >= 0) {
            table.setRowSelectionInterval(index, index);
        }
    }

    @Override
    public void clearContents() {
        clearTable(examList);
        clearTable(seriesList);
        clearTable(imageList);
        imageInfoPanel.clearContents();
    }
    
    /**
     * 清除表格内容
     * 
     * @param table 要清除的表格
     */
    private void clearTable(CTTable table) {
        if (table != null) {
            table.clearItems();
        }
    }

    @Override
    public void showError(String message, Throwable error) {
        if (errorListener != null) {
            errorListener.onError(message, error);
        } else {
            MessageDisplayer.showException(this, "错误", message, error);
        }
    }

    @Override
    public int getSelectedExamRow() {
        return examList.getSelectedRow();
    }

    @Override
    public int getSelectedSeriesRow() {
        return seriesList.getSelectedRow();
    }

    @Override
    public int getSelectedImageRow() {
        return imageList.getSelectedRow();
    }

    private Vector<String> getColumnDisplayNames(String tableType) {
        String[] columnNames = tableDataEngine.getTableColumnNames(tableType);
        Vector<String> displayNames = new Vector<>();

        Map<String, String> columnDisplayMap = columnDisplayMaps.computeIfAbsent(tableType, type -> {
            List<TableColumn> columns = tableColumnManager.getColumns(type);
            Map<String, String> map = new HashMap<>();
            for (TableColumn column : columns) {
                map.put(column.getName(), column.getDisplayName());
            }
            return map;
        });

        for (String columnName : columnNames) {
            String displayName = columnDisplayMap.get(columnName);
            displayNames.add(displayName != null ? displayName : columnName);
        }

        return displayNames;
    }
}