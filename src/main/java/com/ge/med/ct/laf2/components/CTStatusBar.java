package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import java.awt.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 自定义状态栏，实现CT工作站风格的UI
 */
public class CTStatusBar extends JPanel {
    private static final long serialVersionUID = 1L;

    // 组件尺寸
    private static final int HEIGHT = 28;

    // 状态元素
    private final JLabel statusLabel;
    private final JLabel timeLabel;
    private final JLabel versionLabel;

    // 定时器
    private final Timer timer;

    /**
     * 创建一个状态栏
     */
    public CTStatusBar() {
        setLayout(new BorderLayout());
        setBackground(ThemeConstants.Colors.getToolbarBackground());
        setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, ThemeConstants.Colors.getBorder()));
        setPreferredSize(new Dimension(getWidth(), HEIGHT));

        // 状态标签
        statusLabel = new JLabel("就绪");
        statusLabel.setForeground(Color.WHITE);
        statusLabel.setBorder(BorderFactory.createEmptyBorder(2, 10, 2, 10));
        statusLabel.setFont(ThemeConstants.Fonts.getSmall());

        // 时间标签
        timeLabel = new JLabel();
        timeLabel.setForeground(Color.WHITE);
        timeLabel.setHorizontalAlignment(SwingConstants.RIGHT);
        timeLabel.setBorder(BorderFactory.createEmptyBorder(2, 10, 2, 10));
        timeLabel.setFont(ThemeConstants.Fonts.getSmall());
        updateTime();

        // 版本标签
        versionLabel = new JLabel(ThemeConstants.App.getVersion());
        versionLabel.setForeground(Color.WHITE);
        versionLabel.setBorder(BorderFactory.createEmptyBorder(2, 10, 2, 10));
        versionLabel.setFont(ThemeConstants.Fonts.getSmall());

        // 右侧面板
        JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
        rightPanel.setOpaque(false);
        rightPanel.add(versionLabel);
        rightPanel.add(timeLabel);

        // 添加面板到状态栏
        add(statusLabel, BorderLayout.WEST);
        add(rightPanel, BorderLayout.EAST);

        // 启动计时器更新时间
        timer = new Timer(1000, e -> updateTime());
        timer.start();
    }

    /**
     * 更新时间显示
     */
    private void updateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        timeLabel.setText(sdf.format(new Date()));
    }

    /**
     * 设置状态栏文本
     */
    public void setStatus(String status) {
        statusLabel.setText(status);
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g.create();

        // 绘制顶部分隔线
        g2d.setColor(ThemeConstants.Colors.getBorder());
        g2d.drawLine(0, 0, getWidth(), 0);

        g2d.dispose();
    }
}