package com.ge.med.ct.laf2.theming.themes;

import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.laf2.theming.Theme;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.GraphicsEnvironment;

/**
 * CT主题实现
 * 保留当前UIConstants中的所有颜色、字体和常量
 */
public class CTTheme implements Theme {
    private static final ConfigManager configManager = ConfigManager.getInstance();

    // 字体相关
    private static final String[] FONT_NAMES = {
        "Microsoft YaHei", // 微软雅黑
        "SimSun", // 宋体
        "Arial", // 通用英文字体
    };

    // 获取支持中文的字体名称
    private static final String CHINESE_FONT_NAME = findChineseFont();

    @Override
    public String getName() {
        return "ct-theme";
    }

    @Override
    public String getDisplayName() {
        return "CT主题";
    }

    // 应用程序常量
    @Override
    public String getAppName() {
        return configManager.getString("application.name", "CT质量保证工具");
    }

    @Override
    public String getAppVersion() {
        return configManager.getString("application.version", "1.0.0");
    }

    // 颜色常量 - 基础
    @Override
    public Color getPrimaryColor() {
        return new Color(97, 107, 157); // 蓝灰色主色调
    }

    @Override
    public Color getSecondaryColor() {
        return new Color(69, 85, 135); // 深蓝灰色按钮背景
    }

    @Override
    public Color getBackgroundColor() {
        return new Color(173, 183, 215); // 淡蓝灰色背景
    }

    @Override
    public Color getHeaderBackgroundColor() {
        return new Color(42, 57, 107); // 深蓝色头部背景
    }

    @Override
    public Color getPanelBackgroundColor() {
        return new Color(199, 209, 227); // 面板背景色
    }

    @Override
    public Color getTextColor() {
        return Color.BLACK;
    }

    @Override
    public Color getTextBrightColor() {
        return Color.WHITE;
    }

    @Override
    public Color getTextLightColor() {
        return new Color(42, 57, 107); // 蓝色文本
    }

    @Override
    public Color getBorderColor() {
        return new Color(111, 122, 155); // 边框颜色
    }

    // 颜色常量 - 状态
    @Override
    public Color getSuccessColor() {
        return new Color(56, 142, 60); // 成功提示色
    }

    @Override
    public Color getWarningColor() {
        return new Color(245, 124, 0); // 警告提示色
    }

    @Override
    public Color getErrorColor() {
        return new Color(211, 47, 47); // 错误提示色
    }

    // 颜色常量 - 表格
    @Override
    public Color getTableHeaderColor() {
        return new Color(111, 122, 155); // 表头背景色
    }

    @Override
    public Color getTableSelectedRowColor() {
        return getColorFromConfig(
            configManager.getString("ui.table.selected_row_color", "255, 235, 156"),
            new Color(255, 235, 156)); // 选中行颜色
    }

    @Override
    public Color getTableAlternateRowColor() {
        return new Color(217, 225, 241); // 交替行颜色
    }

    // 颜色常量 - 界面元素
    @Override
    public Color getToolbarBackgroundColor() {
        return new Color(97, 107, 157); // 工具栏背景
    }

    @Override
    public Color getMenubarColor() {
        return new Color(42, 57, 107); // 菜单栏背景色，较深蓝色
    }

    @Override
    public Color getButtonBackgroundColor() {
        return new Color(154, 166, 195); // 按钮背景色
    }

    @Override
    public Color getButtonBorderColor() {
        return new Color(111, 122, 155); // 按钮边框色
    }

    @Override
    public Color getFieldBackgroundColor() {
        return new Color(217, 225, 241); // 输入框背景色
    }

    // 颜色常量 - 列表
    @Override
    public Color getListHeaderColor() {
        return new Color(111, 122, 155); // 列表头部背景色
    }

    @Override
    public Color getListSelectedRowColor() {
        return new Color(255, 235, 156); // 选中行颜色（淡金色）
    }

    @Override
    public Color getListAlternateRowColor() {
        return new Color(217, 225, 241); // 交替行颜色
    }

    @Override
    public Color getListBackgroundColor() {
        return new Color(240, 240, 242); // 列表背景颜色
    }

    // 字体常量
    @Override
    public Font getHeaderFont() {
        return new Font(CHINESE_FONT_NAME, Font.BOLD, 16);
    }

    @Override
    public Font getTitleFont() {
        return new Font(CHINESE_FONT_NAME, Font.BOLD, 20);
    }

    @Override
    public Font getNormalFont() {
        return new Font(CHINESE_FONT_NAME, Font.PLAIN, 14);
    }

    @Override
    public Font getSmallFont() {
        return new Font(CHINESE_FONT_NAME, Font.PLAIN, 12);
    }

    @Override
    public Font getDefaultFont() {
        return new Font(CHINESE_FONT_NAME, Font.PLAIN, 13);
    }

    @Override
    public Font getLargeFont() {
        return new Font(CHINESE_FONT_NAME, Font.PLAIN, 16);
    }

    @Override
    public Font getReportFont() {
        return new Font(CHINESE_FONT_NAME, Font.PLAIN, 16);
    }

    @Override
    public Font getTableHeaderFont() {
        return new Font(CHINESE_FONT_NAME, Font.BOLD, 12);
    }

    @Override
    public Font getTableContentFont() {
        return new Font(CHINESE_FONT_NAME, Font.PLAIN, 15);
    }

    @Override
    public Font getImageIndexFont() {
        return new Font(CHINESE_FONT_NAME, Font.PLAIN, 14);
    }

    @Override
    public Font getListItemFont() {
        return getDefaultFont();
    }

    // 尺寸常量
    @Override
    public int getDefaultMargin() {
        return 12;
    }

    @Override
    public int getDefaultPadding() {
        return 8;
    }

    @Override
    public int getDefaultBorderRadius() {
        return 4;
    }

    @Override
    public int getDefaultButtonHeight() {
        return 32;
    }

    @Override
    public int getDefaultComponentHeight() {
        return 28;
    }

    @Override
    public int getDefaultIconSize() {
        return 16;
    }

    @Override
    public Dimension getDefaultButtonSize() {
        return new Dimension(120, getDefaultButtonHeight());
    }

    @Override
    public Dimension getSmallButtonSize() {
        return new Dimension(80, getDefaultButtonHeight());
    }

    @Override
    public Dimension getLargeButtonSize() {
        return new Dimension(160, getDefaultButtonHeight());
    }

    // 图标尺寸
    @Override
    public int getIconSmall() {
        return 16;
    }

    @Override
    public int getIconMedium() {
        return 24;
    }

    @Override
    public int getIconLarge() {
        return 32;
    }

    // 窗口尺寸
    @Override
    public Dimension getDefaultWindowSize() {
        return new Dimension(1024, 768);
    }

    @Override
    public Dimension getDialogSize() {
        return new Dimension(400, 300);
    }

    // 动画常量
    @Override
    public int getAnimationDuration() {
        return 200; // 毫秒
    }

    /**
     * 查找系统支持的中文字体
     */
    private static String findChineseFont() {
        // 尝试系统中定义的每一个字体
        for (String fontName : FONT_NAMES) {
            try {
                Font testFont = new Font(fontName, Font.PLAIN, 12);
                if (testFont.canDisplay('中') && testFont.canDisplay('文')) {
                    return fontName;
                }
            } catch (Exception e) {
                // 忽略不可用的字体
            }
        }

        // 如果以上都失败，尝试系统所有字体
        Font[] allFonts = GraphicsEnvironment.getLocalGraphicsEnvironment().getAllFonts();
        for (Font font : allFonts) {
            if (font.canDisplay('中') && font.canDisplay('文')) {
                return font.getName();
            }
        }

        // 兜底方案：使用Java默认字体
        return Font.SANS_SERIF;
    }

    /**
     * 从配置的RGB字符串创建Color对象
     */
    private static Color getColorFromConfig(String rgbStr, Color defaultColor) {
        try {
            String[] rgb = rgbStr.split(",");
            if (rgb.length == 3) {
                return new Color(
                        Integer.parseInt(rgb[0].trim()),
                        Integer.parseInt(rgb[1].trim()),
                        Integer.parseInt(rgb[2].trim()));
            }
        } catch (Exception e) {
            // 如果解析失败，返回默认颜色
        }
        return defaultColor;
    }
}
