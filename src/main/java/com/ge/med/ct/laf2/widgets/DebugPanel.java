package com.ge.med.ct.laf2.widgets;

import com.ge.med.ct.laf2.theming.ThemeConstants;
import com.ge.med.ct.service.LogManager;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.text.BadLocationException;
import javax.swing.text.DefaultHighlighter;
import java.awt.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DebugPanel extends JPanel {
    private static final Logger LOG = LogManager.getInstance().getLogger(DebugPanel.class);
    private final CustomTextArea debugTextArea;

    // 使用主题颜色
    private static final Color BLUE_MARKER = ThemeConstants.Colors.getTextLight();
    private static final Color RED_MARKER = ThemeConstants.Colors.getError();
    private static final Color CYAN_MARKER = ThemeConstants.Colors.getSuccess();

    public DebugPanel() {
        setLayout(new BorderLayout());

        debugTextArea = new CustomTextArea();
        debugTextArea.setEditable(false);
        debugTextArea.setEnabled(true);
        debugTextArea.setFocusable(true);
        debugTextArea.setHighlighter(new DefaultHighlighter());

        JScrollPane scrollPane = new JScrollPane(debugTextArea);
        scrollPane.getVerticalScrollBar().setUnitIncrement(16);
        scrollPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        add(scrollPane, BorderLayout.CENTER);
    }

    public void appendText(String text) {
        debugTextArea.appendWithMarker(text + "\n");
        debugTextArea.setCaretPosition(debugTextArea.getDocument().getLength());
    }
    
    /**
     * 记录带颜色的日志信息
     * @param text 日志文本
     * @param color 文本颜色
     */
    public void log(String text, Color color) {
        // 使用普通文本追加方式记录日志
        // 颜色只影响标记，保持一致的文本样式
        appendText(text);
    }

    public void clearText() {
        if (debugTextArea != null) {
            debugTextArea.setText("");
        }
    }

    private static class CustomTextArea extends JTextArea {
        private static final int MARKER_WIDTH = 10;

        public CustomTextArea() {
            super();
            setMargin(new Insets(0, MARKER_WIDTH + 20, 0, 0));
            setFont(ThemeConstants.Fonts.getNormal());
        }

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            try {
                int startOffset = viewToModel(new Point(0, 0));
                int endOffset = viewToModel(new Point(0, getHeight()));
                int startLine = getLineOfOffset(startOffset);
                int endLine = getLineOfOffset(endOffset);

                for (int line = startLine; line <= endLine; line++) {
                    int lineStart = getLineStartOffset(line);
                    String lineText = getText(lineStart, getLineEndOffset(line) - lineStart);
                    Rectangle lineRect = modelToView(lineStart);

                    if (lineRect != null) {
                        // 绘制行号
                        g2d.setColor(ThemeConstants.Colors.getTextLight());
                        String lineNumber = String.format("%3d", line + 1);
                        g2d.drawString(lineNumber, MARKER_WIDTH, lineRect.y + lineRect.height - 5);

                        // 绘制标记
                        Color markerColor = getMarkerColor(lineText);
                        if (markerColor != null) {
                            g2d.setColor(markerColor);
                            g2d.fillRect(2, lineRect.y + 2, MARKER_WIDTH - 5, lineRect.height - 5);
                        }
                    }
                }
            } catch (BadLocationException e) {
                LOG.log(Level.SEVERE, "Error painting debug text area", e);
            }

            g2d.dispose();
        }

        public void appendWithMarker(String text) {
            append(text);
            repaint();
        }

        private Color getMarkerColor(String text) {
            String lowerText = text.toLowerCase();
            if (lowerText.contains("-text")) {
                return BLUE_MARKER;
            } else if (lowerText.contains("error") || lowerText.contains("err") || lowerText.contains("fail")) {
                return RED_MARKER;
            } else if (lowerText.contains("success") || lowerText.contains("pass") || lowerText.contains("true")) {
                return CYAN_MARKER;
            }
            return null;
        }
    }
}
