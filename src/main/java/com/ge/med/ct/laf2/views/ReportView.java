package com.ge.med.ct.laf2.views;

import com.ge.med.ct.analysis.model.AnalysisResult;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IReportViewListener;
import com.ge.med.ct.laf2.base.views.BaseView;
import com.ge.med.ct.laf2.base.views.IReportView;
import com.ge.med.ct.laf2.components.CTJScrollPane;
import com.ge.med.ct.laf2.components.CTListView;
import com.ge.med.ct.laf2.components.CTPanel;
import com.ge.med.ct.laf2.widgets.ImagePanel;
import com.ge.med.ct.service.StatusIconProvider;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.util.List;
import java.util.logging.Logger;

/**
 * 报告视图实现
 */
public class ReportView extends BaseView<IReportViewListener> implements IReportView {
    private static final long serialVersionUID = 1L;

    private JTextArea repTextArea = new JTextArea();
    private CTPanel reportPanel;
    private CTListView checkedListView;
    private ImagePanel imagePanel;
    private static final Logger logger = Logger.getLogger(ReportView.class.getName());

    public ReportView() {

        super(JSplitPane.HORIZONTAL_SPLIT);
        initializeComponents();

        setupLayout();
        setupListeners();
    }

    private void initializeComponents() {
        // 设置报告文本区域
        imagePanel = new ImagePanel();
        imagePanel.setMinimumSize(new Dimension(200, 150));

        // 初始化报告文本区域
        repTextArea = new JTextArea();
        repTextArea.setEditable(false);
        repTextArea.setLineWrap(false);
        repTextArea.setWrapStyleWord(true);
        repTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 16));
        repTextArea.setBorder(BorderFactory.createEmptyBorder(5, 20, 5, 20));

        // 初始化面板组件
        reportPanel = new CTPanel(CTPanel.PanelType.HEADER);
        reportPanel.setTitle("Report Details");
        reportPanel.getContentPanel().setLayout(new BorderLayout());
        reportPanel.setPadding(0);

        CTPanel paperPanel = new CTPanel(CTPanel.PanelType.PAPER);
        paperPanel.setBorderWidth(3);
        paperPanel.setHasShadow(true);
        paperPanel.getContentPanel().add(new CTJScrollPane(repTextArea), BorderLayout.CENTER);

        reportPanel.add(paperPanel);
        // 初始化检查项列表
        checkedListView = new CTListView("Analysis Results");
        checkedListView.setBorder(null);
    }

    private void setupLayout() {

        CTPanel leftPanel = new CTPanel(new BorderLayout());
        leftPanel.setBorder(new EmptyBorder(0, 5, 0, 5));

        JSplitPane verticalSplit = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        verticalSplit.setTopComponent(checkedListView);
        verticalSplit.setBottomComponent(imagePanel);
        verticalSplit.setResizeWeight(0.3);
        verticalSplit.setDividerSize(5);

        leftPanel.add(verticalSplit, BorderLayout.CENTER);

        setLeftComponent(leftPanel);
        setRightComponent(reportPanel);
        setDividerSize(5);
        setDividerLocation(380);
        setResizeWeight(LEFT_RIGHT_RATIO);
        setContinuousLayout(true);
    }

    private void setupListeners() {
        checkedListView.setOnItemSelected(item -> {
            if (item != null) {
                logger.fine("Item selected: " + item.getTitle());
                IReportViewListener listener = getViewListener();
                if (listener != null) {
                    listener.onCheckItemSelected(item);
                } else {
                    logger.warning("View listener is null, cannot notify about selected item");
                }
            } else {
                logger.warning("Selected item is null");
            }
        });
    }

    @Override
    public void displayReport(String content) {
        SwingUtilities.invokeLater(() -> {
            repTextArea.setText(content != null ? content : "");
            repTextArea.setCaretPosition(0);
        });
    }

    @Override
    public void loadImages(List<String> imagePaths) {
        SwingUtilities.invokeLater(() -> {
            if (imagePaths == null || imagePaths.isEmpty()) {
                imagePanel.clearImages();
            } else {
                imagePanel.loadImages(imagePaths);
            }
        });
    }

    @Override
    public void setHasData(boolean hasData) {
        // 可以在这里添加数据状态的视觉反馈
        checkedListView.setEnabled(hasData);
        repTextArea.setEnabled(hasData);
        imagePanel.setEnabled(hasData);
    }

    @Override
    public void setLoading(boolean loading) {
        SwingUtilities.invokeLater(() -> {
            // 可以在这里添加加载状态的视觉反馈，如进度条或加载指示器
            if (loading) {
                showMessage(MessageType.INFO, "正在加载数据...");
            }
        });
    }

    @Override
    public void clearView() {
        SwingUtilities.invokeLater(() -> {
            checkedListView.clearItems();
            repTextArea.setText("");
            imagePanel.clearImages();
        });
    }

    @Override
    public void addCheckItem(String itemId, String title, AnalysisResult response) {
        SwingUtilities.invokeLater(() -> {
            CTListView.ListItem item = new CTListView.ListItem(
                    StatusIconProvider.getStatusIcon(response.getStatus()),
                    title,
                    response.getStatus(),
                    response.getImagePaths(),
                    response.getReportFile());
            item.setId(itemId);
            checkedListView.addItem(item);
        });
    }
}
