package com.ge.med.ct.laf2;

import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.dicom2.service.DicomDataService;
import com.ge.med.ct.dicom2.service.DicomStorageService;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.event.ExceptionEvent;
import com.ge.med.ct.exception.message.UIMessages;
import com.ge.med.ct.laf2.base.CTLookAndFeel;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.components.*;
import com.ge.med.ct.laf2.presenter.AnalysisPresenter;
import com.ge.med.ct.laf2.presenter.DicomInsightPresenter;
import com.ge.med.ct.laf2.presenter.ReportPresenter;
import com.ge.med.ct.laf2.utils.*;
import com.ge.med.ct.laf2.theming.ThemeConstants;
import com.ge.med.ct.laf2.views.*;
import com.ge.med.ct.service.LogManager;
import com.ge.med.ct.service.TableOperations;

import javax.swing.*;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.List;
import java.util.logging.Logger;

/**
 * CT质量保证工具主应用程序
 */
public class CTQAssuranceTool extends JFrame {
    private static final Logger LOG = LogManager.getInstance().getLogger(CTQAssuranceTool.class);
    private static final long serialVersionUID = 1L;
    private static final int WINDOW_WIDTH = 1150;
    private static final int WINDOW_HEIGHT = 800;
    private static final int MENU_BAR_HEIGHT = 60;
    private static final int BUTTON_WIDTH = 100;
    private static final int BUTTON_HEIGHT = 38;
    private static final int DICOM_BUTTON_WIDTH = 120;
    private static final int MENU_BAR_PADDING = 30;
    private static final int MENU_BAR_VERTICAL_PADDING = 5;

    private final AnalysisView analysisView;
    private final ReportView reportView;
    private final DicomInsightView dicomInsightView;
    private final AnalysisPresenter analysisPresenter;
    private final ReportPresenter reportPresenter;
    private final DicomInsightPresenter dicomInsightPresenter;
    private final CardLayout contentLayout;
    private final JPanel directPanel;
    private final CTPanel contentPanel;
    private final CTPanel menuBar;
    private final CTStatusBar statusBar;
    private DicomDataService dicomService;
    private DicomDataProvider dicomProvider;

    public CTQAssuranceTool() {
        CTLookAndFeel.applyLookAndFeel();
        SwingUtilities.updateComponentTreeUI(this);

        this.analysisView = new AnalysisView();
        this.reportView = new ReportView();
        this.dicomInsightView = new DicomInsightView();
        this.reportPresenter = new ReportPresenter(reportView);
        this.analysisPresenter = new AnalysisPresenter(analysisView, reportPresenter);
        this.dicomInsightPresenter = new DicomInsightPresenter(dicomInsightView);
        this.contentLayout = new CardLayout();
        this.directPanel = new JPanel(contentLayout);
        this.contentPanel = new CTPanel();
        this.menuBar = new CTPanel(new FlowLayout(FlowLayout.LEFT, MENU_BAR_PADDING, MENU_BAR_VERTICAL_PADDING));
        this.statusBar = new CTStatusBar();

        initializeUI();
        initializeDicomData();
    }

    @HandleException(errorCode = ErrorCode.UI_INIT)
    private void initializeUI() {
        setTitle(ThemeConstants.App.getName());
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        setSize(WINDOW_WIDTH, WINDOW_HEIGHT);
        setLocationRelativeTo(null);

        setupMenuBar();
        setupContentPanel();
        setupWindowListener();

        add(menuBar, BorderLayout.NORTH);
        add(contentPanel, BorderLayout.CENTER);
        add(statusBar, BorderLayout.SOUTH);
    }

    private void setupMenuBar() {
        menuBar.setPreferredSize(new Dimension(0, MENU_BAR_HEIGHT));
        menuBar.setBackground(ThemeConstants.Colors.getToolbarBackground());

        CTButton analysisButton = createMenuButton("分析", "analysis", BUTTON_WIDTH);
        CTButton reportButton = createMenuButton("报告", "report", BUTTON_WIDTH);
        CTButton dicomDatabaseButton = createMenuButton("DICOM数据库", "dicomdb", DICOM_BUTTON_WIDTH);
        CTButton exportJsonButton = ComponentFactory.createButton("导出JSON", e -> exportToJson(),
                new Dimension(BUTTON_WIDTH, BUTTON_HEIGHT));

        menuBar.add(analysisButton);
        menuBar.add(reportButton);
        menuBar.add(dicomDatabaseButton);
        menuBar.add(exportJsonButton);
    }

    private CTButton createMenuButton(String text, String viewName, int width) {
        return ComponentFactory.createButton(text, e -> switchToView(viewName),
                new Dimension(width, BUTTON_HEIGHT));
    }

    private void setupContentPanel() {
        directPanel.setBackground(ThemeConstants.Colors.getPanelBackground());
        directPanel.add(analysisView, "analysis");
        directPanel.add(reportView, "report");
        directPanel.add(dicomInsightView, "dicomdb");

        contentPanel.setContent(directPanel);
        contentLayout.show(directPanel, "analysis");
        statusBar.setStatus("分析视图已加载");
    }

    private void setupWindowListener() {
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                cleanup();
            }
        });
    }

    private void initializeDicomData() {
        new SwingWorker<DicomDataService, String>() {
            @Override
            protected DicomDataService doInBackground() {
                try {
                    publish("正在扫描DICOM目录...");
                    dicomService = DicomDataService.getInstance();
                    loadDicomData();
                    return dicomService;
                } catch (Exception e) {
                    publish("数据初始化失败: " + e.getMessage());
                    throw e;
                }
            }

            private void loadDicomData() {
                try {
                    dicomService.loadDicomData(null, progress -> {
                        if (progress.getTotal() > 0) {
                            int percentage = (int) ((progress.getProcessed() * 100.0) / progress.getTotal());
                            publish(String.format("正在加载DICOM文件... %d/%d (%d%%)",
                                    progress.getProcessed(),
                                    progress.getTotal(),
                                    percentage));
                        }
                    });
                    dicomProvider = dicomService.getDicomProvider();
                    publish("DICOM数据加载完成");
                } catch (Exception e) {
                    throw new RuntimeException("加载DICOM数据失败", e);
                }
            }

            @Override
            protected void process(List<String> chunks) {
                if (!chunks.isEmpty()) {
                    statusBar.setStatus(chunks.get(chunks.size() - 1));
                }
            }

            @Override
            protected void done() {
                try {
                    DicomDataService service = get();
                    if (service != null) {
                        setupPresenters();
                        statusBar.setStatus("DICOM数据加载完成");
                    }
                } catch (Exception e) {
                    handleDicomLoadError(e);
                }
            }

            private void setupPresenters() {
                analysisPresenter.setDicomProvider(dicomProvider);
                dicomInsightPresenter.setDicomProvider(dicomProvider);
            }

            private void handleDicomLoadError(Exception e) {
                statusBar.setStatus("DICOM数据加载失败");
                analysisView.showMessage(MessageType.ERROR, "DICOM数据加载失败: " + e.getMessage());
            }
        }.execute();
    }

    @HandleException(errorCode = ErrorCode.DISPLAY)
    private void switchToView(String viewName) {
        contentLayout.show(directPanel, viewName);
        updateStatusBar(viewName);
        directPanel.revalidate();
        directPanel.repaint();
    }

    private void updateStatusBar(String viewName) {
        switch (viewName) {
            case "report":
                statusBar.setStatus("已切换到报告视图");
                break;
            case "analysis":
                statusBar.setStatus("已切换到分析视图");
                break;
            case "dicomdb":
                statusBar.setStatus("已切换到DICOM数据库视图");
                break;
        }
    }

    @HandleException(errorCode = ErrorCode.OPERATION)
    private void exportToJson() {
        try {
            DicomStorageService storageService = DicomStorageService.getInstance();
            String jsonPath = storageService.exportToJson(dicomProvider, "dicom_export");
            statusBar.setStatus("DICOM数据已导出到: " + jsonPath);
        } catch (Exception e) {
            handleExportError(e);
        }
    }

    private void handleExportError(Exception e) {
        String errorMessage = "DICOM数据导出失败: " + e.getMessage();
        statusBar.setStatus(errorMessage);
        LOG.warning("导出失败: " + e.getMessage());
    }

    @HandleException(errorCode = ErrorCode.OPERATION)
    private void cleanup() {
        destroyViews();
        closeServices();
        TableOperations.shutdown();
    }

    private void destroyViews() {
        if (analysisView != null)
            analysisView.destroy();
        if (reportView != null)
            reportView.destroy();
        if (dicomInsightView != null)
            dicomInsightView.destroy();
    }

    private void closeServices() {
        if (dicomService != null)
            dicomService.close();
    }

    private static void showErrorDialog(QAToolException ex) {
        MessageDisplayer.showException(null, ex);
    }

    @HandleException(errorCode = ErrorCode.OPERATION)
    public static void main(String[] args) {
        setupUncaughtExceptionHandler();
        SwingUtilities.invokeLater(() -> {
            try {
                new CTQAssuranceTool().setVisible(true);
            } catch (Exception ex) {
                handleStartupError(ex);
            }
        });
    }

    private static void setupUncaughtExceptionHandler() {
        Thread.setDefaultUncaughtExceptionHandler((t, throwable) -> {
            QAToolException qaEx = throwable instanceof QAToolException
                    ? (QAToolException) throwable
                    : new QAToolException(ErrorCode.UI_INIT, UIMessages.UNKNOWN_ERROR, throwable);

            SwingUtilities.invokeLater(() -> showErrorDialog(qaEx));
            EventBus.getInstance().post(ExceptionEvent.fromException(qaEx));
        });
    }

    private static void handleStartupError(Exception ex) {
        QAToolException qaEx = new QAToolException(ErrorCode.UI_INIT, UIMessages.DISPLAY_ERROR, ex);
        EventBus.getInstance().post(ExceptionEvent.fromException(qaEx));
        System.exit(1);
    }
}
