package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.TreeModel;
import javax.swing.tree.TreeSelectionModel;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.time.LocalDateTime;

public class FlatTreeView extends JTree {
    private static final int ROW_HEIGHT = 40;
    private static final int THUMBNAIL_SIZE = 32;
    private static final int VERTICAL_GAP = 4;
    private static final int HORIZONTAL_GAP = 8;
    private static final int PADDING = 5;
    private static final int DOT_LINE_WIDTH = 3;
    private static final float[] DASH_PATTERN = { 2f, 2f };

    public FlatTreeView() {
        super(new DefaultMutableTreeNode());
        initializeStyle();
    }

    public FlatTreeView(TreeModel model) {
        super(model);
        initializeStyle();
    }

    private void initializeStyle() {
        setBackground(ThemeConstants.Colors.getPanelBackground());
        setForeground(ThemeConstants.Colors.getText());
        setRowHeight(ROW_HEIGHT);
        setShowsRootHandles(true);
        setRootVisible(false);
        setBorder(BorderFactory.createEmptyBorder(PADDING, PADDING, PADDING, PADDING));

        // 自定义单元格渲染器
        setCellRenderer(new DicomTreeCellRenderer());

        // 选择模式
        getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);

        // 设置展开/折叠图标
        UIManager.put("Tree.expandedIcon", createIcon("expanded"));
        UIManager.put("Tree.collapsedIcon", createIcon("collapsed"));
    }

    private Icon createIcon(String type) {
        int size = 12;
        BufferedImage image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        g2d.setColor(ThemeConstants.Colors.getText());
        if ("expanded".equals(type)) {
            g2d.fillPolygon(new int[] { 2, size - 2, size / 2 },
                    new int[] { size / 2, size / 2, 2 }, 3);
        } else {
            g2d.fillPolygon(new int[] { 2, size - 2, size / 2 },
                    new int[] { 2, 2, size - 2 }, 3);
        }

        g2d.dispose();
        return new ImageIcon(image);
    }

    private class DicomTreeCellRenderer extends DefaultTreeCellRenderer {
        private final Font regularFont;
        private final Font timeFont;
        private final Color dotColor;

        public DicomTreeCellRenderer() {
            regularFont = new Font("Segoe UI", Font.PLAIN, 12);
            timeFont = new Font("Segoe UI", Font.PLAIN, 11);
            dotColor = ThemeConstants.Colors.getText().darker();

            setBackgroundNonSelectionColor(ThemeConstants.Colors.getPanelBackground());
            setBackgroundSelectionColor(ThemeConstants.Colors.getBackground());
            setTextNonSelectionColor(ThemeConstants.Colors.getText());
            setTextSelectionColor(ThemeConstants.Colors.getText());
            setBorderSelectionColor(null);
        }

        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value,
                boolean selected, boolean expanded, boolean leaf, int row, boolean hasFocus) {
            JPanel panel = new JPanel(new BorderLayout(HORIZONTAL_GAP, VERTICAL_GAP));
            panel.setBackground(selected ? ThemeConstants.Colors.getBackground()
                    : ThemeConstants.Colors.getPanelBackground());

            if (value instanceof DefaultMutableTreeNode) {
                DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
                Object userObject = node.getUserObject();

                if (userObject instanceof TreeNodeData) {
                    TreeNodeData nodeData = (TreeNodeData) userObject;

                    // 创建主要内容面板
                    JPanel contentPanel = new JPanel(new BorderLayout(HORIZONTAL_GAP, 0));
                    contentPanel.setBackground(panel.getBackground());

                    // 左侧图标/缩略图面板
                    JPanel iconPanel = new JPanel(new BorderLayout());
                    iconPanel.setBackground(contentPanel.getBackground());
                    iconPanel.setPreferredSize(new Dimension(THUMBNAIL_SIZE + 4, THUMBNAIL_SIZE));

                    if (nodeData.isFolder) {
                        JLabel folderIcon = new JLabel(getFolderIcon());
                        iconPanel.add(folderIcon, BorderLayout.CENTER);
                    } else {
                        JLabel thumbnailLabel = new JLabel();
                        thumbnailLabel.setPreferredSize(new Dimension(THUMBNAIL_SIZE, THUMBNAIL_SIZE));
                        if (nodeData.thumbnail != null) {
                            thumbnailLabel.setIcon(new ImageIcon(nodeData.thumbnail));
                        } else {
                            thumbnailLabel.setIcon(getFileIcon());
                        }
                        iconPanel.add(thumbnailLabel, BorderLayout.CENTER);
                    }

                    // 中间文本面板
                    JPanel textPanel = new JPanel(new BorderLayout(0, 2));
                    textPanel.setBackground(contentPanel.getBackground());

                    // 文件名标签
                    JLabel nameLabel = new JLabel(nodeData.label);
                    nameLabel.setFont(regularFont);
                    nameLabel.setForeground(ThemeConstants.Colors.getText());

                    textPanel.add(nameLabel, BorderLayout.CENTER);

                    // 如果是叶子节点，添加时间信息
                    if (!nodeData.isFolder) {
                        JLabel timeLabel = new JLabel(nodeData.getTimeString());
                        timeLabel.setFont(timeFont);
                        timeLabel.setForeground(ThemeConstants.Colors.getText().darker());
                        textPanel.add(timeLabel, BorderLayout.SOUTH);
                    }

                    contentPanel.add(iconPanel, BorderLayout.WEST);
                    contentPanel.add(textPanel, BorderLayout.CENTER);

                    // 添加连接线装饰
                    if (!nodeData.isFolder) {
                        JPanel dotLinePanel = new JPanel() {
                            @Override
                            protected void paintComponent(Graphics g) {
                                super.paintComponent(g);
                                Graphics2D g2 = (Graphics2D) g;
                                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                                g2.setColor(dotColor);
                                g2.setStroke(new BasicStroke(1, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL, 0,
                                        DASH_PATTERN, 0));
                                g2.drawLine(0, getHeight() / 2, DOT_LINE_WIDTH, getHeight() / 2);
                            }
                        };
                        dotLinePanel.setOpaque(false);
                        dotLinePanel.setPreferredSize(new Dimension(DOT_LINE_WIDTH + 1, THUMBNAIL_SIZE));
                        panel.add(dotLinePanel, BorderLayout.WEST);
                    }

                    panel.add(contentPanel, BorderLayout.CENTER);
                }
            }

            return panel;
        }

        private Icon getFolderIcon() {
            ImageIcon icon = (ImageIcon) UIManager.getIcon("FileView.directoryIcon");
            if (icon != null) {
                return new ImageIcon(
                        icon.getImage().getScaledInstance(THUMBNAIL_SIZE, THUMBNAIL_SIZE, Image.SCALE_SMOOTH));
            }
            return null;
        }

        private Icon getFileIcon() {
            ImageIcon icon = (ImageIcon) UIManager.getIcon("FileView.fileIcon");
            if (icon != null) {
                return new ImageIcon(
                        icon.getImage().getScaledInstance(THUMBNAIL_SIZE, THUMBNAIL_SIZE, Image.SCALE_SMOOTH));
            }
            return null;
        }
    }

    public static class TreeNodeData {
        public final String label;
        public final boolean isFolder;
        public final String path;
        public final LocalDateTime timestamp;
        public final BufferedImage thumbnail;

        public TreeNodeData(String label, boolean isFolder, String path, LocalDateTime timestamp,
                BufferedImage thumbnail) {
            this.label = label;
            this.isFolder = isFolder;
            this.path = path;
            this.timestamp = timestamp;
            this.thumbnail = thumbnail;
        }

        public TreeNodeData(String label, boolean isFolder, String path, LocalDateTime timestamp) {
            this(label, isFolder, path, timestamp, null);
        }

        public String getTimeString() {
            if (timestamp != null) {
                return timestamp.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss"));
            }
            return "";
        }

        @Override
        public String toString() {
            if (isFolder) {
                return String.format("Date & Time: %s, %s",
                        timestamp.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd, HH:mm:ss")),
                        label);
            }
            return label;
        }
    }
}