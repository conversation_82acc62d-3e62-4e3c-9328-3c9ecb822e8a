package com.ge.med.ct.laf2.widgets;

import com.ge.med.ct.laf2.components.CTPanel;
import com.ge.med.ct.laf2.theming.ThemeConstants;
import com.ge.med.ct.service.LogManager;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Image display panel for showing images with navigation controls
 */
public class ImagePanel extends CTPanel {
    private static final long serialVersionUID = 1L;
    private static final Color CONTROL_PANEL_BG = new Color(240, 240, 240, 220);
    private static final int MAX_IMAGE_WIDTH = 800;

    private final Logger logger = LogManager.getInstance().getLogger(ImagePanel.class);
    private final ExecutorService imageLoader = Executors.newSingleThreadExecutor();
    private final JPanel imageDisplayPanel;
    private final JPanel controlPanel;
    private final JButton prevButton;
    private final JButton nextButton;
    private final JLabel indexLabel;
    private final BufferedImage loadingImage;

    private int currentIndex = 0;
    private BufferedImage currentImage;
    private List<BufferedImage> images = new ArrayList<>();
    private List<String> currentImagePaths = new ArrayList<>();
    private Future<?> currentLoading;

    public ImagePanel() {
        super(PanelType.FLAT);

        // Initialize components in correct order
        loadingImage = createLoadingImage();
        prevButton = createNavigationButton("←", this::showPreviousImage);
        nextButton = createNavigationButton("→", this::showNextImage);
        indexLabel = createIndexLabel();
        controlPanel = createControlPanel();
        imageDisplayPanel = createImageDisplayPanel();

        // Ensure contentPanel is initialized and has proper layout
        JPanel content = new JPanel(new BorderLayout());
        content.setOpaque(false);
        setContent(content);

        // Setup layout after all components are initialized
        setupLayout();
    }

    /**
     * 创建图像显示面板
     */
    private JPanel createImageDisplayPanel() {
        return new JPanel(new BorderLayout()) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                if (currentImage == null)
                    return;
                Graphics2D g2d = (Graphics2D) g;
                configureGraphics(g2d);
                int pWidth = getWidth();
                int pHeight = getHeight();
                int iWidth = currentImage.getWidth();
                int iHeight = currentImage.getHeight();
                if (iWidth <= 0 || iHeight <= 0)
                    return;
                double scale = Math.min((double) pWidth / iWidth, (double) pHeight / iHeight);
                int sWidth = (int) (iWidth * scale);
                int sHeight = (int) (iHeight * scale);
                int x = (pWidth - sWidth) / 2;
                int y = (pHeight - sHeight) / 2;
                g2d.drawImage(currentImage, x, y, sWidth, sHeight, null);
            }
        };
    }

    /**
     * 配置绘图上下文以获得最佳质量
     */
    private void configureGraphics(Graphics2D g2d) {
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
    }

    /**
     * 创建控制面板
     */
    private JPanel createControlPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(CONTROL_PANEL_BG);
        panel.setOpaque(true);

        // Add components to panel
        panel.add(prevButton, BorderLayout.WEST);
        panel.add(indexLabel, BorderLayout.CENTER);
        panel.add(nextButton, BorderLayout.EAST);

        return panel;
    }

    /**
     * 创建导航按钮
     */
    private JButton createNavigationButton(String text, Runnable action) {
        JButton button = new JButton(text);
        button.addActionListener(e -> action.run());
        return button;
    }

    /**
     * 创建索引标签
     */
    private JLabel createIndexLabel() {
        JLabel label = new JLabel("0/0", SwingConstants.CENTER);
        label.setFont(ThemeConstants.Fonts.getImageIndex());
        return label;
    }

    /**
     * 创建加载指示图像
     */
    private BufferedImage createLoadingImage() {
        BufferedImage img = new BufferedImage(100, 100, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = img.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setColor(new Color(240, 240, 240, 180));
        g2d.fillRect(0, 0, 100, 100);
        g2d.setColor(Color.GRAY);
        g2d.drawString("Loading...", 25, 50);
        g2d.dispose();
        return img;
    }

    /**
     * 设置面板布局
     */
    private void setupLayout() {
        getContentPanel().setLayout(new BorderLayout());
        getContentPanel().add(imageDisplayPanel, BorderLayout.CENTER);
        getContentPanel().add(controlPanel, BorderLayout.SOUTH);
        setNavigationVisible(false);
    }

    /**
     * 设置导航按钮的可见性
     */
    private void setNavigationVisible(boolean visible) {
        prevButton.setVisible(visible);
        nextButton.setVisible(visible);
    }

    /**
     * 更新图片索引标签
     */
    private void updateIndexLabel() {
        String text = images.isEmpty() ? "0/0" : (currentIndex + 1) + "/" + images.size();
        indexLabel.setText(text);
    }

    /**
     * 加载指定路径的图片
     */
    public void loadImages(List<String> imagePaths) {
        if (imagePaths == null || imagePaths.isEmpty()) {
            clearImages();
            return;
        }
        if (hasSameImagePaths(imagePaths))
            return;
        cancelCurrentLoading();
        currentImagePaths = new ArrayList<>(imagePaths);
        if (currentImage == null) {
            currentImage = loadingImage;
            imageDisplayPanel.repaint();
        }
        currentLoading = imageLoader.submit(() -> loadImagesAsync(imagePaths));
    }

    /**
     * 异步加载图片
     */
    private void loadImagesAsync(List<String> paths) {
        try {
            List<BufferedImage> loadedImages = new ArrayList<>();
            final boolean[] hasLoadedAny = { false };
            for (String path : paths) {
                BufferedImage img = loadImageFromFile(path);
                if (img != null) {
                    loadedImages.add(img);
                    hasLoadedAny[0] = true;
                }
            }
            SwingUtilities.invokeLater(() -> {
                if (hasLoadedAny[0]) {
                    images = loadedImages;
                    currentIndex = 0;
                    currentImage = images.get(0);
                    updateIndexLabel();
                    setNavigationVisible(images.size() > 1);
                } else {
                    clearImages();
                    showMessage("No images to display");
                }
                imageDisplayPanel.repaint();
            });
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "Error loading images", ex);
            SwingUtilities.invokeLater(() -> showErrorMessage("Error loading images"));
        }
    }

    /**
     * 从文件加载单张图片
     */
    private BufferedImage loadImageFromFile(String path) {
        try {
            File file = new File(path);
            if (!file.exists() || !file.isFile()) {
                logger.warning("Image file not found: " + path);
                return null;
            }
            BufferedImage original = ImageIO.read(file);
            if (original == null)
                return null;
            return createOptimizedImage(original);
        } catch (IOException e) {
            logger.log(Level.SEVERE, "Failed to load image file: " + path, e);
            return null;
        }
    }

    /**
     * 创建优化后的图像
     */
    private BufferedImage createOptimizedImage(BufferedImage src) {
        int srcWidth = src.getWidth();
        if (srcWidth <= MAX_IMAGE_WIDTH)
            return src;
        int targetHeight = (int) (src.getHeight() * ((double) MAX_IMAGE_WIDTH / srcWidth));
        BufferedImage scaled = new BufferedImage(MAX_IMAGE_WIDTH, targetHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = scaled.createGraphics();
        configureGraphics(g2d);
        g2d.drawImage(src, 0, 0, MAX_IMAGE_WIDTH, targetHeight, null);
        g2d.dispose();
        return scaled;
    }

    /**
     * 显示上一张图片
     */
    private void showPreviousImage() {
        if (images.isEmpty())
            return;
        currentIndex = (currentIndex - 1 + images.size()) % images.size();
        currentImage = images.get(currentIndex);
        updateIndexLabel();
        imageDisplayPanel.repaint();
    }

    /**
     * 显示下一张图片
     */
    private void showNextImage() {
        if (images.isEmpty())
            return;
        currentIndex = (currentIndex + 1) % images.size();
        currentImage = images.get(currentIndex);
        updateIndexLabel();
        imageDisplayPanel.repaint();
    }

    /**
     * 清除所有图片
     */
    public void clearImages() {
        cancelCurrentLoading();
        currentImage = null;
        images.clear();
        currentImagePaths.clear();
        currentIndex = 0;
        updateIndexLabel();
        setNavigationVisible(false);
        imageDisplayPanel.repaint();
    }

    /**
     * 检查是否具有相同的图片路径
     */
    private boolean hasSameImagePaths(List<String> paths) {
        if (currentImagePaths.size() != (paths != null ? paths.size() : 0))
            return false;
        for (int i = 0; i < currentImagePaths.size(); i++) {
            if (!Objects.equals(currentImagePaths.get(i), paths.get(i)))
                return false;
        }
        return true;
    }

    /**
     * 显示消息
     */
    public void showMessage(String message) {
        showMessage(message, ThemeConstants.Colors.getText());
    }

    /**
     * 显示错误消息
     */
    public void showErrorMessage(String message) {
        showMessage(message, Color.RED);
    }

    private void showMessage(String message, Color color) {
        clearImages();
        JLabel label = new JLabel(message, SwingConstants.CENTER);
        label.setFont(ThemeConstants.Fonts.getDefault());
        label.setForeground(color);

        getContentPanel().removeAll();
        getContentPanel().setLayout(new BorderLayout());
        getContentPanel().add(label, BorderLayout.CENTER);
        getContentPanel().add(controlPanel, BorderLayout.SOUTH);

        revalidate();
        repaint();
    }

    /**
     * 取消当前的加载任务
     */
    private void cancelCurrentLoading() {
        if (currentLoading != null && !currentLoading.isDone()) {
            currentLoading.cancel(true);
        }
    }
}