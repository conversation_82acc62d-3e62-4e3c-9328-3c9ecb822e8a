package com.ge.med.ct.laf2.widgets;

import com.ge.med.ct.laf2.theming.ThemeManager;
import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import java.awt.*;

/**
 * 主题选择器组件
 * 提供主题切换功能的UI组件
 */
public class ThemeSelector extends JPanel {
    private final JComboBox<String> themeComboBox;
    private final ThemeManager themeManager = ThemeManager.getInstance();
    
    public ThemeSelector() {
        setLayout(new FlowLayout(FlowLayout.LEFT));
        
        // 创建标签
        JLabel label = new JLabel("主题:");
        label.setFont(ThemeConstants.Fonts.getDefault());
        
        // 获取所有主题的显示名称
        String[] themeDisplayNames = ThemeConstants.ThemeOps.getAllThemeDisplayNames();
        
        // 创建下拉框
        themeComboBox = new JComboBox<>(themeDisplayNames);
        themeComboBox.setFont(ThemeConstants.Fonts.getDefault());
        
        // 设置当前选中的主题
        String currentThemeName = ThemeConstants.ThemeOps.getCurrentThemeName();
        for (int i = 0; i < themeManager.getAllThemes().size(); i++) {
            if (themeManager.getAllThemes().get(i).getName().equals(currentThemeName)) {
                themeComboBox.setSelectedIndex(i);
                break;
            }
        }
        
        // 添加主题切换监听器
        themeComboBox.addActionListener(e -> {
            int selectedIndex = themeComboBox.getSelectedIndex();
            if (selectedIndex >= 0 && selectedIndex < themeManager.getAllThemes().size()) {
                String themeName = themeManager.getAllThemes().get(selectedIndex).getName();
                ThemeConstants.ThemeOps.switchTheme(themeName);
            }
        });
        
        // 添加组件到面板
        add(label);
        add(themeComboBox);
    }
}
