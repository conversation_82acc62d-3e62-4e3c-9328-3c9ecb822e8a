package com.ge.med.ct.laf2.presenter;

import com.ge.med.ct.dcm_se.core.cfg.ConfigManager;
import com.ge.med.ct.dcm_se.core.cfg.report.ReportConfigService;
import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.laf2.views.IPatientView;
import com.ge.med.ct.analysis.service.ProtocolInferService;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 患者面板Presenter
 * 负责协调PatientView与数据模型之间的交互
 */
public class PatientPresenter {
    private static final Logger LOG = Logger.getLogger(PatientPresenter.class.getName());
    private final IPatientView view;
    private final ConfigManager configManager;
    private final ReportConfigService reportConfig;
    private final ProtocolInferService protocolService;
    
    private DicomDataProvider dataProvider;
    private List<DicomExam> currentExams = new ArrayList<>();
    private List<DicomSeries> currentSeries = new ArrayList<>();
    private List<DicomImage> currentImages = new ArrayList<>();
    
    // 监听器接口
    public interface SelectionChangeListener {
        void onSelectionChanged(String inputPath, String outputPath, String protocol);
    }
    
    private SelectionChangeListener selectionChangeListener;
    
    /**
     * 构造函数
     * @param view 患者视图
     */
    public PatientPresenter(IPatientView view) {
        this.view = view;
        this.configManager = ConfigManager.getInstance();
        this.reportConfig = configManager.getReportConfig();
        this.protocolService = new ProtocolInferService();
    }
    
    /**
     * 设置DICOM数据提供者
     * @param provider DICOM数据提供者
     */
    public void setDicomDataProvider(DicomDataProvider provider) {
        this.dataProvider = provider;
        if (provider != null) {
            loadData();
        } else {
            clearData();
        }
    }
    
    /**
     * 加载数据
     */
    public void loadData() {
        if (dataProvider == null) {
            return;
        }
        
        try {
            clearData();
            
            List<DicomExam> exams = dataProvider.getAllExams();
            if (exams == null || exams.isEmpty()) {
                return;
            }
            
            currentExams = exams;
            view.updateExamList(exams);
            view.selectExam(0);
            
            // 自动选择第一个检查
            handleExamSelection(0);
        } catch (Exception e) {
            view.showError("数据加载失败", e);
        }
    }
    
    /**
     * 清除数据
     */
    public void clearData() {
        currentExams.clear();
        currentSeries.clear();
        currentImages.clear();
        view.clearContents();
    }
    
    /**
     * 搜索检查
     * @param patientName 患者姓名
     * @param examId 检查ID
     */
    public void searchExams(String patientName, String examId) {
        if (dataProvider == null) {
            return;
        }
        
        try {
            if (patientName.isEmpty() && examId.isEmpty()) {
                loadData();
                return;
            }
            
            List<DicomExam> exams = dataProvider.searchExams(patientName, examId);
            if (exams != null && !exams.isEmpty()) {
                currentExams = exams;
                view.updateExamList(exams);
                view.selectExam(0);
                handleExamSelection(0);
            } else {
                clearData();
            }
        } catch (Exception e) {
            view.showError("搜索失败", e);
        }
    }
    
    /**
     * 处理检查选择
     * @param selectedRow 选中的行索引
     */
    public void handleExamSelection(int selectedRow) {
        if (selectedRow < 0 || currentExams == null || selectedRow >= currentExams.size() || dataProvider == null) {
            return;
        }
        
        try {
            DicomExam selectedExam = currentExams.get(selectedRow);
            List<DicomSeries> series = dataProvider.getSeriesForExam(selectedExam.getId());
            if (series == null || series.isEmpty()) {
                return;
            }
            
            currentSeries = series;
            view.updateSeriesList(series);
            view.selectSeries(0);
            
            // 自动选择第一个序列
            handleSeriesSelection(0);
            
            // 处理协议
            processProtocol(0);
        } catch (Exception e) {
            view.showError("检查选择失败", e);
        }
    }
    
    /**
     * 处理序列选择
     * @param selectedRow 选中的行索引
     */
    public void handleSeriesSelection(int selectedRow) {
        if (selectedRow < 0 || currentSeries == null || selectedRow >= currentSeries.size() || dataProvider == null) {
            return;
        }
        
        try {
            DicomSeries selectedSeries = currentSeries.get(selectedRow);
            List<DicomImage> images = dataProvider.getImagesForSeries(selectedSeries.getId());
            if (images == null || images.isEmpty()) {
                return;
            }
            
            currentImages = images;
            view.updateImageList(images);
            view.selectImage(0);
            
            // 更新图像信息
            if (!images.isEmpty()) {
                updateImageInfo(0);
            }
            
            // 处理协议
            processProtocol(selectedRow);
        } catch (Exception e) {
            view.showError("序列选择失败", e);
        }
    }
    
    /**
     * 处理图像选择
     * @param selectedRow 选中的行索引
     */
    public void handleImageSelection(int selectedRow) {
        if (selectedRow < 0 || currentImages == null || selectedRow >= currentImages.size()) {
            return;
        }
        
        try {
            updateImageInfo(selectedRow);
        } catch (Exception e) {
            view.showError("图像选择失败", e);
        }
    }
    
    /**
     * 更新图像信息
     * @param imageIndex 图像索引
     */
    private void updateImageInfo(int imageIndex) {
        if (imageIndex < 0 || currentImages == null || imageIndex >= currentImages.size()) {
            return;
        }
        
        DicomImage image = currentImages.get(imageIndex);
        String filePath = findImageFilePath(image);
        if (!filePath.isEmpty()) {
            view.updateImageInfo(filePath);
        }
    }
    
    /**
     * 查找图像文件路径
     * @param image DICOM图像
     * @return 文件路径
     */
    private String findImageFilePath(DicomImage image) {
        if (image == null || dataProvider == null) {
            return "";
        }
        
        List<DicomFileModel> allFiles = dataProvider.getAllFileModels();
        if (allFiles == null) {
            return "";
        }
        
        return allFiles.stream()
                .filter(file -> {
                    Object sopInstanceUIDObj = file.getTagValue(DicomTagConstants.Image.SOP_INSTANCE_UID);
                    String sopInstanceUID = sopInstanceUIDObj != null ? sopInstanceUIDObj.toString() : null;
                    return image.getId().equals(sopInstanceUID);
                })
                .map(DicomFileModel::getFilePath)
                .findFirst()
                .orElse("");
    }
    
    /**
     * 处理协议并通知更改
     */
    private void processProtocol(int seriesIndex) {
        if (seriesIndex < 0 || seriesIndex >= currentSeries.size() || 
            selectionChangeListener == null) {
            return;
        }
        
        try {
            // 获取选中的序列
            DicomSeries series = currentSeries.get(seriesIndex);
            
            // 获取当前选中检查的行索引
            int examRow = view.getSelectedExamRow();
            DicomExam exam = null;
            if (examRow >= 0 && examRow < currentExams.size()) {
                exam = currentExams.get(examRow);
            }
            
            // 使用整合后的协议处理方法
            String protocol = protocolService.inferFromSeries(series, exam, currentImages);
            
            // 准备输出目录
            String repFilePath = prepareOutputDirectory() + File.separator + "analysis_rep_" + series.getId() + ".rep";
            
            // 找到输入图像路径
            String inputPath = "";
            if (!currentImages.isEmpty()) {
                inputPath = findImageFilePath(currentImages.get(0));
            }
            
            // 通知选择变更
            notifySelectionChanged(inputPath, repFilePath, protocol);
        } catch (Exception e) {
            LOG.log(Level.WARNING, "协议处理失败", e);
            view.showError("协议处理失败", e);
        }
    }
    
    /**
     * 准备输出目录
     * @return 输出目录路径
     */
    private String prepareOutputDirectory() {
        // 从配置文件中读取report.output.directory配置
        String outputDir = reportConfig.getReportOutputDirectory();
        
        // 如果配置为空，则使用默认目录
        if (outputDir == null || outputDir.trim().isEmpty()) {
            String currentDir = System.getProperty("user.dir");
            outputDir = currentDir + File.separator + "reports";
            LOG.warning("未配置report.output.directory，使用默认目录: " + outputDir);
        }
        
        // 确保输出目录存在
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            boolean created = outputDirFile.mkdirs();
            if (created) {
                LOG.info("已创建输出目录: " + outputDir);
            } else {
                LOG.warning("无法创建输出目录: " + outputDir + "，将使用当前目录");
                outputDir = System.getProperty("user.dir");
            }
        }
        
        return outputDir;
    }
    
    /**
     * 设置选择更改监听器
     * @param listener 选择更改监听器
     */
    public void setSelectionChangeListener(SelectionChangeListener listener) {
        this.selectionChangeListener = listener;
    }
    
    /**
     * 通知选择更改
     * @param inputPath 输入路径
     * @param outputPath 输出路径
     * @param protocolInfo 协议信息
     */
    private void notifySelectionChanged(String inputPath, String outputPath, String protocol) {
        if (selectionChangeListener != null) {
            selectionChangeListener.onSelectionChanged(inputPath, outputPath, protocol);
        }
    }
} 