package com.ge.med.ct.laf2.views;

import com.ge.med.ct.analysis.model.AnalysisParams;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IAnalysisViewListener;
import com.ge.med.ct.laf2.base.views.BaseView;
import com.ge.med.ct.laf2.base.views.IAnalysisView;
import com.ge.med.ct.laf2.components.CTButton;
import com.ge.med.ct.laf2.components.CTPanel;
import com.ge.med.ct.laf2.components.CTTextField;
import com.ge.med.ct.laf2.components.ProtocolTypeComboBox;
import com.ge.med.ct.laf2.model.ProtocolTypeMode;
import com.ge.med.ct.laf2.utils.ClassAlias.GBC;
import com.ge.med.ct.laf2.utils.ComponentFactory;
import com.ge.med.ct.laf2.utils.LayoutWorker;
import com.ge.med.ct.laf2.widgets.DebugPanel;
import com.ge.med.ct.laf2.widgets.PatientPanel;
import com.ge.med.ct.laf2.widgets.CommandPanel;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;

/**
 * 分析视图实现
 */
public class AnalysisView extends BaseView<IAnalysisViewListener> implements IAnalysisView {
    private static final long serialVersionUID = 1L;
    private static final int CONTROL_PANEL_HEIGHT = 250;
    private static final int DIVIDER_LOCATION = 380;
    private static final int DIVIDER_SIZE = 5;
    private static final int TEXT_FIELD_COLUMNS = 30;
    private static final int COMMAND_PANEL_HEIGHT = 200;
    
    // UI组件
    private final CTButton btnAccept = new CTButton("Series Analysis");
    private final CTTextField inputField = new CTTextField("", TEXT_FIELD_COLUMNS);
    private final CTTextField outputField = new CTTextField("", TEXT_FIELD_COLUMNS);
    private final CTTextField protocolField = new CTTextField("", TEXT_FIELD_COLUMNS);
    private final ProtocolTypeComboBox protocolTypeComboBox = new ProtocolTypeComboBox();
    private final JCheckBox textModeCheckBox = new JCheckBox("文本模式", true);

    // 面板组件
    private final PatientPanel patientPanel = new PatientPanel();
    private final CommandPanel seriesCommandPanel = new CommandPanel();
    private final DebugPanel debugPanel = new DebugPanel();
    
    /**
     * 构造函数
     */
    public AnalysisView() {
        super(JSplitPane.HORIZONTAL_SPLIT);
        setupLayout();
        setupListeners();
    }

    /**
     * 设置布局
     */
    private void setupLayout() {
        CTPanel rightPanel = createRightPanel();
        
        setLeftComponent(patientPanel);
        setRightComponent(rightPanel);
        configureSplitPane();
    }
    
    /**
     * 创建右侧面板
     */
    private CTPanel createRightPanel() {
        CTPanel rightPanel = new CTPanel(new BorderLayout());
        CTPanel controlPanel = createControlPanel();
        
        rightPanel.add(controlPanel, BorderLayout.NORTH);
        rightPanel.add(debugPanel, BorderLayout.CENTER);
        
        // 添加命令面板
        seriesCommandPanel.setPreferredSize(new Dimension(0, COMMAND_PANEL_HEIGHT));
        rightPanel.add(seriesCommandPanel, BorderLayout.SOUTH);
        
        return rightPanel;
    }
    
    /**
     * 创建控制面板
     */
    private CTPanel createControlPanel() {
        CTPanel controlPanel = new CTPanel(new GridBagLayout());
        controlPanel.setPreferredSize(new Dimension(0, CONTROL_PANEL_HEIGHT));
        controlPanel.setBorder(new EmptyBorder(0, 0, 0, 0));

        LayoutWorker helper = new LayoutWorker(controlPanel);
        helper.insets(new Insets(5, 5, 5, 5)).fill(GBC.HORIZONTAL);

        addInputComponents(helper);
        addProtocolComponents(helper);
        addOptionComponents(helper);
        
        // 添加接受按钮
        helper.position(0, 5).weight(0, 0).size(2, 1)
              .anchor(GridBagConstraints.CENTER).add(btnAccept);
        btnAccept.setEnabled(true);
        
        return controlPanel;
    }
    
    /**
     * 添加输入组件
     */
    private void addInputComponents(LayoutWorker helper) {
        // 添加输入路径
        JLabel inputLabel = ComponentFactory.createLabel("输入路径:");
        helper.position(0, 0).weight(0, 0).add(inputLabel);
        helper.position(1, 0).weight(1.0, 0).add(inputField);

        // 添加输出路径
        JLabel outputLabel = ComponentFactory.createLabel("输出路径:");
        helper.position(0, 1).weight(0, 0).add(outputLabel);
        helper.position(1, 1).weight(1.0, 0).add(outputField);
    }
    
    /**
     * 添加协议组件
     */
    private void addProtocolComponents(LayoutWorker helper) {
        // 添加协议
        JLabel protocolLabel = ComponentFactory.createLabel("协议:");
        helper.position(0, 2).weight(0, 0).add(protocolLabel);
        helper.position(1, 2).weight(1.0, 0).add(protocolField);

        // 添加协议类型下拉框
        JLabel protocolTypeLabel = ComponentFactory.createLabel("协议类型:");
        helper.position(0, 3).weight(0, 0).add(protocolTypeLabel);
        helper.position(1, 3).weight(1.0, 0).add(protocolTypeComboBox);
    }
    
    /**
     * 添加选项组件
     */
    private void addOptionComponents(LayoutWorker helper) {
        // 添加文本模式复选框
        helper.position(0, 4).weight(0, 0).add(new JLabel(""));
        helper.position(1, 4).weight(1.0, 0).add(textModeCheckBox);
    }
    
    /**
     * 配置分割面板
     */
    private void configureSplitPane() {
        setDividerSize(DIVIDER_SIZE);
        setDividerLocation(DIVIDER_LOCATION);
        setResizeWeight(LEFT_RIGHT_RATIO);
        setContinuousLayout(true);
    }

    /**
     * 设置监听器
     */
    private void setupListeners() {
        setupAcceptButtonListener();
        setupProtocolTypeListener();
        setupPatientPanelListener();
    }
    
    /**
     * 设置接受按钮监听器
     */
    private void setupAcceptButtonListener() {
        btnAccept.addActionListener(e -> {
            IAnalysisViewListener listener = getViewListener();
            if (listener != null) {
                listener.onAnalysisRequested();
            }
        });
    }
    
    /**
     * 设置协议类型监听器
     */
    private void setupProtocolTypeListener() {
        protocolTypeComboBox.setSelectionListener(selectedType -> {
            try {
                updateProtocolField(selectedType);
            } finally {
                // 空finally块确保完成操作
            }
        });
    }
    
    /**
     * 更新协议字段
     */
    private void updateProtocolField(ProtocolTypeMode selectedType) {
        // 获取当前协议文本
        String currentProtocol = protocolField.getText();

        // 如果协议文本为空或不包含符号'|'，则创建新的协议字符串
        if (currentProtocol == null || currentProtocol.isEmpty() || !currentProtocol.contains("|")) {
            protocolField.setText(selectedType.getProtocolString() + "|AUTO|1");
        } else {
            // 如果已有协议字符串，只替换协议类型部分
            String[] parts = currentProtocol.split("\\|");
            if (parts.length >= 1) {
                StringBuilder newProtocol = new StringBuilder(selectedType.getProtocolString());
                for (int i = 1; i < parts.length; i++) {
                    newProtocol.append("|").append(parts[i]);
                }
                protocolField.setText(newProtocol.toString());
            }
        }
    }
    
    /**
     * 设置患者面板监听器
     */
    private void setupPatientPanelListener() {
        patientPanel.setSelectionChangeListener((inputPath, outputPath, protocol) -> {
            inputField.setText(inputPath);
            outputField.setText(outputPath);
            protocolField.setText(protocol);

            // 如果协议不为空，则更新协议类型下拉框
            if (protocol != null && !protocol.isEmpty()) {
                protocolTypeComboBox.selectByProtocolString(protocol);
            }
        });
    }

    @Override
    public void setAnalyzing(boolean isAnalyzing) {
        SwingUtilities.invokeLater(() -> {
            btnAccept.setEnabled(!isAnalyzing);
            if (isAnalyzing) {
                showMessage(MessageType.INFO, "分析正在进行中...");
            }
        });
    }

    @Override
    public void refreshPatientInfo() {
        SwingUtilities.invokeLater(() -> {
            if (getDataProvider() != null) {
                patientPanel.updateContents(getDataProvider());
            }
        });
    }

    @Override
    public void resetPatientInfo() {
        SwingUtilities.invokeLater(patientPanel::clearContents);
    }

    /**
     * 清空视图
     * 重置所有UI组件到初始状态
     */
    @Override
    public void clearView() {
        // 清除输入/输出路径
        inputField.setText("");
        outputField.setText("");
        protocolField.setText("");
        
        // 重置UI组件状态
        protocolTypeComboBox.setSelectedIndex(0);
        textModeCheckBox.setSelected(true);
        btnAccept.setEnabled(true);
        
        // 清空日志
        debugPanel.clearText();
        
        // 重置患者信息
        resetPatientInfo();
    }

    @Override
    public AnalysisParams getAnalysisParams() {
        return new AnalysisParams(
            inputField.getText(),
            outputField.getText(),
            protocolField.getText(),
            textModeCheckBox.isSelected()
        );
    }
    
    /**
     * 记录带类型的日志
     * @param type 消息类型
     * @param text 日志文本
     */
    @Override
    public void logToView(MessageType type, String text) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }
        
        // 不同类型消息使用不同颜色
        Color color;
        switch (type) {
            case ERROR:
                color = Color.RED;
                break;
            case WARNING:
                color = Color.ORANGE;
                break;
            case SUCCESS:
                color = new Color(0, 128, 0); // 深绿色
                break;
            case INFO:
            default:
                color = Color.BLACK;
                break;
        }
        
        // 使用debugPanel添加日志
        debugPanel.log(text, color);
        
        // 记录日志
        switch (type) {
            case ERROR:
                logger.severe(text);
                break;
            case WARNING:
                logger.warning(text);
                break;
            case SUCCESS:
            case INFO:
            default:
                logger.info(text);
                break;
        }
    }
}
