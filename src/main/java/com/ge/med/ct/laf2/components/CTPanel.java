package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import java.awt.*;

/**
 * 通用面板组件，整合了多种面板功能
 */
public class CTPanel extends JPanel {
    private static final long serialVersionUID = 1L;

    // 面板类型
    public enum PanelType {
        NORMAL,      // 普通面板
        FLAT,        // 扁平化面板
        PAPER,       // 纸张效果面板
        ELEVATED,    // 带阴影的面板
        PROPORTIONAL,// 比例面板
        HEADER       // 带标题栏的面板
    }

    // 面板属性
    private final PanelType panelType;
    private int borderRadius = ThemeConstants.Sizes.getBorderRadius();
    private Color borderColor = ThemeConstants.Colors.getBorder();
    private int borderWidth = 1;
    private int padding = ThemeConstants.Sizes.getPadding();
    private boolean hasShadow = false;
    private float shadowOpacity = 0.2f;
    private int shadowSize = 6;
    private int shadowOffsetX = 0;
    private int shadowOffsetY = 2;
    private Color shadowColor = new Color(0, 0, 0);
    private float proportionalRatio = 1.0f;

    // 标题相关属性
    private JPanel headerPanel;
    private JLabel titleLabel;
    private String title;
    private Color headerBackgroundColor = ThemeConstants.Colors.getPrimary();
    private Color headerTextColor = Color.WHITE;
    private int headerHeight = 30;

    // 内容面板
    private final JPanel contentPanel;

    /**
     * 创建默认面板
     */
    public CTPanel() {
        this(null, PanelType.NORMAL);
    }

    /**
     * 创建指定类型的面板
     */
    public CTPanel(PanelType panelType) {
        this(null, panelType);
    }

    /**
     * 创建带布局管理器的面板
     */
    public CTPanel(LayoutManager layout) {
        this(layout, PanelType.NORMAL);
    }

    public CTPanel(String title, LayoutManager layout, PanelType panelType) {
        this(layout, panelType);
        this.title = title;

        createHeaderPanel();
    }
    /**
     * 创建带布局管理器和指定类型的面板
     */
    public CTPanel(LayoutManager layout, PanelType panelType) {
        super(new BorderLayout());
        this.panelType = panelType;

        setOpaque(false);
        setBackground(ThemeConstants.Colors.getPanelBackground());

        contentPanel = new JPanel(layout != null ? layout : new BorderLayout());
        contentPanel.setOpaque(true);
        contentPanel.setBackground(getBackground());
        contentPanel.setVisible(true);
        contentPanel.setMinimumSize(new Dimension(100, 100));
        contentPanel.setPreferredSize(new Dimension(800, 600));

        super.add(contentPanel, BorderLayout.CENTER);
        applyPanelTypeSettings();
    }

    /**
     * 创建比例面板
     */
    public CTPanel(LayoutManager layout, float ratio) {
        this(layout, PanelType.PROPORTIONAL);
        this.proportionalRatio = ratio;
    }

    /**
     * 创建带标题的面板
     */
    public CTPanel(String title) {
        this(new BorderLayout(), PanelType.HEADER);
        this.title = title;

        // 应用标题面板设置
        createHeaderPanel();
    }

    /**
     * 根据面板类型应用相应设置
     */
    private void applyPanelTypeSettings() {
        switch (panelType) {
            case FLAT:
                borderRadius = 10;
                padding = ThemeConstants.Sizes.getPadding();
                break;

            case PAPER:
                borderRadius = 4;
                hasShadow = true;
                shadowOpacity = 0.15f;
                shadowSize = 8;
                shadowOffsetY = 2;
                break;

            case ELEVATED:
                borderRadius = 6;
                hasShadow = true;
                shadowOpacity = 0.25f;
                shadowSize = 10;
                shadowOffsetY = 3;
                borderWidth = 0;
                break;

            case HEADER:
                createHeaderPanel();
                break;

            case NORMAL:
            case PROPORTIONAL:
            default:
                // 使用默认设置
                break;
        }

        updateBorder();
    }

    /**
     * 创建标题面板
     */
    private void createHeaderPanel() {
        // 创建标题面板
        headerPanel = new JPanel(new BorderLayout());
        headerPanel.setPreferredSize(new Dimension(0, headerHeight));
        headerPanel.setBackground(headerBackgroundColor);
        headerPanel.setBorder(BorderFactory.createMatteBorder(2, 0, 0, 0,
                headerBackgroundColor.darker()));

        // 创建标题标签
        titleLabel = new JLabel(title != null ? title : "", SwingConstants.CENTER);
        titleLabel.setForeground(headerTextColor);
        titleLabel.setFont(ThemeConstants.Fonts.getNormal().deriveFont(Font.BOLD));

        headerPanel.add(titleLabel, BorderLayout.CENTER);
        super.add(headerPanel, BorderLayout.NORTH);
    }

    /**
     * 更新边框设置
     */
    private void updateBorder() {
        if (borderWidth > 0) {
            contentPanel.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(borderColor, borderWidth),
                    BorderFactory.createEmptyBorder(padding, padding, padding, padding)
            ));
        } else {
            contentPanel.setBorder(BorderFactory.createEmptyBorder(padding, padding, padding, padding));
        }
    }

    // 基本属性设置器

    public void setBorderRadius(int radius) {
        this.borderRadius = radius;
        repaint();
    }

    public void setBorderColor(Color color) {
        this.borderColor = color;
        updateBorder();
    }

    public void setBorderWidth(int width) {
        this.borderWidth = width;
        updateBorder();
    }

    public void setPadding(int padding) {
        this.padding = padding;
        updateBorder();
    }

    // 阴影设置

    public void setHasShadow(boolean hasShadow) {
        this.hasShadow = hasShadow;
        repaint();
    }

    public void setShadowOpacity(float opacity) {
        this.shadowOpacity = opacity;
        repaint();
    }

    public void setShadowSize(int size) {
        this.shadowSize = size;
        repaint();
    }

    public void setShadowOffset(int offsetX, int offsetY) {
        this.shadowOffsetX = offsetX;
        this.shadowOffsetY = offsetY;
        repaint();
    }

    public void setShadowColor(Color color) {
        this.shadowColor = color;
        repaint();
    }

    // 标题面板设置

    public void setHeaderBackgroundColor(Color color) {
        this.headerBackgroundColor = color;
        if (headerPanel != null) {
            headerPanel.setBackground(color);
            headerPanel.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, color.darker()));
            repaint();
        }
    }

    public void setHeaderTextColor(Color color) {
        this.headerTextColor = color;
        if (titleLabel != null) {
            titleLabel.setForeground(color);
        }
    }

    public void setTitle(String title) {
        this.title = title;
        if (titleLabel != null) {
            titleLabel.setText(title);
        }
    }

    public void setHeaderHeight(int height) {
        this.headerHeight = height;
        if (headerPanel != null) {
            headerPanel.setPreferredSize(new Dimension(0, height));
            revalidate();
        }
    }

    // 访问器

    public JPanel getContentPanel() {
        return contentPanel;
    }

    public JPanel getHeaderPanel() {
        return headerPanel;
    }

    // 组件操作

    @Override
    public Component add(Component comp) {
        contentPanel.add(comp);
        return comp;
    }

    @Override
    public void add(Component comp, Object constraints) {
        if (constraints != null) {
            // Get the layout manager
            LayoutManager layout = contentPanel.getLayout();

            // Handle different layout types
            if (layout instanceof BorderLayout) {
                if (constraints instanceof String ||
                    BorderLayout.CENTER.equals(constraints) ||
                    BorderLayout.NORTH.equals(constraints) ||
                    BorderLayout.SOUTH.equals(constraints) ||
                    BorderLayout.EAST.equals(constraints) ||
                    BorderLayout.WEST.equals(constraints)) {
                    contentPanel.add(comp, constraints);
                } else {
                    contentPanel.add(comp, BorderLayout.CENTER);
                }
            } else if (layout instanceof CardLayout) {
                // For CardLayout, the constraint should be a String name
                if (constraints instanceof String) {
                    String name = (String) constraints;
                    contentPanel.add(comp, name);
                } else {
                    contentPanel.add(comp);
                }
            } else {
                contentPanel.add(comp, constraints);
            }
        } else {
            contentPanel.add(comp);
        }
    }

    public void setContent(Component component) {
        contentPanel.removeAll();
        contentPanel.add(component, BorderLayout.CENTER);
        contentPanel.revalidate();
        contentPanel.repaint();
    }

    @Override
    public void setBackground(Color bg) {
        super.setBackground(bg);
        if (contentPanel != null) {
            contentPanel.setBackground(bg);
        }
    }

    // 绘制

    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        int width = getWidth();
        int height = getHeight();

        // 绘制阴影
        if (hasShadow) {
            paintShadow(g2, width, height);
        }

        // 绘制背景
        g2.setColor(getBackground());
        g2.fillRoundRect(0, 0, width, height, borderRadius, borderRadius);

        g2.dispose();
    }

    private void paintShadow(Graphics2D g2, int width, int height) {
        AlphaComposite originalComposite = (AlphaComposite) g2.getComposite();

        for (int i = 0; i < shadowSize; i++) {
            float alpha = shadowOpacity * (1.0f - (float) i / shadowSize);
            g2.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
            g2.setColor(shadowColor);

            g2.fillRoundRect(
                    shadowOffsetX - (shadowSize - i) / 2,
                    shadowOffsetY - (shadowSize - i) / 2,
                    width + (shadowSize - i),
                    height + (shadowSize - i),
                    borderRadius + shadowSize - i,
                    borderRadius + shadowSize - i
            );
        }

        g2.setComposite(originalComposite);
    }

    @Override
    public Dimension getPreferredSize() {
        if (panelType == PanelType.PROPORTIONAL) {
            Container parent = getParent();
            if (parent != null) {
                int parentWidth = parent.getWidth();
                int width = (int) (parentWidth * proportionalRatio);
                return new Dimension(width, super.getPreferredSize().height);
            }
        }
        return super.getPreferredSize();
    }

    /**
     * 设置布局管理器
     */
    @Override
    public void setLayout(LayoutManager mgr) {
        super.setLayout(new BorderLayout());

        if (contentPanel != null) {
            contentPanel.setLayout(mgr);
        }
    }

    /**
     * 设置BoxLayout布局
     * @param axis BoxLayout.X_AXIS, BoxLayout.Y_AXIS, BoxLayout.LINE_AXIS, BoxLayout.PAGE_AXIS
     */
    public void setBoxLayout(int axis) {
        if (contentPanel != null) {
            contentPanel.setLayout(new BoxLayout(contentPanel, axis));
        }
    }
}