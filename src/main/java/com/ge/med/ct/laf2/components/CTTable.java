package com.ge.med.ct.laf2.components;

import com.ge.med.ct.service.TableOperations;
import com.ge.med.ct.laf2.theming.FixTableStyle;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.util.Vector;

public class CTTable extends JTable {

    public CTTable() {
        this(new DefaultTableModel());
    }

    public CTTable(DefaultTableModel model) {
        super(model);
        // 应用表格样式
        SwingUtilities.invokeLater(() -> FixTableStyle.setupTableStyle(this));
    }

    public CTTable(String[] columnNames) {
        this(new DefaultTableModel(columnNames, 0));
    }

    public void updateData(DefaultTableModel model) {
        setModel(model);
        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidthsAsync(this));
    }

    public void updateData(Vector<Vector<String>> data) {
        DefaultTableModel model = (DefaultTableModel) getModel();
        model.setRowCount(0);
        for (Vector<String> row : data) {
            model.addRow(row);
        }

        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidthsAsync(this));
    }

    public void clearItems() {
        DefaultTableModel model = (DefaultTableModel) getModel();
        model.setRowCount(0);
        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidthsAsync(this));
    }

    @Override
    public void addNotify() {
        super.addNotify();
        // 组件添加到容器后调整列宽
        SwingUtilities.invokeLater(() -> TableOperations.adjustColumnWidthsAsync(this));
    }

}