package com.ge.med.ct.laf2.widgets;

import com.ge.med.ct.dicom2.reader.DicomMetadataReader;
import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.laf2.components.CTJScrollPane;
import com.ge.med.ct.laf2.components.CTPanel;
import com.ge.med.ct.laf2.utils.ClassAlias.GBC;
import com.ge.med.ct.laf2.utils.ClassAlias.GBLayout;
import com.ge.med.ct.laf2.theming.ThemeConstants;
import com.ge.med.ct.service.LogManager;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.logging.Logger;

public class ImageInfoPanel extends CTPanel {

    private static final Insets DEFAULT_INSETS = new Insets(5, 5, 5, 0);
    private static final Insets SECTION_INSETS = new Insets(5, 5, 0, 5);
    private final Logger logger = LogManager.getInstance().getLogger(ImageInfoPanel.class);

    private final JPanel contentPanel = new JPanel(new GBLayout());
    private final DicomMetadataReader metadataReader = new DicomMetadataReader();

    public ImageInfoPanel() {
        super(PanelType.HEADER);

        CTJScrollPane scrollPane = new CTJScrollPane(contentPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setBorder(null);

        setPadding(3);
        setTitle("图像信息");
        setContent(scrollPane);
    }

    public void loadDicomFile(String dicomPath) {
        if (dicomPath == null || dicomPath.isEmpty()) {
            clearContents();
            return;
        }
        clearContents();

        SwingUtilities.invokeLater(() -> {
            try {
                List<DicomTag> tags = metadataReader.readAllTags(dicomPath);
                if (tags.isEmpty()) {
                    logger.warning("No dicom tags were loaded.");
                    return;
                }

                GBC gbc = new GBC();
                gbc.insets = DEFAULT_INSETS;
                gbc.fill = GBC.HORIZONTAL;
                gbc.anchor = GBC.WEST;
                gbc.weightx = 1.0;

                int currentRow = 0;

                // Study Information
                addSectionHeader(contentPanel, "Study Information", currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Study.STUDY_ID),
                        getTagValue(tags, DicomTagConstants.Study.STUDY_ID), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Study.STUDY_DESCRIPTION),
                        getTagValue(tags, DicomTagConstants.Study.STUDY_DESCRIPTION), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Patient.PATIENT_ID),
                        getTagValue(tags, DicomTagConstants.Patient.PATIENT_ID), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Patient.PATIENT_NAME),
                        getTagValue(tags, DicomTagConstants.Patient.PATIENT_NAME), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Study.STUDY_DATE),
                        formatDate(getTagValue(tags, DicomTagConstants.Study.STUDY_DATE)), currentRow++, gbc);
                currentRow++;

                // Series Information
                addSectionHeader(contentPanel, "Series Information", currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Series.SERIES_NUMBER),
                        getTagValue(tags, DicomTagConstants.Series.SERIES_NUMBER), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Series.SERIES_DESCRIPTION),
                        getTagValue(tags, DicomTagConstants.Series.SERIES_DESCRIPTION), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Series.MODALITY),
                        getTagValue(tags, DicomTagConstants.Series.MODALITY), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Equipment.MANUFACTURER),
                        getTagValue(tags, DicomTagConstants.Equipment.MANUFACTURER), currentRow++, gbc);
                addInfoRow(contentPanel,
                        DicomTagConstants.getTagName(DicomTagConstants.Equipment.MANUFACTURER_MODEL_NAME),
                        getTagValue(tags, DicomTagConstants.Equipment.MANUFACTURER_MODEL_NAME), currentRow++, gbc);
                currentRow++;

                // Image Information
                addSectionHeader(contentPanel, "Image Information", currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Image.INSTANCE_NUMBER),
                        formatValue(getTagValue(tags, DicomTagConstants.Image.INSTANCE_NUMBER), ""), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Image.SLICE_THICKNESS),
                        formatValue(getTagValue(tags, DicomTagConstants.Image.SLICE_THICKNESS), "mm"), currentRow++,
                        gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.CT.GANTRY_TILT),
                        formatValue(getTagValue(tags, DicomTagConstants.CT.GANTRY_TILT), "°"), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Image.ROWS),
                        getMatrixSize(tags), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Image.PIXEL_SPACING),
                        formatPixelSpacing(getTagValue(tags, DicomTagConstants.Image.PIXEL_SPACING)), currentRow++,
                        gbc);
                currentRow++;

                // Protocol Parameters
                addSectionHeader(contentPanel, "Protocol Parameters", currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Study.PROTOCOL_NAME),
                        getTagValue(tags, DicomTagConstants.Study.PROTOCOL_NAME), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.CT.KVP),
                        formatValue(getTagValue(tags, DicomTagConstants.CT.KVP), "kV"), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.CT.XRAY_TUBE_CURRENT),
                        formatValue(getTagValue(tags, DicomTagConstants.CT.XRAY_TUBE_CURRENT), "mA"), currentRow++,
                        gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.CT.FIELD_OF_VIEW),
                        formatValue(getTagValue(tags, DicomTagConstants.CT.FIELD_OF_VIEW), "cm"), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.CT.RECONSTRUCTION_DIAMETER),
                        formatValue(getTagValue(tags, DicomTagConstants.CT.RECONSTRUCTION_DIAMETER), "cm"),
                        currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.CT.CONVOLUTION_KERNEL),
                        getTagValue(tags, DicomTagConstants.CT.CONVOLUTION_KERNEL), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Image.WINDOW_CENTER),
                        getWindowSettings(tags), currentRow++, gbc);
                currentRow++;

                // Position Information
                addSectionHeader(contentPanel, "Position Information", currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.CT.PATIENT_POSITION),
                        getTagValue(tags, DicomTagConstants.CT.PATIENT_POSITION), currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Image.IMAGE_POSITION_PATIENT),
                        formatImagePosition(getTagValue(tags, DicomTagConstants.Image.IMAGE_POSITION_PATIENT)),
                        currentRow++, gbc);
                addInfoRow(contentPanel, DicomTagConstants.getTagName(DicomTagConstants.Image.SLICE_LOCATION),
                        formatValue(getTagValue(tags, DicomTagConstants.Image.SLICE_LOCATION), "mm"), currentRow++,
                        gbc);

                revalidate();
                repaint();
            } catch (Exception e) {
                logger.severe("Failed to load DICOM file: " + e.getMessage());
                showError("Failed to load DICOM file: " + e.getMessage());
            }
        });
    }

    private String getTagValue(List<DicomTag> tags, String tagId) {
        return tags.stream()
                .filter(tag -> tag.getTagId().equals(tagId))
                .map(DicomTag::getValueAsString)
                .findFirst()
                .orElse("");
    }

    private void addSectionHeader(JPanel panel, String title, int row, GBC gbc) {
        JLabel headerLabel = new JLabel(title);
        headerLabel.setFont(ThemeConstants.getCurrentTheme().getTableContentFont());
        headerLabel.setForeground(ThemeConstants.Colors.getTextBright());

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.insets = SECTION_INSETS;
        panel.add(headerLabel, gbc);

        gbc.insets = DEFAULT_INSETS;
        gbc.gridwidth = 1;
    }

    private void addInfoRow(JPanel panel, String label, String value, int row, GBC gbc) {
        String displayValue = value != null && !value.isEmpty() ? value : "⊹";
        String TAG_VALUE_FORMAT = "<html><div style='margin: 1px 0'><span style='color: #666666'>%s</span><br>%s</div></html>";
        JLabel infoLabel = new JLabel(String.format(TAG_VALUE_FORMAT, label, displayValue));
        infoLabel.setFont(ThemeConstants.getCurrentTheme().getDefaultFont());

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 1.0;
        gbc.anchor = GBC.LINE_START;
        gbc.fill = GBC.HORIZONTAL;
        panel.add(infoLabel, gbc);
    }

    public void clearContents() {
        contentPanel.removeAll();
        revalidate();
        repaint();
    }

    private String formatValue(String value, String unit) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        return value + (unit.isEmpty() ? "" : " " + unit);
    }

    private String getMatrixSize(List<DicomTag> tags) {
        String rows = getTagValue(tags, DicomTagConstants.Image.ROWS);
        String columns = getTagValue(tags, DicomTagConstants.Image.COLUMNS);
        if (!rows.isEmpty() && !columns.isEmpty()) {
            return rows + " × " + columns;
        }
        return "";
    }

    private String formatPixelSpacing(String pixelSpacing) {
        if (pixelSpacing == null || pixelSpacing.trim().isEmpty()) {
            return "";
        }
        String[] values = pixelSpacing.split("\\\\");
        if (values.length == 2) {
            return values[0] + " × " + values[1] + " mm";
        }
        return pixelSpacing + " mm";
    }

    private String getWindowSettings(List<DicomTag> tags) {
        String center = getTagValue(tags, DicomTagConstants.Image.WINDOW_CENTER);
        String width = getTagValue(tags, DicomTagConstants.Image.WINDOW_WIDTH);
        if (!center.isEmpty() && !width.isEmpty()) {
            return center + " / " + width;
        }
        return "";
    }

    private String formatDate(String dicomDate) {
        if (dicomDate == null || dicomDate.trim().isEmpty()) {
            return "";
        }

        if (dicomDate.length() == 8) {
            return dicomDate.substring(0, 4) + "-" +
                    dicomDate.substring(4, 6) + "-" +
                    dicomDate.substring(6, 8);
        }
        return dicomDate;
    }

    private String formatImagePosition(String position) {
        if (position == null || position.trim().isEmpty()) {
            return "";
        }

        String[] values = position.split("\\\\");
        if (values.length == 3) {
            return String.format("X: %s, Y: %s, Z: %s", values[0], values[1], values[2]);
        }
        return position;
    }

    private void showError(String message) {
        clearContents();
        JLabel errorLabel = new JLabel(message);
        errorLabel.setForeground(ThemeConstants.Colors.getError());
        errorLabel.setFont(ThemeConstants.getCurrentTheme().getTableContentFont());
        GBC gbc = new GBC();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.insets = DEFAULT_INSETS;
        contentPanel.add(errorLabel, gbc);
        revalidate();
        repaint();
    }
}