package com.ge.med.ct.laf2.message;

import com.ge.med.ct.exception.message.AbstractMessage;
import com.ge.med.ct.exception.message.Message;

/**
 * 分析模块的消息枚举
 * 定义分析模块中使用的所有消息
 */
public enum AnalysisMessages implements Message {
    // 分析状态消息
    ANALYSIS_IN_PROGRESS("analysis.in.progress", "分析已经在进行中，请等待当前分析完成"),
    ANALYSIS_STARTED("analysis.started", "开始分析..."),
    ANALYSIS_COMPLETED("analysis.completed", "分析成功完成，结果报告已生成"),
    ANALYSIS_FAILED("analysis.failed", "分析失败，未能生成有效报告"),
    PROCESS_RESULT_ERROR("analysis.process.result.error", "处理结果时发生错误: {0}"),

    // 协议相关消息
    PROTOCOL_INFO("analysis.protocol.info", "分析协议: {0} - {1}"),
    SERIES_PREFIX("analysis.series.prefix", "Series "),

    // 错误消息
    UNKNOWN_ERROR("analysis.unknown.error", "未知错误"),
    ANALYSIS_ERROR("analysis.error", "分析失败: {0}"),
    EXECUTION_FAILED("analysis.execution.failed", "执行失败: {0}"),

    // 文件相关消息
    OUTPUT_FILE_WARNING("analysis.output.file.warning", "警告: 输出文件未生成"),


    // 错误消息提取关键词
    IO_ERROR_PREFIX("analysis.io.error.prefix", "IO错误:"),
    CREATE_PROCESS_ERROR("analysis.create.process.error", "CreateProcess error="),
    ELLIPSIS("analysis.ellipsis", "...");

    private final AbstractMessage delegate;

    AnalysisMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage) {};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}
