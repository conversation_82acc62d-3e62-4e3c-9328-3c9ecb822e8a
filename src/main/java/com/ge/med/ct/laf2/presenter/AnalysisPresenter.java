package com.ge.med.ct.laf2.presenter;

import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IAnalysisViewListener;
import com.ge.med.ct.laf2.base.views.IAnalysisView;
import com.ge.med.ct.laf2.message.AnalysisMessages;
import com.ge.med.ct.service.CommandInvoker;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.analysis.model.AnalysisParams;
import com.ge.med.ct.analysis.model.AnalysisResult;
import com.ge.med.ct.analysis.model.AnalysisState;
import com.ge.med.ct.dicom2.core.DicomDataProvider;

import javax.swing.*;
import java.io.File;
import java.util.Objects;

/**
 * 分析视图的Presenter，负责协调Model和View
 */
public class AnalysisPresenter implements IAnalysisViewListener {
    private final IAnalysisView view;
    private final ReportPresenter reportPresenter;
    private boolean isAnalyzing = false;

    /**
     * 创建分析视图Presenter
     */
    public AnalysisPresenter(IAnalysisView view, ReportPresenter reportPresenter) {
        this.view = Objects.requireNonNull(view, "视图不能为空");
        this.reportPresenter = Objects.requireNonNull(reportPresenter, "报告Presenter不能为空");
        view.setViewListener(this);
    }

    /**
     * 设置DICOM数据提供者
     */
    public void setDicomProvider(DicomDataProvider provider) {
        if (provider != null) {
            view.setDataProvider(provider);
            view.refreshPatientInfo();
        }
    }

    @Override
    public void onAnalysisRequested() {
        if (isAnalyzing) {
            logToView(MessageType.ERROR, AnalysisMessages.ANALYSIS_IN_PROGRESS.toStr());
            return;
        }

        AnalysisParams params = view.getAnalysisParams();
        if (!params.isValid()) {
            logToView(MessageType.ERROR, params.getValidationError());
            return;
        }

        executeAnalysis(params);
    }

    @Override
    public void onMessage(MessageType type, String message) {
        logToView(type, message);
    }

    /**
     * 执行分析任务
     */
    private void executeAnalysis(AnalysisParams params) {
        updateAnalysisState(true);
        logToView(MessageType.INFO, AnalysisMessages.ANALYSIS_STARTED.toStr());

        String command = params.toCommandString();

        new AnalysisWorker(params, command).execute();
    }

    /**
     * 更新分析状态
     */
    private void updateAnalysisState(boolean analyzing) {
        isAnalyzing = analyzing;
        view.setAnalyzing(analyzing);
    }



    /**
     * 确定分析状态
     */
    private AnalysisState determineAnalysisState(String output, boolean fileExists) {
        if (!fileExists || output == null) {
            return AnalysisState.FAIL;
        }

        if (output.contains("ERROR") || output.contains("错误")) {
            return AnalysisState.FAIL;
        }

        return AnalysisState.PASS;
    }

    /**
     * 处理分析结果
     */
    private void processResult(String protocol, AnalysisResult result) {
        if (result.getStatus() == AnalysisState.PASS) {
            logToView(MessageType.INFO, AnalysisMessages.ANALYSIS_COMPLETED.toStr());
        } else {
            logToView(MessageType.ERROR, AnalysisMessages.ANALYSIS_FAILED.toStr());
        }

        reportPresenter.addCheckItem(AnalysisMessages.SERIES_PREFIX.toStr() + protocol, result);
    }

    /**
     * 记录带类型的消息到视图
     */
    private void logToView(MessageType type, String message) {
        SwingUtilities.invokeLater(() -> view.logToView(type, message));
    }

    /**
     * 记录纯文本消息到视图
     */
    private void logToView(String message) {
        logToView(MessageType.INFO, message);
    }

    /**
     * 分析工作线程
     */
    private class AnalysisWorker extends SwingWorker<AnalysisResult, String> {
        private final AnalysisParams params;
        private final String command;

        AnalysisWorker(AnalysisParams params, String command) {
            this.params = params;
            this.command = command;
        }

        @Override
        protected AnalysisResult doInBackground() {
            try {
                // 简化命令输出，只显示协议信息
                publish(AnalysisMessages.PROTOCOL_INFO.format(params.getProtocolType(), params.getProtocolName()).toStr());

                String cmdOutput = CommandInvoker.execute(CommandInvoker.IAUI_SCRIPT, command);
                processCommandOutput(cmdOutput);

                boolean outputFileExists = checkOutputFile();
                return createAnalysisResult(cmdOutput, outputFileExists);

            } catch (QAToolException e) {
                // 简化错误消息，提取关键信息
                String errorMsg = simplifyErrorMessage(e.getMessage());
                publish(AnalysisMessages.ANALYSIS_ERROR.format(errorMsg).toStr());
                return new AnalysisResult(params, AnalysisState.FAIL,
                        AnalysisMessages.EXECUTION_FAILED.format(errorMsg).toStr(), null);
            }
        }

        /**
         * 简化错误消息，提取关键信息
         */
        private String simplifyErrorMessage(String message) {
            if (message == null) {
                return AnalysisMessages.UNKNOWN_ERROR.toStr();
            }

            // 如果是IO错误，提取关键部分
            if (message.contains(AnalysisMessages.IO_ERROR_PREFIX.toStr())) {
                int startIndex = message.indexOf(AnalysisMessages.IO_ERROR_PREFIX.toStr());
                int endIndex = message.indexOf(AnalysisMessages.CREATE_PROCESS_ERROR.toStr());
                if (endIndex > startIndex) {
                    return message.substring(startIndex, endIndex).trim();
                }
            }

            // 如果消息过长，截取前100个字符
            if (message.length() > 100) {
                return message.substring(0, 100) + AnalysisMessages.ELLIPSIS.toStr();
            }

            return message;
        }

        private void processCommandOutput(String cmdOutput) {
            // 只输出重要的错误和警告信息
            boolean hasImportantOutput = false;
            for (String line : cmdOutput.split("\\r?\\n")) {
                if (isImportantLine(line)) {
                    publish(line);
                    hasImportantOutput = true;
                }
            }

            if (!hasImportantOutput) {
                // 不输出无效输出的消息，减少日志冗余
            }
        }

        /**
         * 判断是否是重要的日志行
         */
        private boolean isImportantLine(String line) {
            if (line == null || line.trim().isEmpty()) {
                return false;
            }

            // 只显示错误和警告信息
            return (line.contains("ERROR") ||
                    line.contains("错误") ||
                    line.contains("WARN") ||
                    line.contains("警告")) &&
                   !line.contains("DEBUG") &&
                   !line.contains("Cleaning up") &&
                   !line.contains("ProtocolMgr") &&
                   !line.trim().startsWith("[IA]");
        }

        private boolean checkOutputFile() {
            boolean outputFileExists = new File(params.getOutputPath()).exists();
            if (!outputFileExists) {
                publish(AnalysisMessages.OUTPUT_FILE_WARNING.toStr());
            }
            return outputFileExists;
        }

        private AnalysisResult createAnalysisResult(String cmdOutput, boolean outputFileExists) {
            AnalysisState status = determineAnalysisState(cmdOutput, outputFileExists);
            return new AnalysisResult(params, status, cmdOutput, null);
        }

        @Override
        protected void process(java.util.List<String> chunks) {
            for (String chunk : chunks) {
                logToView(chunk);
            }
        }

        @Override
        protected void done() {
            try {
                AnalysisResult result = get();
                processResult(params.getProtocol(), result);
            } catch (Exception e) {
                logToView(MessageType.ERROR, AnalysisMessages.PROCESS_RESULT_ERROR.format(e.getMessage()).toStr());
            } finally {
                updateAnalysisState(false);
            }
            // 不输出分析结束的分隔符，减少日志冗余
        }
    }
}