package com.ge.med.ct.laf2.widgets;

import com.ge.med.ct.laf2.components.*;
import com.ge.med.ct.laf2.utils.ClassAlias.GBC;
import com.ge.med.ct.laf2.utils.ClassAlias.GBLayout;
import com.ge.med.ct.laf2.utils.LayoutWorker;

import javax.swing.*;
import java.awt.*;

public class CommandPanel extends CTPanel {
    private static final int DEFAULT_PADDING = 5;
    private static final int LIST_AREA_ROWS = 5;
    private static final int LIST_AREA_COLUMNS = 20;

    private ProtocolTypeComboBox protocolTypeCombo;
    private JComboBox<String> phantomNameCombo;
    private CTTextField indexImageField;
    private CTButton acceptButton;
    private CTButton analysisButton;
    private CTButton minusButton;
    private JTextArea analysisListArea;

    public CommandPanel() {
        super(PanelType.HEADER);
        setTitle("Series 2 analysis");
        initializeComponents();
        setupLayout();
    }

    private void initializeComponents() {
        protocolTypeCombo = new ProtocolTypeComboBox();
        phantomNameCombo = new JComboBox<>(new String[] { "ImageSerQA" });

        indexImageField = new CTTextField();
        indexImageField.setPlaceholder("Image index");

        acceptButton = new CTButton("accept");
        analysisButton = new CTButton("analysis");
        minusButton = new CTButton("-");

        analysisListArea = new JTextArea(LIST_AREA_ROWS, LIST_AREA_COLUMNS);
        analysisListArea.setEditable(false);
    }

    private void setupLayout() {
        JPanel mainPanel = new JPanel(new GBLayout());
        LayoutWorker helper = new LayoutWorker(mainPanel);
        helper.insets(new Insets(DEFAULT_PADDING, DEFAULT_PADDING, DEFAULT_PADDING, DEFAULT_PADDING));

        // 添加输入组件
        addInputComponents(helper);

        // 创建分析列表面板
        JPanel listPanel = createListPanel();

        // 创建按钮面板
        JPanel buttonPanel = createButtonPanel();

        // 设置主布局
        setLayout(new BorderLayout());
        add(mainPanel, BorderLayout.NORTH);
        add(listPanel, BorderLayout.CENTER);
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private void addInputComponents(LayoutWorker helper) {
        helper.position(0, 0).weight(0, 0).add(new CTLabel("Protocol Type"));
        helper.position(1, 0).weight(1, 0).fill(GBC.HORIZONTAL).add(protocolTypeCombo);

        helper.position(2, 0).weight(0, 0).add(new CTLabel("Phantom Name"));
        helper.position(3, 0).weight(1, 0).fill(GBC.HORIZONTAL).add(phantomNameCombo);

        helper.position(4, 0).weight(0, 0).add(new CTLabel("Index Image"));
        helper.position(5, 0).weight(1, 0).fill(GBC.HORIZONTAL).add(indexImageField);

        helper.position(6, 0).weight(0, 0).add(acceptButton);
    }

    private JPanel createListPanel() {
        JPanel listPanel = new JPanel(new BorderLayout());
        JLabel listLabel = new CTLabel("2 analysis list");
        listPanel.add(listLabel, BorderLayout.NORTH);
        listPanel.add(new JScrollPane(analysisListArea), BorderLayout.CENTER);
        return listPanel;
    }

    private JPanel createButtonPanel() {
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.add(minusButton);
        buttonPanel.add(analysisButton);
        return buttonPanel;
    }

    // Getter methods
    public ProtocolTypeComboBox getProtocolTypeCombo() {
        return protocolTypeCombo;
    }

    public JComboBox<String> getPhantomNameCombo() {
        return phantomNameCombo;
    }

    public CTTextField getIndexImageField() {
        return indexImageField;
    }

    public CTButton getAcceptButton() {
        return acceptButton;
    }

    public CTButton getAnalysisButton() {
        return analysisButton;
    }

    public CTButton getMinusButton() {
        return minusButton;
    }

    public JTextArea getAnalysisListArea() {
        return analysisListArea;
    }
}