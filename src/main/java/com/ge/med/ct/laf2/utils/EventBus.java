package com.ge.med.ct.laf2.utils;

import com.ge.med.ct.service.LogManager;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

public class EventBus {
    private static final EventBus INSTANCE = new EventBus();
    private final ConcurrentHashMap<Class<?>, CopyOnWriteArrayList<Consumer<?>>> subscribers = new ConcurrentHashMap<>();

    private EventBus() {
        // 私有构造函数，防止外部实例化
    }

    public static EventBus getInstance() {
        return INSTANCE;
    }

    public <T> void subscribe(Class<T> eventType, Consumer<T> subscriber) {
        subscribers.computeIfAbsent(eventType, k -> new CopyOnWriteArrayList<>()).add(subscriber);
    }

    public <T> void unsubscribe(Class<T> eventType, Consumer<T> subscriber) {
        subscribers.computeIfPresent(eventType, (k, v) -> {
            v.remove(subscriber);
            return v.isEmpty() ? null : v;
        });
    }

    @SuppressWarnings("unchecked")
    public <T> void post(T event) {
        Class<?> eventType = event.getClass();
        CopyOnWriteArrayList<Consumer<?>> eventSubscribers = subscribers.get(eventType);
        if (eventSubscribers != null) {
            eventSubscribers.forEach(subscriber -> {
                try {
                    ((Consumer<T>) subscriber).accept(event);
                } catch (Exception e) {
                    // 处理订阅者异常
                    LogManager.getInstance().getLogger(EventBus.class)
                            .severe("事件处理异常: " + e.getMessage());
                }
            });
        }
    }
}