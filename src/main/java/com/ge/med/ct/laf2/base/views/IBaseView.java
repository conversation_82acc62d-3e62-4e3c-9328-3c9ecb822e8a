package com.ge.med.ct.laf2.base.views;

import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IViewListener;

/**
 * 基础视图接口
 * @param <T> 视图监听器类型，必须是 IViewListener 的子类
 */
public interface IBaseView<T extends IViewListener> {
    /**
     * 显示消息
     * @param type 消息类型
     * @param message 消息内容
     */
    void showMessage(MessageType type, String message);

    /**
     * 清空视图内容
     */
    void clearView();

    /**
     * 设置视图监听器
     * @param listener 视图监听器
     */
    void setViewListener(T listener);

    /**
     * 获取视图监听器
     * @return 视图监听器
     */
    T getViewListener();

    /**
     * 设置数据提供者
     * @param provider 数据提供者
     */
    void setDataProvider(DicomDataProvider provider);

    /**
     * 获取数据提供者
     * @return 数据提供者
     */
    DicomDataProvider getDataProvider();
}