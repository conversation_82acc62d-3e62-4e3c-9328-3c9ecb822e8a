package com.ge.med.ct.dicom2.tag;

import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * DICOM标签配置加载器
 * 负责从外部配置文件加载DICOM标签配置
 * 精简设计：专注于配置加载功能，使用DicomTagConstants统一管理标签
 */
public class TagConfigLoader {
    private static final Logger LOG = Logger.getLogger(TagConfigLoader.class.getName());

    private TagConfigLoader() {
        // 工具类，不应实例化
    }

    /**
     * 标签验证规则接口
     */
    public interface ValidationRule {
        boolean validate(String value);

        String getMessage();
    }

    /**
     * 简单验证规则实现
     */
    public static class SimpleValidationRule implements ValidationRule {
        private final Predicate<String> validator;
        private final String message;

        public SimpleValidationRule(Predicate<String> validator, String message) {
            this.validator = validator;
            this.message = message;
        }

        @Override
        public boolean validate(String value) {
            return validator.test(value);
        }

        @Override
        public String getMessage() {
            return message;
        }
    }

    /**
     * 正则表达式验证规则实现
     */
    public static class RegexValidationRule implements ValidationRule {
        private final Pattern pattern;
        private final String message;

        public RegexValidationRule(String regex, String message) {
            this.pattern = Pattern.compile(regex);
            this.message = message;
        }

        @Override
        public boolean validate(String value) {
            return value != null && pattern.matcher(value).matches();
        }

        @Override
        public String getMessage() {
            return message;
        }
    }

    /**
     * 从XML文件加载DICOM标签配置
     *
     * @param xmlFilePath XML配置文件路径
     * @return 标签映射（标签ID -> 标签名称）
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    public static Map<String, String> loadTagsFromXml(String xmlFilePath) throws DicomException {
        Map<String, String> tagMap = new HashMap<>();

        if (xmlFilePath == null || xmlFilePath.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "XML配置文件路径不能为空");
        }

        File xmlFile = new File(xmlFilePath);
        if (!xmlFile.exists()) {
            LOG.warning("XML配置文件不存在: " + xmlFilePath);
            return tagMap;
        }

        try {
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.parse(xmlFile);
            doc.getDocumentElement().normalize();

            NodeList tagNodes = doc.getElementsByTagName("tag");
            for (int i = 0; i < tagNodes.getLength(); i++) {
                Element tagElement = (Element) tagNodes.item(i);
                String tagId = tagElement.getAttribute("id");
                String tagName = tagElement.getAttribute("name");

                if (!tagId.isEmpty() && !tagName.isEmpty()) {
                    tagMap.put(tagId, tagName);
                }
            }

            LOG.info("从XML加载了 " + tagMap.size() + " 个标签定义");
        } catch (Exception e) {
            LOG.log(Level.SEVERE, "加载XML配置失败: " + e.getMessage(), e);
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_CONFIGURATION,
                    DicomMessages.PROCESSING_ERROR, e, "加载XML配置失败", xmlFilePath);
        }

        return tagMap;
    }

    /**
     * 从文本文件加载配置，通用方法
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    private static <T> T loadConfigFromFile(String configPath, ConfigParser<T> parser) throws DicomException {
        if (configPath == null || configPath.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "配置文件路径不能为空");
        }

        File configFile = new File(configPath);
        if (!configFile.exists()) {
            LOG.warning("配置文件不存在: " + configPath);
            return parser.createEmptyResult();
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                Files.newInputStream(configFile.toPath()), StandardCharsets.UTF_8))) {

            return parser.parse(reader.lines()
                    .filter(line -> !line.startsWith("#") && !line.trim().isEmpty())
                    .collect(Collectors.toList()));

        } catch (IOException e) {
            LOG.severe("加载配置失败: " + e.getMessage());
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_CONFIGURATION,
                    DicomMessages.PROCESSING_ERROR, e, "加载配置失败", configPath);
        } catch (Exception e) {
            LOG.severe("解析配置失败: " + e.getMessage());
            return parser.createEmptyResult();
        }
    }

    /**
     * 配置解析器接口
     */
    private interface ConfigParser<T> {
        T parse(List<String> lines);

        T createEmptyResult();
    }

    /**
     * 加载验证规则
     *
     * @param configPath 配置文件路径
     * @return 验证规则映射
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    public static Map<String, List<ValidationRule>> loadValidationRules(String configPath) throws DicomException {
        return loadConfigFromFile(configPath, new ConfigParser<Map<String, List<ValidationRule>>>() {
            @Override
            public Map<String, List<ValidationRule>> parse(List<String> lines) {
                Map<String, List<ValidationRule>> ruleMap = new HashMap<>();

                for (String line : lines) {
                    String[] parts = line.split("\\|");
                    if (parts.length < 3)
                        continue;

                    String tagId = parts[0].trim();
                    String ruleType = parts[1].trim();
                    String ruleConfig = parts[2].trim();
                    String message = parts.length > 3 ? parts[3].trim() : "验证失败";

                    List<ValidationRule> ruleList = ruleMap.computeIfAbsent(tagId, k -> new ArrayList<>());

                    switch (ruleType.toLowerCase()) {
                        case "regex":
                            ruleList.add(new RegexValidationRule(ruleConfig, message));
                            break;
                        case "required":
                            ruleList.add(new SimpleValidationRule(
                                    value -> value != null && !value.trim().isEmpty(), message));
                            break;
                        case "length":
                            try {
                                int maxLength = Integer.parseInt(ruleConfig);
                                ruleList.add(new SimpleValidationRule(
                                        value -> value != null && value.length() <= maxLength, message));
                            } catch (NumberFormatException e) {
                                LOG.warning("无效的长度限制: " + ruleConfig);
                            }
                            break;
                    }
                }

                LOG.info("从配置文件加载了 " + ruleMap.size() + " 组验证规则");
                return ruleMap;
            }

            @Override
            public Map<String, List<ValidationRule>> createEmptyResult() {
                return new HashMap<>();
            }
        });
    }

    /**
     * 加载标签分类配置
     *
     * @param configPath 配置文件路径
     * @return 标签分类映射（标签ID -> 分类）
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    public static Map<String, DicomTagConstants.TagCategory> loadTagCategories(String configPath)
            throws DicomException {
        return loadConfigFromFile(configPath, new ConfigParser<Map<String, DicomTagConstants.TagCategory>>() {
            @Override
            public Map<String, DicomTagConstants.TagCategory> parse(List<String> lines) {
                Map<String, DicomTagConstants.TagCategory> categoryMap = new HashMap<>();

                for (String line : lines) {
                    String[] parts = line.split("\\|");
                    if (parts.length < 2)
                        continue;

                    String tagId = parts[0].trim();
                    String categoryName = parts[1].trim().toUpperCase();

                    try {
                        DicomTagConstants.TagCategory category = DicomTagConstants.TagCategory.valueOf(categoryName);
                        categoryMap.put(tagId, category);
                    } catch (IllegalArgumentException e) {
                        LOG.warning("无效的标签分类: " + categoryName);
                    }
                }

                LOG.info("从配置文件加载了 " + categoryMap.size() + " 个标签分类");
                return categoryMap;
            }

            @Override
            public Map<String, DicomTagConstants.TagCategory> createEmptyResult() {
                return new HashMap<>();
            }
        });
    }

    /**
     * 加载标签组配置
     *
     * @param configPath 配置文件路径
     * @return 标签组映射（组名 -> 标签ID集合）
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    public static Map<String, Set<String>> loadTagGroups(String configPath) throws DicomException {
        return loadConfigFromFile(configPath, new ConfigParser<Map<String, Set<String>>>() {
            @Override
            public Map<String, Set<String>> parse(List<String> lines) {
                Map<String, Set<String>> groupMap = new HashMap<>();

                for (String line : lines) {
                    String[] parts = line.split("\\|");
                    if (parts.length < 2)
                        continue;

                    String groupName = parts[0].trim();
                    String tagId = parts[1].trim();

                    groupMap.computeIfAbsent(groupName, k -> new HashSet<>()).add(tagId);
                }

                LOG.info("从配置文件加载了 " + groupMap.size() + " 个标签组");
                return groupMap;
            }

            @Override
            public Map<String, Set<String>> createEmptyResult() {
                return new HashMap<>();
            }
        });
    }

    /**
     * 从DICOM数据字典中加载标准标签
     *
     * @return 标签映射（标签ID -> 标签名称）
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    public static Map<String, String> loadStandardTags() throws DicomException {
        Map<String, String> tagMap = new HashMap<>();

        // 使用DicomTagConstants中定义的标签常量
        addStandardTagFromConstant(tagMap, DicomTagConstants.Patient.class);
        addStandardTagFromConstant(tagMap, DicomTagConstants.Study.class);
        addStandardTagFromConstant(tagMap, DicomTagConstants.Series.class);
        addStandardTagFromConstant(tagMap, DicomTagConstants.Image.class);
        addStandardTagFromConstant(tagMap, DicomTagConstants.Equipment.class);

        LOG.info("加载了 " + tagMap.size() + " 个标准DICOM标签");
        return tagMap;
    }

    /**
     * 从DicomTagConstants类的子类中提取标签常量
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    private static void addStandardTagFromConstant(Map<String, String> tagMap, Class<?> constantClass)
            throws DicomException {
        if (tagMap == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "标签映射不能为空");
        }

        if (constantClass == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "常量类不能为空");
        }

        try {
            java.lang.reflect.Field[] fields = constantClass.getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) &&
                        field.getType() == String.class &&
                        !field.getName().endsWith("_TAG")) {

                    String tagId = (String) field.get(null);
                    String name = field.getName();

                    // 将字段名转换为标准标签名 (如 PATIENT_ID -> PatientID)
                    StringBuilder nameBuilder = new StringBuilder();
                    String[] parts = name.split("_");
                    for (String part : parts) {
                        if (!part.isEmpty()) {
                            nameBuilder.append(part.substring(0, 1).toUpperCase());
                            nameBuilder.append(part.substring(1).toLowerCase());
                        }
                    }

                    tagMap.put(tagId, nameBuilder.toString());
                }
            }
        } catch (IllegalAccessException e) {
            LOG.fine("从常量类提取标签失败: " + constantClass.getSimpleName() + " - " + e.getMessage());
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_CONFIGURATION,
                    DicomMessages.PROCESSING_ERROR, e, "从常量类提取标签失败", constantClass.getSimpleName());
        } catch (Exception e) {
            LOG.fine("从常量类提取标签失败: " + constantClass.getSimpleName() + " - " + e.getMessage());
        }
    }

    /**
     * 从资源文件加载标签定义
     *
     * @param resourcePath 资源路径
     * @return 标签映射（标签ID -> 标签名称）
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    public static Map<String, String> loadTagsFromResource(String resourcePath) throws DicomException {
        if (resourcePath == null || resourcePath.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "资源路径不能为空");
        }

        Map<String, String> tagMap = new HashMap<>();

        try (InputStream is = TagConfigLoader.class.getResourceAsStream(resourcePath)) {
            if (is == null) {
                LOG.warning("找不到资源文件: " + resourcePath);
                return tagMap;
            }

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                tagMap = reader.lines()
                        .filter(line -> !line.startsWith("#") && !line.trim().isEmpty())
                        .map(line -> line.split(","))
                        .filter(parts -> parts.length >= 2)
                        .filter(parts -> !parts[0].trim().isEmpty() && !parts[1].trim().isEmpty())
                        .collect(Collectors.toMap(
                                parts -> parts[0].trim(),
                                parts -> parts[1].trim(),
                                (v1, v2) -> v1 // 如果有重复的键，保留第一个
                        ));
            }
        } catch (IOException e) {
            LOG.log(Level.SEVERE, "加载资源标签失败: " + e.getMessage(), e);
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_CONFIGURATION,
                    DicomMessages.PROCESSING_ERROR, e, "加载资源标签失败", resourcePath);
        }

        return tagMap;
    }

    /**
     * 从系统环境变量获取配置路径
     */
    @HandleException(errorCode = ErrorCode.DICOM_CONFIGURATION)
    public static String getConfigPathFromEnv(String envVarName, String defaultPath) throws DicomException {
        if (envVarName == null || envVarName.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "环境变量名不能为空");
        }

        if (defaultPath == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "默认路径不能为空");
        }

        String path = System.getenv(envVarName);
        if (path == null || path.isEmpty()) {
            LOG.warning("未找到环境变量 " + envVarName + "，使用默认配置路径: " + defaultPath);
            return defaultPath;
        }

        return path;
    }

    /**
     * 校验标签ID格式
     *
     * @param tagId 标签ID
     * @return 是否为有效的标签ID
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static boolean isValidTagId(String tagId) {
        if (tagId == null || tagId.trim().isEmpty()) {
            return false;
        }

        // 使用DicomTagConstants进行验证
        return DicomTagConstants.isValidTag(tagId);
    }
}