package com.ge.med.ct.dicom2.tag;

import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.ElementDictionary;
import org.dcm4che3.data.Tag;
import org.dcm4che3.data.VR;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * DICOM标签解析器
 * 负责从DICOM属性中解析标签
 * 精简设计：专注于解析功能
 */
public class DicomTagParser {
    private static final Logger LOG = Logger.getLogger(DicomTagParser.class.getName());
    private static final ElementDictionary DICT = ElementDictionary.getStandardElementDictionary();

    // 标签创建接口
    private final TagCreator tagCreator;

    /**
     * 标签创建接口
     */
    public interface TagCreator {
        /**
         * 创建DICOM标签对象
         */
        DicomTag createTag(String tagId, String value) throws DicomException;

        /**
         * 标准化标签ID
         */
        String normalizeTagId(String tagId);
    }

    /**
     * 构造函数
     */
    public DicomTagParser(TagCreator tagCreator) {
        this.tagCreator = tagCreator;
    }

    /**
     * 兼容旧API构造函数
     */
    public DicomTagParser(DicomTagService tagService) {
        this.tagCreator = new TagCreator() {
            @Override
            public DicomTag createTag(String tagId, String value) throws DicomException {
                try {
                    return tagService.createTag(tagId, value);
                } catch (Exception e) {
                    throw new DicomException(DicomMessages.TAG_CREATE_FAILED, e, e.getMessage());
                }
            }

            @Override
            public String normalizeTagId(String tagId) {
                return DicomTagConstants.normalizeTagId(tagId);
            }
        };
    }

    /**
     * 解析DICOM属性到标签映射
     */
    public Map<String, String> parseTags(Attributes attrs) {
        Map<String, String> tags = new HashMap<>();

        if (attrs == null) {
            LOG.warning("解析失败：DICOM属性为空");
            return tags;
        }

        // 遍历所有标签
        for (int tag : attrs.tags()) {
            try {
                String tagId = DicomTagConstants.tagToString(tag);
                VR vr = attrs.getVR(tag) != null ? attrs.getVR(tag) : DICT.vrOf(tag);
                String value = getStringValue(attrs, tag, vr);

                if (value != null) {
                    tags.put(tagId, value);
                }
            } catch (Exception e) {
                LOG.fine("解析标签失败：" + e.getMessage());
            }
        }

        return tags;
    }

    /**
     * 解析DICOM标签到DicomFileModel
     */
    public DicomFileModel parseDicomTags(DicomFileModel model, Attributes attrs) throws DicomException {
        if (model == null || attrs == null) {
            throw new DicomException(model == null ? DicomMessages.MODEL_NULL : DicomMessages.ATTRIBUTES_EMPTY);
        }

        try {
            // 解析标签并添加到模型
            parseTags(attrs).forEach((tagId, value) -> {
                try {
                    model.addTag(tagId, tagCreator.createTag(tagId, value));
                } catch (Exception e) {
                    LOG.fine("创建标签对象失败：" + e.getMessage());
                }
            });

            // 设置图像相关属性
            if (model.getImage() != null) {
                setImageAttributes(model, attrs);
            }

            // 解析研究相关属性
            parseStudyTags(attrs, model);

            return model;
        } catch (Exception e) {
            throw new DicomException(DicomMessages.TAG_PARSE_FAILED, e, e.getMessage());
        }
    }

    /**
     * 设置图像属性
     */
    private void setImageAttributes(DicomFileModel model, Attributes attrs) {
        // 设置基本图像属性
        setIntAttribute(attrs, Tag.Rows, model.getImage()::setRows, 0);
        setIntAttribute(attrs, Tag.Columns, model.getImage()::setColumns, 0);
        setIntAttribute(attrs, Tag.BitsAllocated, model.getImage()::setBitsAllocated, 16);
        setIntAttribute(attrs, Tag.BitsStored, model.getImage()::setBitsStored, 16);
        setIntAttribute(attrs, Tag.HighBit, model.getImage()::setHighBit, 15);
        setIntAttribute(attrs, Tag.SamplesPerPixel, model.getImage()::setSamplesPerPixel, 1);
        setIntAttribute(attrs, Tag.PixelRepresentation, model.getImage()::setPixelRepresentation, 0);

        if (attrs.contains(Tag.PhotometricInterpretation)) {
            model.getImage().setPhotometricInterpretation(attrs.getString(Tag.PhotometricInterpretation));
        }

        // 解析像素间距
        if (attrs.contains(Tag.PixelSpacing)) {
            try {
                String pixelSpacing = attrs.getString(Tag.PixelSpacing);
                if (pixelSpacing != null && pixelSpacing.contains("\\")) {
                    String[] values = pixelSpacing.split("\\\\");
                    float[] spacingValues = new float[values.length];
                    for (int i = 0; i < values.length; i++) {
                        spacingValues[i] = Float.parseFloat(values[i]);
                    }
                    model.getImage().setPixelSpacing(spacingValues);
                }
            } catch (Exception e) {
                LOG.fine("解析像素间距失败: " + e.getMessage());
            }
        }

        // 窗宽窗位
        setFloatPair(attrs, Tag.WindowCenter, Tag.WindowWidth,
                model.getImage()::setWindowCenter, model.getImage()::setWindowWidth,
                "窗宽窗位");

        // 缩放参数
        setFloatPair(attrs, Tag.RescaleIntercept, Tag.RescaleSlope,
                model.getImage()::setRescaleIntercept, model.getImage()::setRescaleSlope,
                "缩放参数");

        // 图像位置和方向
        try {
            if (attrs.contains(Tag.ImagePositionPatient)) {
                model.getImage().setImagePosition(attrs.getFloats(Tag.ImagePositionPatient));
            }
            if (attrs.contains(Tag.ImageOrientationPatient)) {
                model.getImage().setImageOrientation(attrs.getFloats(Tag.ImageOrientationPatient));
            }
        } catch (Exception e) {
            LOG.log(Level.FINE, "解析图像位置/方向失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置整数属性
     */
    private void setIntAttribute(Attributes attrs, int tag, IntSetter setter, int defaultValue) {
        if (attrs.contains(tag)) {
            setter.set(attrs.getInt(tag, defaultValue));
        }
    }

    /**
     * 整数属性设置器
     */
    @FunctionalInterface
    private interface IntSetter {
        void set(int value);
    }

    /**
     * 设置浮点数对属性
     */
    private void setFloatPair(Attributes attrs, int tag1, int tag2,
            FloatSetter setter1, FloatSetter setter2,
            String description) {
        if (attrs.contains(tag1) && attrs.contains(tag2)) {
            try {
                setter1.set(Float.parseFloat(attrs.getString(tag1)));
                setter2.set(Float.parseFloat(attrs.getString(tag2)));
            } catch (Exception e) {
                LOG.fine("解析" + description + "失败: " + e.getMessage());
            }
        }
    }

    /**
     * 浮点数属性设置器
     */
    @FunctionalInterface
    private interface FloatSetter {
        void set(float value);
    }

    /**
     * 获取标签的字符串值
     */
    private String getStringValue(Attributes attrs, int tag, VR vr) {
        try {
            if (vr == null)
                return attrs.getString(tag);

            // 使用Java 1.8兼容的switch语句
            if (vr == VR.SQ) {
                return null; // 序列类型不支持
            } else if (vr == VR.OW || vr == VR.OB) {
                return "[二进制数据]";
            } else {
                return tag == Tag.PixelData ? "[像素数据]" : attrs.getString(tag);
            }
        } catch (Exception e) {
            LOG.fine("获取标签值失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取所有标签对象
     */
    public List<DicomTag> getAllTags(Attributes attrs) throws DicomException {
        if (attrs == null) {
            throw new DicomException(DicomMessages.ATTRIBUTES_EMPTY);
        }

        List<DicomTag> result = new ArrayList<>();
        parseTags(attrs).forEach((tagId, value) -> {
            try {
                result.add(tagCreator.createTag(tagId, value));
            } catch (Exception e) {
                LOG.fine("创建标签对象失败: " + e.getMessage());
            }
        });

        return result;
    }

    /**
     * 验证必需标签是否存在
     */
    public boolean validateEssentialTags(Attributes attrs) {
        if (attrs == null) {
            return false;
        }

        for (int tag : DicomTagConstants.ESSENTIAL_TAGS) {
            if (!attrs.contains(tag) || attrs.getString(tag) == null || attrs.getString(tag).trim().isEmpty()) {
                String tagName = DICT.keywordOf(tag);
                LOG.warning("缺少必需标签: " + tagName + " (" + DicomTagConstants.tagToString(tag) + ")");
                return false;
            }
        }

        return true;
    }

    private void parseStudyTags(Attributes attrs, DicomFileModel model) {
        try {
            // 研究实例UID
            if (attrs.contains(Tag.StudyInstanceUID)) {
                model.addTag(DicomTagConstants.Study.STUDY_INSTANCE_UID,
                        new DicomTag(DicomTagConstants.Study.STUDY_INSTANCE_UID,
                                attrs.getString(Tag.StudyInstanceUID), VR.UI));
            }

            // 研究ID - 添加默认值处理
            String studyId;
            if (attrs.contains(Tag.StudyID)) {
                studyId = attrs.getString(Tag.StudyID);
            } else {
                // 如果没有StudyID，尝试使用AccessionNumber作为备选
                studyId = attrs.getString(Tag.AccessionNumber);
            }
            if (studyId != null && !studyId.isEmpty()) {
                model.addTag(DicomTagConstants.Study.STUDY_ID,
                        new DicomTag(DicomTagConstants.Study.STUDY_ID, studyId, VR.SH));
            }

            // 研究日期和时间
            if (attrs.contains(Tag.StudyDate)) {
                model.addTag(DicomTagConstants.Study.STUDY_DATE,
                        new DicomTag(DicomTagConstants.Study.STUDY_DATE,
                                attrs.getString(Tag.StudyDate), VR.DA));
            }
            if (attrs.contains(Tag.StudyTime)) {
                model.addTag(DicomTagConstants.Study.STUDY_TIME,
                        new DicomTag(DicomTagConstants.Study.STUDY_TIME,
                                attrs.getString(Tag.StudyTime), VR.TM));
            }

            // 研究描述
            if (attrs.contains(Tag.StudyDescription)) {
                model.addTag(DicomTagConstants.Study.STUDY_DESCRIPTION,
                        new DicomTag(DicomTagConstants.Study.STUDY_DESCRIPTION,
                                attrs.getString(Tag.StudyDescription), VR.LO));
            }

            // 站点名称 - 添加多个备选标签
            String stationName = null;
            if (attrs.contains(Tag.StationName)) {
                stationName = attrs.getString(Tag.StationName);
            } else if (attrs.contains(Tag.InstitutionName)) {
                stationName = attrs.getString(Tag.InstitutionName);
            } else if (attrs.contains(Tag.Manufacturer)) {
                stationName = attrs.getString(Tag.Manufacturer);
            }
            if (stationName != null) {
                model.addTag(DicomTagConstants.Equipment.STATION_NAME,
                        new DicomTag(DicomTagConstants.Equipment.STATION_NAME,
                                stationName, VR.SH));
            }
        } catch (DicomException e) {
            LOG.warning("解析研究标签时发生错误: " + e.getMessage());
        }
    }
}