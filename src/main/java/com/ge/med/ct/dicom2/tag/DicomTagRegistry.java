package com.ge.med.ct.dicom2.tag;

import org.dcm4che3.data.ElementDictionary;
import org.dcm4che3.data.VR;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * DICOM标签注册中心
 * 存储和管理所有标签的信息和元数据
 * 简化版，作为核心集中式标签存储
 */
public class DicomTagRegistry {

    private static final Logger LOG = Logger.getLogger(DicomTagRegistry.class.getName());

    // 使用标准元素字典
    private static final ElementDictionary DICT = ElementDictionary.getStandardElementDictionary();

    // 标签映射，存储所有标签信息
    private final Map<String, TagInfo> tagRegistry = new ConcurrentHashMap<>();

    /**
     * 默认构造函数
     */
    public DicomTagRegistry() {
        LOG.info("创建DICOM标签注册中心");
    }

    /**
     * 使用预定义的标签映射初始化
     *
     * @param tagMap 预定义标签映射
     */
    public DicomTagRegistry(Map<String, TagInfo> tagMap) {
        if (tagMap != null) {
            this.tagRegistry.putAll(tagMap);
        }
        LOG.info("使用预定义映射创建DICOM标签注册中心，共 " + this.tagRegistry.size() + " 个标签");
    }

    /**
     * 注册标签
     *
     * @param tagId 标签ID
     * @param name 标签名称
     * @param vr 值表示类型
     * @param category 标签分类
     */
    public void registerTag(String tagId, String name, VR vr, DicomTagConstants.TagCategory category) {
        if (tagId == null || tagId.trim().isEmpty()) {
            return;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        TagInfo info = new TagInfo(normalizedId, name, vr, category);
        tagRegistry.put(normalizedId, info);
    }

    /**
     * 获取标签名称
     *
     * @param tagId 标签ID
     * @return 标签名称
     */
    public String getTagName(String tagId) {
        if (tagId == null) {
            return "Unknown";
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        TagInfo info = tagRegistry.get(normalizedId);

        if (info != null) {
            return info.getName();
        }

        return DicomTagConstants.getTagName(normalizedId);
    }

    /**
     * 获取标签VR类型
     *
     * @param tagId 标签ID
     * @return 标签VR类型
     */
    public VR getTagVR(String tagId) {
        if (tagId == null) {
            return VR.UN;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        TagInfo info = tagRegistry.get(normalizedId);

        if (info != null && info.getVr() != null) {
            return info.getVr();
        }

        // 尝试从标准字典查找
        try {
            int tag = DicomTagConstants.stringToTag(normalizedId);
            VR vr = DICT.vrOf(tag);
            if (vr != null) {
                return vr;
            }
        } catch (Exception e) {
            // 忽略异常，返回UN
        }

        return VR.UN;
    }

    /**
     * 获取标签分类
     *
     * @param tagId 标签ID
     * @return 标签分类
     */
    public DicomTagConstants.TagCategory getTagCategory(String tagId) {
        if (tagId == null) {
            return DicomTagConstants.TagCategory.OTHER;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        TagInfo info = tagRegistry.get(normalizedId);

        if (info != null) {
            return info.getCategory();
        }

        return DicomTagConstants.getCategoryFromTagId(normalizedId);
    }

    /**
     * 获取所有标签ID
     *
     * @return 所有标签ID集合
     */
    public Set<String> getAllTagIds() {
        return new HashSet<>(tagRegistry.keySet());
    }

    /**
     * 根据标签分类获取标签ID
     *
     * @param category 标签分类
     * @return 指定分类的标签ID集合
     */
    public Set<String> getTagIdsByCategory(DicomTagConstants.TagCategory category) {
        if (category == null) {
            return Collections.emptySet();
        }

        Set<String> result = new HashSet<>();
        for (Map.Entry<String, TagInfo> entry : tagRegistry.entrySet()) {
            if (category.equals(entry.getValue().getCategory())) {
                result.add(entry.getKey());
            }
        }

        return result;
    }

    /**
     * 检查标签是否已注册
     *
     * @param tagId 标签ID
     * @return 是否已注册
     */
    public boolean isTagRegistered(String tagId) {
        if (tagId == null) {
            return false;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        return tagRegistry.containsKey(normalizedId);
    }

    /**
     * 获取标签信息
     *
     * @param tagId 标签ID
     * @return 标签信息
     */
    public TagInfo getTagInfo(String tagId) {
        if (tagId == null) {
            return null;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        return tagRegistry.get(normalizedId);
    }

    /**
     * 清空注册表
     */
    public void clear() {
        tagRegistry.clear();
        LOG.info("DICOM标签注册中心已清空");
    }

    /**
     * 获取注册表大小
     *
     * @return 注册表中的标签数量
     */
    public int size() {
        return tagRegistry.size();
    }

    /**
     * 标签信息内部类
     */
    public static class TagInfo {
        private final String id;
        private final String name;
        private final VR vr;
        private final DicomTagConstants.TagCategory category;

        public TagInfo(String id, String name, VR vr, DicomTagConstants.TagCategory category) {
            this.id = id;
            this.name = name != null ? name : "Unknown";
            this.vr = vr != null ? vr : VR.UN;
            this.category = category != null ? category : DicomTagConstants.TagCategory.OTHER;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public VR getVr() {
            return vr;
        }

        public DicomTagConstants.TagCategory getCategory() {
            return category;
        }
    }
}