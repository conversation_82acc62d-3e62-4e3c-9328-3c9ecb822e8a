package com.ge.med.ct.dicom2.service;

import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.service.IOOperator;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.io.DicomInputStream;
import org.dcm4che3.util.TagUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import java.nio.ByteBuffer;
import java.nio.channels.SeekableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.logging.Logger;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * DICOM验证服务
 * 负责验证DICOM文件的有效性
 */
public class DicomValidationService {
    private static final Logger LOG = Logger.getLogger(DicomValidationService.class.getName());
    private static volatile DicomValidationService instance;

    // DICOM文件标识常量
    protected static final int DICOM_MAGIC_OFFSET = 128; // DICOM文件前128字节为保留区
    protected static final String DICOM_MAGIC_STRING = "DICM"; // DICOM文件标识
    protected static final int DICOM_MAGIC_LENGTH = 4; // DICOM标识长度

    // 使用整数标签常量
    private static final Set<Integer> REQUIRED_TAGS;
    static {
        Set<Integer> tags = new HashSet<>();
        tags.add(Tag.PatientID);
        tags.add(Tag.PatientName);
        tags.add(Tag.StudyInstanceUID);
        tags.add(Tag.SeriesInstanceUID);
        tags.add(Tag.SOPInstanceUID);
        tags.add(Tag.Modality);
        tags.add(Tag.SeriesNumber);
        tags.add(Tag.InstanceNumber);
        REQUIRED_TAGS = Collections.unmodifiableSet(tags);
    }

    // 压缩传输语法UID前缀
    private static final Set<String> COMPRESSED_TRANSFER_SYNTAX_PREFIXES;
    static {
        Set<String> prefixes = new HashSet<>();
        prefixes.add("1.2.840.10008.1.2.4");  // JPEG
        prefixes.add("1.2.840.10008.1.2.5");  // RLE
        prefixes.add("1.2.840.10008.1.2.4.90");  // JPEG 2000
        prefixes.add("1.2.840.10008.1.2.4.91");
        COMPRESSED_TRANSFER_SYNTAX_PREFIXES = Collections.unmodifiableSet(prefixes);
    }
    
    // 配置选项 - 默认设置
    private static boolean STRICT_PATIENT_NAME = false; // 默认不严格检查患者姓名
    private static boolean STRICT_IMAGE_PARAMS = true; // 默认严格检查图像参数
    private static boolean ENABLE_RECOVERY = true; // 默认启用错误恢复

    private DicomValidationService() {
    }

    public static DicomValidationService getInstance() {
        if (instance == null) {
            synchronized (DicomValidationService.class) {
                if (instance == null) {
                    instance = new DicomValidationService();
                }
            }
        }
        return instance;
    }

    /**
     * 检查文件是否为DICOM格式
     *
     * @param filePath 文件路径
     * @return 是否为DICOM文件
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static boolean isDicomFile(String filePath) {
        if (!IOOperator.fileExists(filePath)) {
            return false;
        }

        try (DicomInputStream dis = new DicomInputStream(new File(filePath))) {
            return dis.readDataset() != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查文件是否为DICOM格式
     *
     * @param path 文件路径
     * @return 是否为DICOM文件
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static boolean isDicomFile(Path path) {
        try {
            if (path == null) {
                LOG.warning("检查的文件路径为空");
                return false;
            }

            if (!Files.exists(path)) {
                LOG.warning("文件不存在: " + path);
                return false;
            }

            if (!Files.isRegularFile(path)) {
                LOG.warning("路径不是文件: " + path);
                return false;
            }

            if (!Files.isReadable(path)) {
                LOG.warning("文件无法读取: " + path);
                return false;
            }

            // 检查文件扩展名
            try {
                String fileName = path.getFileName().toString().toLowerCase();
                // 常见的DICOM文件扩展名
                String[] dicomExtensions = { "dcm", "dicom", "dic" };
                for (String ext : dicomExtensions) {
                    if (fileName.endsWith("." + ext)) {
                        LOG.fine("根据扩展名判断为DICOM文件: " + path);
                        // 即使有正确的扩展名，也要检查文件头部
                        // 这样可以确保所有文件都经过相同的验证过程
                    }
                }
            } catch (Exception e) {
                LOG.warning("检查文件扩展名时出错: " + path + ", 原因: " + e.getMessage());
                // 继续检查文件头部
            }

            // 检查文件头部
            try {
                if (Files.size(path) < DICOM_MAGIC_OFFSET + DICOM_MAGIC_LENGTH) {
                    LOG.fine("文件太小，不是DICOM文件: " + path + ", 大小: " + Files.size(path) + " 字节");
                    return false;
                }

                try (SeekableByteChannel channel = Files.newByteChannel(path)) {
                    channel.position(DICOM_MAGIC_OFFSET);
                    ByteBuffer buffer = ByteBuffer.allocate(DICOM_MAGIC_LENGTH);
                    int bytesRead = channel.read(buffer);

                    if (bytesRead != DICOM_MAGIC_LENGTH) {
                        LOG.fine("无法读取DICOM文件标识: " + path + ", 实际读取: " + bytesRead + " 字节");
                        return false;
                    }

                    buffer.flip();
                    byte[] magicBytes = new byte[DICOM_MAGIC_LENGTH];
                    buffer.get(magicBytes);

                    String magic = new String(magicBytes);
                    boolean isDicom = DICOM_MAGIC_STRING.equals(magic);

                    if (!isDicom) {
                        LOG.fine("文件不是DICOM格式: " + path + ", 标识: '" + magic + "'");
                    } else {
                        LOG.fine("根据文件头部判断为DICOM文件: " + path);
                    }

                    return isDicom;
                }
            } catch (IOException e) {
                LOG.warning("读取文件头部时出错: " + path + ", 原因: " + e.getMessage());
                return false;
            }
        } catch (Exception e) {
            LOG.warning("检查DICOM文件时发生意外异常: " + (path != null ? path : "null") + ", 原因: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查文件是否为DICOM格式
     *
     * @param file 文件对象
     * @return 是否为DICOM文件
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static boolean isDicomFile(File file) {
        if (file == null) {
            LOG.warning("检查的文件对象为空");
            return false;
        }

        try {
            return isDicomFile(file.toPath());
        } catch (Exception e) {
            LOG.warning("检查DICOM文件时发生异常: " + file.getAbsolutePath() + ", 原因: " + e.getMessage());
            return false;
        }
    }

    /**
     * 读取并执行DICOM文件的完整验证
     */
    public ValidationResult validateFile(String filePath, DicomFileModel model) {
        ValidationResult result = new ValidationResult();
        if (model == null) {
            result.setErrorMessage("DicomFileModel为空");
            return result;
        }

        if (!IOOperator.fileExists(filePath)) {
            result.setErrorMessage("DICOM文件不存在");
            return result;
        }

        try (DicomInputStream dis = new DicomInputStream(new File(filePath))) {
            validateDicomFile(model, dis.readDataset());
            result.setValid(true);
        } catch (IOException e) {
            LOG.warning("读取失败: " + e.getMessage());
            result.setErrorMessage("读取失败: " + e.getMessage());
        } catch (QAToolException e) {
            LOG.warning("验证失败: " + e.getMessage());
            result.setErrorMessage(e.getMessage());
        } catch (Exception e) {
            LOG.warning("验证错误: " + e.getMessage());
            result.setErrorMessage("验证错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 快速验证DICOM文件基本信息
     */
    public boolean quickValidate(Attributes attributes) {
        if (attributes == null) {
            return false;
        }

        try {
            Map<String, String> requiredValues = new HashMap<>();
            requiredValues.put(DicomTagConstants.Image.SOP_INSTANCE_UID, attributes.getString(Tag.SOPInstanceUID));
            requiredValues.put(DicomTagConstants.Series.MODALITY, attributes.getString(Tag.Modality));
            requiredValues.put(DicomTagConstants.Study.STUDY_INSTANCE_UID, attributes.getString(Tag.StudyInstanceUID));
            requiredValues.put(DicomTagConstants.Series.SERIES_INSTANCE_UID,
                    attributes.getString(Tag.SeriesInstanceUID));

            for (String value : requiredValues.values()) {
                if (value == null || value.isEmpty()) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            LOG.fine("DICOM快速验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证DICOM文件的完整性
     */
    public void validateDicomFile(DicomFileModel model, Attributes attributes) {
        if (model == null || attributes == null) {
            throw new QAToolException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, "DICOM属性为空");
        }

        List<String> errors = new ArrayList<>();

        // 验证必需标签
        for (int tag : REQUIRED_TAGS) {
            // 如果是PatientName并且不需要严格验证，则跳过
            if (tag == Tag.PatientName && !STRICT_PATIENT_NAME) {
                continue;
            }
            
            if (!attributes.contains(tag)) {
                errors.add("缺少必需标签: " + TagUtils.toString(tag));
            }
        }

        try {
            // 验证像素数据 - 只在需要严格验证图像参数时进行
            if (STRICT_IMAGE_PARAMS) {
                validatePixelData(attributes, errors);
            }
        } catch (IOException e) {
            if (STRICT_IMAGE_PARAMS) {
                errors.add("验证像素数据时出错: " + e.getMessage());
            } else {
                // 不严格验证时只记录警告
                LOG.warning("验证像素数据时出错: " + e.getMessage());
            }
        }

        // 验证图像参数
        validateImageParameters(attributes, errors);

        // 验证患者信息
        validatePatientInfo(attributes, errors);

        // 验证检查和序列信息
        validateStudyAndSeriesInfo(attributes, errors);

        // 如果有任何错误且未启用恢复模式，抛出异常
        if (!errors.isEmpty() && !ENABLE_RECOVERY) {
            throw new QAToolException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR,
                    String.join("; ", errors));
        } else if (!errors.isEmpty()) {
            // 启用了恢复模式，记录警告但不抛出异常
            LOG.warning("DICOM验证发现问题但继续处理: " + String.join("; ", errors));
        }
    }

    private void validatePixelData(Attributes attributes, List<String> errors) throws IOException {
        if (isCompressedImageFormat(attributes)) {
            byte[] pixelData = attributes.getBytes(Tag.PixelData);
            if (pixelData == null || pixelData.length == 0) {
                errors.add("像素数据为空");
            }
            return;
        }

        int rows = attributes.getInt(Tag.Rows, -1);
        int columns = attributes.getInt(Tag.Columns, -1);
        if (rows <= 0 || columns <= 0) {
            errors.add("无效的图像尺寸");
            return;
        }

        int bitsAllocated = attributes.getInt(Tag.BitsAllocated, -1);
        if (bitsAllocated <= 0 || bitsAllocated % 8 != 0) {
            errors.add("无效的位分配");
            return;
        }

        int samplesPerPixel = attributes.getInt(Tag.SamplesPerPixel, -1);
        if (samplesPerPixel <= 0) {
            errors.add("无效的每像素样本数");
            return;
        }

        byte[] pixelData = attributes.getBytes(Tag.PixelData);
        if (pixelData == null || pixelData.length == 0) {
            errors.add("像素数据为空");
            return;
        }

        long expectedSize = (long) rows * columns * (bitsAllocated / 8) * samplesPerPixel;
        if (expectedSize > Integer.MAX_VALUE) {
            errors.add("图像尺寸超出限制");
        } else if (pixelData.length != expectedSize) {
            errors.add("像素数据大小不匹配");
        }
    }

    /**
     * 检查文件头部是否包含DICOM标识
     */
    public boolean hasDicomHeader(File file) {
        try {
            if (file == null) {
                LOG.warning("检查的文件对象为空");
                return false;
            }

            if (!file.exists()) {
                LOG.warning("文件不存在: " + file.getAbsolutePath());
                return false;
            }

            if (!file.isFile()) {
                LOG.warning("路径不是文件: " + file.getAbsolutePath());
                return false;
            }

            if (!file.canRead()) {
                LOG.warning("文件无法读取: " + file.getAbsolutePath());
                return false;
            }

            if (file.length() < DICOM_MAGIC_OFFSET + DICOM_MAGIC_LENGTH) {
                LOG.warning("文件太小，不是DICOM文件: " + file.getAbsolutePath() + ", 大小: " + file.length() + " 字节");
                return false;
            }

            try (FileInputStream fis = new FileInputStream(file)) {
                // 跳过前128字节
                long bytesSkipped = 0;
                long totalToSkip = DICOM_MAGIC_OFFSET;

                while (bytesSkipped < totalToSkip) {
                    long skipped = fis.skip(totalToSkip - bytesSkipped);
                    if (skipped <= 0) {
                        LOG.warning("无法跳过文件头部: " + file.getAbsolutePath());
                        return false;
                    }
                    bytesSkipped += skipped;
                }

                byte[] magicBytes = new byte[DICOM_MAGIC_LENGTH];
                int bytesRead = fis.read(magicBytes);

                if (bytesRead != DICOM_MAGIC_LENGTH) {
                    LOG.warning("无法读取DICOM文件标识: " + file.getAbsolutePath() + ", 实际读取: " + bytesRead + " 字节");
                    return false;
                }

                String magic = new String(magicBytes);
                boolean isDicom = DICOM_MAGIC_STRING.equals(magic);

                if (!isDicom) {
                    LOG.fine("文件不是DICOM格式: " + file.getAbsolutePath() + ", 标识: '" + magic + "'");
                }

                return isDicom;
            } catch (IOException e) {
                LOG.warning("检查DICOM文件头部时出错: " + file.getAbsolutePath() + ", 原因: " + e.getMessage());
                return false;
            }
        } catch (Exception e) {
            LOG.warning("检查DICOM文件头部时发生意外异常: " + (file != null ? file.getAbsolutePath() : "null") + ", 原因: "
                    + e.getMessage());
            return false;
        }
    }

    private boolean isCompressedImageFormat(Attributes attributes) {
        String transferSyntaxUID = attributes.getString(Tag.TransferSyntaxUID);
        if (transferSyntaxUID == null) {
            return false;
        }
        return COMPRESSED_TRANSFER_SYNTAX_PREFIXES.stream()
                .anyMatch(prefix -> transferSyntaxUID.startsWith(prefix));
    }

    private void validateImageParameters(Attributes attributes, List<String> errors) {
        // 如果不需要严格验证图像参数，则直接返回
        if (!STRICT_IMAGE_PARAMS) {
            return;
        }
        
        int rows = attributes.getInt(Tag.Rows, 0);
        int columns = attributes.getInt(Tag.Columns, 0);
        int bitsAllocated = attributes.getInt(Tag.BitsAllocated, 0);
        int bitsStored = attributes.getInt(Tag.BitsStored, 0);
        int highBit = attributes.getInt(Tag.HighBit, 0);

        if (rows <= 0 || columns <= 0) {
            errors.add("无效的图像尺寸");
        }

        if (bitsStored <= 0 || highBit < 0 || bitsStored > bitsAllocated || highBit >= bitsAllocated) {
            errors.add("无效的位参数");
        }
    }

    private void validatePatientInfo(Attributes attributes, List<String> errors) {
        String patientId = attributes.getString(Tag.PatientID);
        String patientName = attributes.getString(Tag.PatientName);

        if (patientId == null || patientId.trim().isEmpty()) {
            errors.add("患者ID不能为空");
        }

        // 只在严格验证患者姓名时检查
        if (STRICT_PATIENT_NAME && (patientName == null || patientName.trim().isEmpty())) {
            errors.add("患者姓名不能为空");
        }
    }

    private void validateStudyAndSeriesInfo(Attributes attributes, List<String> errors) {
        String studyInstanceUID = attributes.getString(Tag.StudyInstanceUID);
        String seriesInstanceUID = attributes.getString(Tag.SeriesInstanceUID);
        String seriesNumber = attributes.getString(Tag.SeriesNumber);
        String modality = attributes.getString(Tag.Modality);
        String sopInstanceUID = attributes.getString(Tag.SOPInstanceUID);

        if (studyInstanceUID == null || studyInstanceUID.trim().isEmpty()) {
            errors.add("检查实例UID不能为空");
        }

        if (seriesInstanceUID == null || seriesInstanceUID.trim().isEmpty()) {
            errors.add("序列实例UID不能为空");
        }

        if (seriesNumber == null || seriesNumber.trim().isEmpty()) {
            errors.add("序列号不能为空");
        }

        if (modality == null || modality.trim().isEmpty()) {
            errors.add("模态不能为空");
        }

        if (sopInstanceUID == null || sopInstanceUID.trim().isEmpty()) {
            errors.add("SOP实例UID不能为空");
        }
    }

    /**
     * DICOM验证结果类
     */
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;

        public ValidationResult() {
            this.valid = false;
            this.errorMessage = "";
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage != null ? errorMessage : "";
        }
    }
}