package com.ge.med.ct.dicom2.tag;

import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.VR;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * DICOM标签工厂类
 * 提供创建DICOM标签对象的工厂方法
 * 精简设计：直接使用DicomTagConstants获取标签元数据
 */
public class DicomTagFactory {
    private static final Logger LOG = Logger.getLogger(DicomTagFactory.class.getName());

    // 缓存常用的空标签实例
    private final Map<String, DicomTag> emptyTagCache = new ConcurrentHashMap<>();

    /**
     * 标签元数据提供者接口
     */
    public interface MetadataProvider {
        String getTagName(String tagId);

        VR getTagVR(String tagId);

        boolean isPrivateTag(String tagId);

        String normalizeTagId(String tagId);
    }

    // 默认元数据提供者
    private final MetadataProvider metadataProvider;

    /**
     * 默认构造函数
     */
    public DicomTagFactory() {
        this.metadataProvider = new DefaultMetadataProvider();
    }

    /**
     * 带元数据提供者的构造函数
     *
     * @param metadataProvider 元数据提供者
     */
    public DicomTagFactory(MetadataProvider metadataProvider) {
        this.metadataProvider = metadataProvider != null ? metadataProvider : new DefaultMetadataProvider();
    }

    /**
     * 创建标签
     *
     * @param tagId 标签ID
     * @param value 标签值
     * @return 创建的标签
     * @throws DicomException 如果创建失败
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public DicomTag createTag(String tagId, String value) throws DicomException {
        if (tagId == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.TAG_INVALID,
                    "tagId", "null");
        }

        String normalizedId = metadataProvider.normalizeTagId(tagId);
        String name = metadataProvider.getTagName(normalizedId);
        VR vr = metadataProvider.getTagVR(normalizedId);
        boolean isPrivate = metadataProvider.isPrivateTag(normalizedId);

        try {
            return new DicomTag(normalizedId, name, value, vr, null, isPrivate);
        } catch (DicomException e) {
            // 直接重新抛出DicomException
            throw e;
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.TAG_PROCESSING,
                    DicomMessages.TAG_CREATE_FAILED, e, normalizedId);
        }
    }

    /**
     * 创建标准标签
     *
     * @param tagId 标签ID
     * @param value 标签值
     * @return 新的DicomTag实例
     * @throws DicomException 如果创建失败
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public DicomTag createStandardTag(String tagId, String value) throws DicomException {
        if (tagId == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.TAG_INVALID,
                    "tagId", "null");
        }

        try {
            String normalizedId = DicomTagConstants.normalizeTagId(tagId);
            String name = DicomTagConstants.getTagName(normalizedId);
            VR vr = DicomTagConstants.getTagVR(normalizedId);

            return new DicomTag(normalizedId, name, value, vr, null, DicomTagConstants.isPrivateTag(normalizedId));
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.TAG_PROCESSING,
                    DicomMessages.TAG_CREATE_FAILED, e, tagId);
        }
    }

    /**
     * 获取空标签（预创建的标签实例，值为空）
     *
     * @param tagId 标签ID
     * @return 空标签实例
     * @throws DicomException 如果创建失败
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public DicomTag getEmptyTag(String tagId) throws DicomException {
        if (tagId == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.TAG_INVALID,
                    "tagId", "null");
        }

        String normalizedId = metadataProvider.normalizeTagId(tagId);

        // 检查缓存
        DicomTag cachedTag = emptyTagCache.get(normalizedId);
        if (cachedTag != null) {
            return cachedTag;
        }

        // 创建新标签并缓存
        DicomTag emptyTag = createTag(normalizedId, "");
        emptyTagCache.put(normalizedId, emptyTag);
        return emptyTag;
    }

    /**
     * 批量创建指定类别的标签
     *
     * @param tagIds 标签ID数组
     * @return 标签映射
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, DicomTag> createEmptyTags(String... tagIds) {
        Map<String, DicomTag> tags = new HashMap<>();
        if (tagIds == null) {
            return tags;
        }

        for (String tagId : tagIds) {
            try {
                tags.put(tagId, getEmptyTag(tagId));
            } catch (DicomException e) {
                LOG.log(Level.WARNING, "创建标签失败: " + tagId, e);
            }
        }

        return tags;
    }

    /**
     * 获取常用患者标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, DicomTag> getPatientTags() {
        return createEmptyTags(
                DicomTagConstants.Patient.PATIENT_ID,
                DicomTagConstants.Patient.PATIENT_NAME,
                DicomTagConstants.Patient.PATIENT_BIRTH_DATE,
                DicomTagConstants.Patient.PATIENT_SEX);
    }

    /**
     * 获取常用研究标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, DicomTag> getStudyTags() {
        return createEmptyTags(
                DicomTagConstants.Study.STUDY_INSTANCE_UID,
                DicomTagConstants.Study.STUDY_ID,
                DicomTagConstants.Study.STUDY_DATE,
                DicomTagConstants.Study.STUDY_DESCRIPTION);
    }

    /**
     * 获取常用序列标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, DicomTag> getSeriesTags() {
        return createEmptyTags(
                DicomTagConstants.Series.SERIES_INSTANCE_UID,
                DicomTagConstants.Series.SERIES_NUMBER,
                DicomTagConstants.Series.SERIES_DESCRIPTION,
                DicomTagConstants.Series.MODALITY);
    }

    /**
     * 获取常用图像标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, DicomTag> getImageTags() {
        return createEmptyTags(
                DicomTagConstants.Image.SOP_INSTANCE_UID,
                DicomTagConstants.Image.INSTANCE_NUMBER,
                DicomTagConstants.Image.IMAGE_TYPE);
    }

    /**
     * 获取常用设备标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, DicomTag> getEquipmentTags() {
        return createEmptyTags(
                DicomTagConstants.Equipment.MANUFACTURER,
                DicomTagConstants.Equipment.DEVICE_SERIAL_NUMBER,
                DicomTagConstants.Equipment.STATION_NAME);
    }

    /**
     * 获取基本地必需标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, DicomTag> getEssentialTags() {
        return createEmptyTags(DicomTagConstants.ESSENTIAL_TAG_IDS);
    }

    /**
     * 清除缓存
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public void clearCache() {
        emptyTagCache.clear();
    }

    /**
     * 默认的元数据提供者实现
     */
    private static class DefaultMetadataProvider implements MetadataProvider {
        @Override
        public String getTagName(String tagId) {
            return DicomTagConstants.getTagName(tagId);
        }

        @Override
        public VR getTagVR(String tagId) {
            return DicomTagConstants.getTagVR(tagId);
        }

        @Override
        public boolean isPrivateTag(String tagId) {
            return DicomTagConstants.isPrivateTag(tagId);
        }

        @Override
        public String normalizeTagId(String tagId) {
            return DicomTagConstants.normalizeTagId(tagId);
        }
    }
}