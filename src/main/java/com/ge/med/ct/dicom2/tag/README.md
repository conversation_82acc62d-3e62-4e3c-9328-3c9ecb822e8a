# DICOM 标签管理模块

本模块提供了 DICOM 标签的管理、注册、查询和转换功能，采用统一的架构设计，确保代码清晰、高效和可维护。

## 模块架构

模块采用面向对象设计，将功能分解为相互协作的类：

### 核心类

1. **DicomTagConstants.java** - 基础类，存储标签常量和基本工具方法
   - 定义所有标准DICOM标签常量
   - 提供标签ID格式化、转换等工具方法
   - 定义标签类别枚举

2. **DicomTag.java** - 数据类，表示单个DICOM标签
   - 包含ID、名称、值等基本属性
   - 纯数据对象，不包含业务逻辑

3. **DicomTagRegistry.java** - 注册中心，存储标签元数据
   - 集中管理所有已注册标签
   - 提供标签查询和元数据访问方法

4. **DicomTagManager.java** - 管理器，负责标签注册和分组
   - 整合了原有TagGroupManager和TagRegistrator功能
   - 处理标签分组管理
   - 支持从配置文件批量加载标签

5. **DicomTagService.java** - 服务类，提供标签元数据访问和查询
   - 整合了原有TagMetadataProvider和TagQueryService功能
   - 提供对外标签服务接口
   - 支持标签的复杂查询和过滤

6. **DicomTagValueConverter.java** - 工具类，处理标签值类型转换
   - 提供各种类型转换方法
   - 处理多值属性和特殊编码

7. **TagConfigLoader.java** - 配置加载器，处理外部配置文件加载
   - 支持XML和文本格式配置文件
   - 提供标签验证规则

### 兼容性类

为了保持与现有代码的兼容性，我们提供了以下兼容层类：

1. **DicomTags.java** - 兼容层，提供与旧版DicomTags类的兼容性
   - 直接引用DicomTagConstants中的标签常量
   - 提供常用工具方法的转发
   - 仅用于向后兼容，新代码应直接使用DicomTagConstants

2. **StandardDicomTags.java** - 兼容层，提供与旧版StandardDicomTags类的兼容性
   - 定义TagCategory枚举与DicomTagConstants.TagCategory的转换
   - 提供常用标签常量的引用
   - 仅用于向后兼容，新代码应直接使用DicomTagConstants

## 功能特性

1. **标签常量管理**
   - 统一存储和访问所有标准标签
   - 按类别组织，如Patient、Study、Series等

2. **标签注册和分组**
   - 动态注册自定义标签
   - 支持灵活的标签分组
   - 预定义标准分组(STANDARD, GE_CT, PATIENT等)

3. **元数据查询**
   - 根据标签ID查询名称、VR类型、分类等
   - 支持复杂查询条件组合
   - 高效缓存机制

4. **值类型转换**
   - 支持多种DICOM值表示格式(VR)转换
   - 处理多值属性和编码转换
   - 类型安全的转换方法

5. **配置加载**
   - 从XML、JSON或文本文件加载配置
   - 支持默认配置和自定义配置
   - 标签验证和错误处理

6. **向后兼容性**
   - 保持与现有代码的兼容性
   - 无缝支持旧API调用
   - 提供清晰的迁移路径

## 使用示例

### 标签注册和查询

```java
// 创建基础组件
DicomTagRegistry registry = new DicomTagRegistry();
DicomTagManager manager = new DicomTagManager(registry);
DicomTagService service = new DicomTagService(registry, manager);

// 注册标准标签
manager.registerAllStandardTags();

// 查询标签信息
String patientNameTag = DicomTagConstants.Patient.PATIENT_NAME;
String name = service.getTagName(patientNameTag);
VR vr = service.getTagVR(patientNameTag);
```

### 标签值转换

```java
// 转换标签值
String floatValue = "123.45";
Float parsed = DicomTagValueConverter.getFloatValue(floatValue);

// 处理多值属性
String multiValue = "1.2\\3.4\\5.6";
String[] values = DicomTagValueConverter.parseMultiValue(multiValue);
```

### 批量加载标签

```java
// 从配置文件加载标签
manager.registerTagsFromConfig("config/tags.xml");
manager.loadGroupsFromConfig("config/groups.txt");

// 获取特定组的标签
Set<String> patientTags = service.getTagIdsByGroup("PATIENT");
```

### 使用兼容类

```java
// 使用DicomTags (兼容旧版API)
String patientId = DicomTags.PATIENT_ID;
boolean isPrivate = DicomTags.isPrivateTag(patientId);

// 使用StandardDicomTags (兼容旧版API)
StandardDicomTags.TagCategory category = StandardDicomTags.getTagCategory(patientId);
```

## 设计原则

1. **单一职责原则** - 每个类都有明确的职责
2. **开放封闭原则** - 易于扩展，无需修改现有代码
3. **依赖倒置原则** - 高层组件依赖抽象而非实现
4. **接口隔离原则** - 提供精确的功能接口
5. **向后兼容原则** - 保持对现有代码的兼容性

## 未来扩展

1. 支持更多标签标准和厂商扩展
2. 增强配置文件格式和处理能力
3. 添加更多验证和合规性检查
4. 提供性能优化，特别是对大量标签的处理
5. 逐步废弃兼容类，完全迁移到新API 