package com.ge.med.ct.dicom2.tag;

import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.VR;

/**
 * DICOM标签类
 * 表示DICOM文件中的标签及其值
 * 设计为不可变类，以避免意外状态修改
 * 重构：使用DicomTagValueConverter转换值类型，移除静态工厂方法至DicomTagFactory
 */
public final class DicomTag {

    private final String tagId; // 标签16进制ID
    private final String value; // 标签值（使用final以保证不可变性）
    private final VR vr; // 值表示类型
    private final String name; // 标签名称
    private final String description; // 标签描述
    private final boolean isPrivate; // 是否为私有标签

    /**
     * 构造函数
     *
     * @param tagId       标签ID，16进制格式，如 "(0010,0010)"
     * @param name        标签名称
     * @param value       标签值
     * @param vr          值表示类型
     * @param description 描述（可选）
     * @param isPrivate   是否为私有标签
     * @throws DicomException 如果参数无效
     */
    public DicomTag(String tagId, String name, String value, VR vr, String description, boolean isPrivate)
            throws DicomException {
        if (tagId == null || tagId.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.TAG_PROCESSING, DicomMessages.TAG_INVALID,
                    "tagId", "null");
        }

        this.tagId = DicomTagConstants.normalizeTagId(tagId);
        this.name = (name != null && !name.isEmpty()) ? name : "Tag-" + this.tagId;
        this.value = (value != null) ? value : "";
        this.vr = vr != null ? vr : VR.UN;
        this.description = (description != null) ? description : "DICOM标签 " + this.name;
        this.isPrivate = isPrivate || DicomTagConstants.isPrivateTag(tagId);
    }

    /**
     * 简化构造函数
     *
     * @param tagId 标签ID
     * @param value 标签值
     * @param vr    值表示类型
     */
    public DicomTag(String tagId, String value, VR vr) throws DicomException {
        this(tagId, DicomTagConstants.getTagName(tagId), value, vr, null, DicomTagConstants.isPrivateTag(tagId));
    }

    /**
     * 简化构造函数 - 只指定标签ID
     *
     * @param tagId 标签ID
     */
    public DicomTag(String tagId) throws DicomException {
        this(tagId, DicomTagConstants.getTagName(tagId), "", DicomTagConstants.getTagVR(tagId), null,
                DicomTagConstants.isPrivateTag(tagId));
    }

    /**
     * 创建带有新值的标签副本
     * 由于标签是不可变的，需要创建新实例来更新值
     *
     * @param newValue 新的标签值
     * @return 带有新值的标签副本
     * @throws DicomException 如果创建失败
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public DicomTag withValue(String newValue) throws DicomException {
        return new DicomTag(this.tagId, this.name, newValue, this.vr, this.description, this.isPrivate);
    }

    /**
     * 获取标签组
     */
    public int getGroup() {
        return DicomTagValueConverter.getGroupFromTagId(this.tagId);
    }

    /**
     * 获取标签元素
     */
    public int getElement() {
        return DicomTagValueConverter.getElementFromTagId(this.tagId);
    }

    /**
     * 获取标签值（字符串形式）
     */
    public String getValueAsString() {
        return this.value;
    }

    /**
     * 获取标签值（整数形式）
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Integer getValueAsInteger() {
        return DicomTagValueConverter.getIntegerValue(this.value);
    }

    /**
     * 获取标签值（浮点形式）
     */
    public Float getValueAsFloat() {
        return DicomTagValueConverter.getFloatValue(this.value);
    }

    /**
     * 获取标签值（双精度浮点形式）
     */
    public Double getValueAsDouble() {
        return DicomTagValueConverter.getDoubleValue(this.value);
    }

    /**
     * 获取标签值（字节数组形式）
     */
    public byte[] getValueAsBytes() {
        return DicomTagValueConverter.getBytesValue(this.value, this.vr);
    }

    /**
     * 获取原始值对象（根据VR类型返回适当的Java对象）
     */
    public Object getRawValue() {
        return DicomTagValueConverter.getRawValue(this.value, this.vr);
    }

    /**
     * 获取标签ID
     */
    public String getTagId() {
        return tagId;
    }

    /**
     * 获取标签名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取值表示类型
     */
    public VR getVr() {
        return vr;
    }

    /**
     * 获取标签描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为私有标签
     */
    public boolean isPrivate() {
        return isPrivate;
    }

    /**
     * 获取标签分类
     */
    public DicomTagConstants.TagCategory getCategory() {
        return DicomTagConstants.getCategoryFromTagId(this.tagId);
    }

    @Override
    public String toString() {
        return String.format("%s[%s]=\"%s\"", this.name, this.tagId, this.value);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;

        DicomTag other = (DicomTag) obj;
        return this.tagId.equals(other.tagId) &&
                ((this.value == null && other.value == null) ||
                        (this.value != null && this.value.equals(other.value)));
    }

    @Override
    public int hashCode() {
        // 使用标签ID和值计算哈希码
        final int prime = 31;
        int result = 1;
        result = prime * result + ((tagId == null) ? 0 : tagId.hashCode());
        result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }
}