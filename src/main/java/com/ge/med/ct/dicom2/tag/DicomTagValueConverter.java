package com.ge.med.ct.dicom2.tag;

import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

import java.nio.charset.StandardCharsets;

/**
 * DICOM标签值转换工具类
 */
public final class DicomTagValueConverter {

    private DicomTagValueConverter() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 将标签值转换为字符串
     */
    public static String getStringValue(Object value) {
        if (value == null) {
            return "";
        }
        return value.toString();
    }

    /**
     * 将标签值转换为整数
     */
    public static Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }

        String strValue = value.toString().trim();
        if (strValue.isEmpty()) {
            return null;
        }

        try {
            if (strValue.contains("\\")) {
                // 处理多值属性，返回第一个值
                String firstValue = strValue.split("\\\\")[0].trim();
                return Integer.parseInt(firstValue);
            } else {
                return Integer.parseInt(strValue);
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 将标签值转换为浮点数
     */
    public static Float getFloatValue(Object value) {
        if (value == null) {
            return null;
        }

        String strValue = value.toString().trim();
        if (strValue.isEmpty()) {
            return null;
        }

        try {
            if (strValue.contains("\\")) {
                // 处理多值属性，返回第一个值
                String firstValue = strValue.split("\\\\")[0].trim();
                return Float.parseFloat(firstValue);
            } else {
                return Float.parseFloat(strValue);
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 将标签值转换为双精度浮点数
     */
    public static Double getDoubleValue(Object value) {
        if (value == null) {
            return null;
        }

        String strValue = value.toString().trim();
        if (strValue.isEmpty()) {
            return null;
        }

        try {
            if (strValue.contains("\\")) {
                // 处理多值属性，返回第一个值
                String firstValue = strValue.split("\\\\")[0].trim();
                return Double.parseDouble(firstValue);
            } else {
                return Double.parseDouble(strValue);
            }
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 将标签值转换为字节数组
     */
    public static byte[] getBytesValue(Object value, VR vr) {
        if (value == null) {
            return new byte[0];
        }

        String strValue = value.toString();
        if (strValue.isEmpty()) {
            return new byte[0];
        }

        // 根据VR类型不同的解析策略
        if (vr == VR.OB || vr == VR.UN) {
            // 二进制数据，假设是以十六进制字符串形式存储
            return hexStringToByteArray(strValue);
        } else {
            // 常规文本转为UTF-8字节
            return strValue.getBytes(StandardCharsets.UTF_8);
        }
    }

    /**
     * 根据VR类型获取合适的Java对象
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public static Object getRawValue(Object value, VR vr) throws DicomException {
        if (value == null) {
            return null;
        }

        String strValue = value.toString();

        // 根据VR类型返回合适的值
        if (vr == VR.DS || vr == VR.FD) {
            return getDoubleValue(strValue);
        } else if (vr == VR.FL) {
            return getFloatValue(strValue);
        } else if (vr == VR.IS || vr == VR.SL || vr == VR.SS || vr == VR.US || vr == VR.UL) {
            return getIntegerValue(strValue);
        } else if (vr == VR.OB || vr == VR.OW || vr == VR.UN) {
            return getBytesValue(strValue, vr);
        } else {
            return strValue;
        }
    }

    /**
     * 转换十六进制字符串到字节数组
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public static byte[] hexStringToByteArray(String hex) throws DicomException {
        if (hex == null || hex.isEmpty()) {
            return new byte[0];
        }

        // 确保偶数长度
        if (hex.length() % 2 != 0) {
            hex = "0" + hex;
        }

        int len = hex.length();
        byte[] data = new byte[len / 2];

        try {
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                        + Character.digit(hex.charAt(i + 1), 16));
            }
        } catch (Exception e) {
            return new byte[0];
        }

        return data;
    }

    /**
     * 获取标签组号
     */
    public static int getGroupFromTagId(String tagId) {
        try {
            int tag = DicomTagConstants.stringToTag(tagId);
            return (tag >> 16) & 0xFFFF;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取标签元素号
     */
    public static int getElementFromTagId(String tagId) {
        try {
            int tag = DicomTagConstants.stringToTag(tagId);
            return tag & 0xFFFF;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 解析多值DICOM属性，返回字符串数组
     */
    public static String[] parseMultiValue(String multiValue) {
        if (multiValue == null || multiValue.isEmpty()) {
            return new String[0];
        }

        return multiValue.split("\\\\");
    }

    /**
     * 转换整数标签到字符串表示
     */
    public static String intTagToString(int tagInt) {
        return DicomTagConstants.tagToString(tagInt);
    }
}