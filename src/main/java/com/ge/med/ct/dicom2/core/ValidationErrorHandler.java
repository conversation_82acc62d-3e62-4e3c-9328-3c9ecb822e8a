package com.ge.med.ct.dicom2.core;

import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.dicom2.reader.DicomMetadataReader;
import com.ge.med.ct.service.LogManager;

import java.io.File;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * 验证错误处理器
 * 负责收集、分组和记录验证错误和警告
 */
public class ValidationErrorHandler {
    private static final Logger LOG = LogManager.getInstance().getLogger(ValidationErrorHandler.class);

    private final boolean detailedWarnings;
    private final boolean groupWarnings;
    private final int maxExamples;
    private final DicomMetadataReader metadataReader;

    // 按错误类型分组的文件列表
    private final Map<String, List<String>> errorTypeToFiles = new HashMap<>();

    public ValidationErrorHandler(ConfigManager configManager, DicomMetadataReader metadataReader) {
        this.detailedWarnings = configManager.getBoolean("dicom.validation.detailed_warnings", true);
        this.groupWarnings = configManager.getBoolean("dicom.validation.group_warnings", true);
        this.maxExamples = configManager.getInt("dicom.validation.max_examples", 10);
        this.metadataReader = metadataReader;
    }

    /**
     * 记录验证错误
     *
     * @param filePath     文件路径
     * @param errorType    错误类型
     * @param errorMessage 错误消息
     */
    public void recordError(String filePath, String errorType, String errorMessage) {
        String safeErrorType = errorType != null ? errorType : "UNKNOWN_ERROR";

        // 只在详细警告模式下记录单个文件的错误
        // 这样可以避免与其他组件的日志重复
        if (detailedWarnings) {
            String fileName = new File(filePath).getName();
            LOG.fine("验证记录: 文件 " + fileName + " 验证失败: " + safeErrorType);
        }

        // 如果需要分组，则收集错误
        if (groupWarnings) {
            errorTypeToFiles.computeIfAbsent(safeErrorType, key -> new ArrayList<>()).add(filePath);
        }
    }

    /**
     * 记录验证警告
     *
     * @param filePath       文件路径
     * @param warningType    警告类型
     * @param warningMessage 警告消息
     */
    public void recordWarning(String filePath, String warningType, String warningMessage) {
        String safeWarningType = warningType != null ? warningType : "UNKNOWN_WARNING";

        // 如果需要详细警告，则记录单个文件的警告
        if (detailedWarnings) {
            String fileName = new File(filePath).getName();
            LOG.fine("验证记录: 文件 " + fileName + " 验证警告: " + safeWarningType);
        }

        // 如果需要分组，则收集警告
        if (groupWarnings) {
            errorTypeToFiles.computeIfAbsent(safeWarningType, key -> new ArrayList<>()).add(filePath);
        }
    }

    /**
     * 生成汇总报告
     */
    public void generateSummaryReport() {
        if (!groupWarnings || errorTypeToFiles.isEmpty()) {
            return;
        }

        LOG.warning("验证汇总报告:");

        for (Map.Entry<String, List<String>> entry : errorTypeToFiles.entrySet()) {
            String errorType = entry.getKey();
            List<String> files = entry.getValue();

            LOG.warning("- " + errorType + ": 影响了 " + files.size() + " 个文件");

            // 按目录分组
            Map<String, List<String>> directoryToFiles = files.stream()
                    .collect(Collectors.groupingBy(filePath -> new File(filePath).getParent()));

            for (Map.Entry<String, List<String>> dirEntry : directoryToFiles.entrySet()) {
                String directory = dirEntry.getKey();
                List<String> dirFiles = dirEntry.getValue();

                LOG.warning("  - 目录 " + directory + ": " + dirFiles.size() + " 个文件");

                // 显示示例
                int count = 0;
                for (String filePath : dirFiles) {
                    if (count++ < maxExamples) {
                        LOG.warning("    - " + new File(filePath).getName());
                    } else {
                        LOG.warning("    - ... 以及其他 " + (dirFiles.size() - maxExamples) + " 个文件");
                        break;
                    }
                }
            }

            // 如果需要详细信息，提供更多示例
            if (detailedWarnings && !files.isEmpty()) {
                LOG.warning("  - 详细示例:");

                // 随机选择几个文件进行详细分析
                List<String> sampleFiles = new ArrayList<>(files);
                Collections.shuffle(sampleFiles);
                int sampleSize = Math.min(3, sampleFiles.size());

                for (int i = 0; i < sampleSize; i++) {
                    String filePath = sampleFiles.get(i);
                    try {
                        // 读取文件的关键标签
                        Map<String, String> keyTags = metadataReader.readKeyTags(filePath);

                        String details = "    - 文件: " + new File(filePath).getName() + "\n" +
                                "      PatientID: " + keyTags.getOrDefault("PatientID", "缺失") + "\n" +
                                "      PatientName: " + keyTags.getOrDefault("PatientName", "缺失") + "\n" +
                                "      StudyInstanceUID: " + keyTags.getOrDefault("StudyInstanceUID", "缺失") + "\n" +
                                "      SeriesInstanceUID: " + keyTags.getOrDefault("SeriesInstanceUID", "缺失");

                        LOG.warning(details);
                    } catch (Exception e) {
                        LOG.warning(
                                "    - 文件: " + new File(filePath).getName() + " (无法读取详细信息: " + e.getMessage() + ")");
                    }
                }
            }
        }
    }

    /**
     * 获取错误类型数量
     */
    public int getErrorTypeCount() {
        return errorTypeToFiles.size();
    }

    /**
     * 获取总文件数量
     */
    public int getTotalFileCount() {
        return errorTypeToFiles.values().stream()
                .mapToInt(List::size)
                .sum();
    }

    /**
     * 清除所有记录
     */
    public void clear() {
        errorTypeToFiles.clear();
    }
}
