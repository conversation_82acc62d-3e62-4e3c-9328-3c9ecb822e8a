package com.ge.med.ct.dicom2.core;

import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.*;

/**
 * DICOM序列管理器
 * 负责管理DICOM序列的组织和关联
 */
public class DicomSeriesManager {
    private final Map<String, List<DicomFileModel>> seriesMap;

    public DicomSeriesManager() {
        this.seriesMap = new HashMap<>();
    }

    /**
     * 添加DICOM文件到序列
     *
     * @param model DICOM文件模型
     * @throws DicomException 如果添加失败
     */
    public void addToSeries(DicomFileModel model) throws DicomException {
        try {
            String seriesInstanceUID = getSeriesInstanceUID(model);
            if (seriesInstanceUID == null || seriesInstanceUID.isEmpty()) {
                throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.SERIES_EMPTY);
            }

            seriesMap.computeIfAbsent(seriesInstanceUID, k -> new ArrayList<>()).add(model);
            sortSeriesByInstanceNumber(seriesInstanceUID);
        } catch (Exception e) {
            throw new DicomException(ErrorCode.PROCESSING, DicomMessages.PROCESSING_ERROR, e);
        }
    }

    /**
     * 获取指定序列的所有DICOM文件
     *
     * @param seriesInstanceUID 序列实例UID
     * @return 序列中的DICOM文件列表
     */
    public List<DicomFileModel> getSeriesFiles(String seriesInstanceUID) {
        return seriesMap.getOrDefault(seriesInstanceUID, new ArrayList<>());
    }

    /**
     * 获取所有序列实例UID
     *
     * @return 序列实例UID集合
     */
    public Set<String> getAllSeriesUIDs() {
        return new HashSet<>(seriesMap.keySet());
    }

    /**
     * 获取序列实例UID
     *
     * @param model DICOM文件模型
     * @return 序列实例UID
     */
    private String getSeriesInstanceUID(DicomFileModel model) {
        return model.getSeriesInstanceUID();
    }

    /**
     * 按实例号排序序列
     *
     * @param seriesInstanceUID 序列实例UID
     */
    private void sortSeriesByInstanceNumber(String seriesInstanceUID) {
        List<DicomFileModel> seriesFiles = seriesMap.get(seriesInstanceUID);
        if (seriesFiles != null) {
            seriesFiles.sort((a, b) -> {
                Integer instanceNumberA = getInstanceNumber(a);
                Integer instanceNumberB = getInstanceNumber(b);
                return instanceNumberA.compareTo(instanceNumberB);
            });
        }
    }

    /**
     * 获取实例号
     *
     * @param model DICOM文件模型
     * @return 实例号
     */
    private Integer getInstanceNumber(DicomFileModel model) {
        Integer instanceNumber = model.getInstanceNumber();
        return instanceNumber != null ? instanceNumber : 0;
    }

    /**
     * 获取序列描述
     *
     * @param seriesInstanceUID 序列实例UID
     * @return 序列描述信息
     */
    public Map<String, Object> getSeriesDescription(String seriesInstanceUID) {
        List<DicomFileModel> seriesFiles = seriesMap.get(seriesInstanceUID);
        if (seriesFiles == null || seriesFiles.isEmpty()) {
            return Collections.emptyMap();
        }

        DicomFileModel firstFile = seriesFiles.get(0);
        Map<String, Object> description = new HashMap<>();
        description.put("seriesInstanceUID", seriesInstanceUID);
        description.put("seriesDescription", firstFile.getSeriesDescription());
        description.put("seriesNumber", firstFile.getSeriesNumber());
        description.put("modality", firstFile.getModality());
        description.put("numberOfImages", seriesFiles.size());

        return description;
    }
}