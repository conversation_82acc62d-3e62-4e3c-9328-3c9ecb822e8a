package com.ge.med.ct.dicom2.core;

import com.ge.med.ct.analysis.service.AnalysisStatusCallback;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.exception.core.DicomException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * DICOM数据提供者接口
 * 负责DICOM数据的访问和管理
 */
public interface DicomDataProvider {
    // === 检查(Exam)相关操作 ===
    /**
     * 获取所有检查列表
     */
    List<DicomExam> getAllExams();

    /**
     * 异步获取所有检查列表
     */
    CompletableFuture<List<DicomExam>> getAllExamsAsync();

    /**
     * 获取指定检查
     *
     * @param examId 检查ID
     * @return 检查对象，如果不存在返回null
     */
    DicomExam getExam(String examId);

    /**
     * 异步获取指定检查
     */
    CompletableFuture<DicomExam> getExamAsync(String examId);

    /**
     * 搜索检查
     *
     * @param patientName 患者姓名（可选）
     * @param patientId   患者ID（可选）
     * @return 匹配的检查列表
     */
    List<DicomExam> searchExams(String patientName, String patientId);

    /**
     * 异步搜索检查
     */
    CompletableFuture<List<DicomExam>> searchExamsAsync(String patientName, String patientId);

    // === 序列(Series)相关操作 ===
    /**
     * 获取指定序列
     */
    DicomSeries getSeries(String seriesId);

    /**
     * 获取检查下的所有序列
     */
    List<DicomSeries> getSeriesForExam(String examId);

    // === 图像(Image)相关操作 ===
    /**
     * 获取指定图像
     */
    DicomImage getImage(String imageId);

    /**
     * 获取序列下的所有图像
     */
    List<DicomImage> getImagesForSeries(String seriesId);

    // === DICOM文件操作 ===
    /**
     * 添加DICOM文件模型
     *
     * @throws DicomException 如果添加过程中发生错误
     */
    void addFileModel(DicomFileModel model) throws DicomException;

    /**
     * 异步添加DICOM文件模型
     */
    CompletableFuture<Void> addFileModelAsync(DicomFileModel model);

    /**
     * 获取DICOM标签值
     */
    String getTagValue(String fileId, String tagId);

    /**
     * 获取所有DICOM文件模型
     *
     * @return 所有DICOM文件模型的列表
     */
    List<DicomFileModel> getAllFileModels();

    /**
     * 异步获取所有DICOM文件模型
     *
     * @return 包含所有DICOM文件模型的列表的CompletableFuture
     */
    CompletableFuture<List<DicomFileModel>> getAllFileModelsAsync();

    // === 系统操作 ===
    /**
     * 设置状态回调
     */
    void setStatusCallback(AnalysisStatusCallback callback);

    /**
     * 清除所有数据
     */
    void clearData() throws DicomException;

    /**
     * 关闭资源
     */
    void shutdown();
}