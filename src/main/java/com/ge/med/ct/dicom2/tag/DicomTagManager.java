package com.ge.med.ct.dicom2.tag;

import org.dcm4che3.data.ElementDictionary;
import org.dcm4che3.data.VR;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * DICOM标签管理器
 * 负责标签的注册、分组管理和配置加载
 * 整合了原有TagGroupManager和TagRegistrator的功能
 */
public class DicomTagManager {
    
    private static final Logger LOG = Logger.getLogger(DicomTagManager.class.getName());
    
    // 使用标准元素字典
    private static final ElementDictionary DICT = ElementDictionary.getStandardElementDictionary();
    
    // 标签注册中心
    private final DicomTagRegistry registry;
    
    // 标签组映射（组名 -> 标签ID集合）
    private final Map<String, Set<String>> tagGroups = new ConcurrentHashMap<>();
    
    /**
     * 构造函数
     *
     * @param registry 标签注册中心
     */
    public DicomTagManager(DicomTagRegistry registry) {
        if (registry == null) {
            throw new IllegalArgumentException("标签注册中心不能为空");
        }
        this.registry = registry;
        initializeGroups();
    }
    
    /**
     * 初始化标准标签组
     */
    private void initializeGroups() {
        tagGroups.put("STANDARD", new HashSet<>());
        tagGroups.put("GE_CT", new HashSet<>());
        tagGroups.put("PATIENT", new HashSet<>());
        tagGroups.put("STUDY", new HashSet<>());
        tagGroups.put("SERIES", new HashSet<>());
        tagGroups.put("IMAGE", new HashSet<>());
        tagGroups.put("EQUIPMENT", new HashSet<>());
    }

    /**
     * 注册所有标准类别的标签
     */
    public void registerAllStandardTags() {
        registerPatientTags();
        registerStudyTags();
        registerSeriesTags();
        registerImageTags();
        registerEquipmentTags();
    }
    
    /**
     * 注册所有GE CT特有标签
     */
    public void registerAllGECTTags() {
        registerGECTTags();
    }
    
    /**
     * 注册患者相关标签
     */
    public void registerPatientTags() {
        registerStandardTag(DicomTagConstants.Patient.PATIENT_ID, "PatientID");
        registerStandardTag(DicomTagConstants.Patient.PATIENT_NAME, "PatientName");
        registerStandardTag(DicomTagConstants.Patient.PATIENT_BIRTH_DATE, "PatientBirthDate");
        registerStandardTag(DicomTagConstants.Patient.PATIENT_SEX, "PatientSex");
        registerStandardTag(DicomTagConstants.Patient.PATIENT_AGE, "PatientAge");
        registerStandardTag(DicomTagConstants.Patient.PATIENT_WEIGHT, "PatientWeight");
        registerStandardTag(DicomTagConstants.Patient.PATIENT_SIZE, "PatientSize");
        registerStandardTag(DicomTagConstants.Patient.PATIENT_STATE, "PatientState");
    }
    
    /**
     * 注册研究相关标签
     */
    public void registerStudyTags() {
        registerStandardTag(DicomTagConstants.Study.STUDY_INSTANCE_UID, "StudyInstanceUID");
        registerStandardTag(DicomTagConstants.Study.STUDY_ID, "StudyID");
        registerStandardTag(DicomTagConstants.Study.STUDY_DATE, "StudyDate");
        registerStandardTag(DicomTagConstants.Study.STUDY_TIME, "StudyTime");
        registerStandardTag(DicomTagConstants.Study.STUDY_DESCRIPTION, "StudyDescription");
        registerStandardTag(DicomTagConstants.Study.ACCESSION_NUMBER, "AccessionNumber");
        registerStandardTag(DicomTagConstants.Study.REFERRING_PHYSICIAN_NAME, "ReferringPhysicianName");
        registerStandardTag(DicomTagConstants.Study.PROTOCOL_NAME, "ProtocolName");
    }
    
    /**
     * 注册序列相关标签
     */
    public void registerSeriesTags() {
        registerStandardTag(DicomTagConstants.Series.SERIES_INSTANCE_UID, "SeriesInstanceUID");
        registerStandardTag(DicomTagConstants.Series.SERIES_NUMBER, "SeriesNumber");
        registerStandardTag(DicomTagConstants.Series.SERIES_DESCRIPTION, "SeriesDescription");
        registerStandardTag(DicomTagConstants.Series.SERIES_DATE, "SeriesDate");
        registerStandardTag(DicomTagConstants.Series.SERIES_TIME, "SeriesTime");
        registerStandardTag(DicomTagConstants.Series.MODALITY, "Modality");
        registerStandardTag(DicomTagConstants.Series.BODY_PART_EXAMINED, "BodyPartExamined");
    }
    
    /**
     * 注册图像相关标签
     */
    public void registerImageTags() {
        registerStandardTag(DicomTagConstants.Image.SOP_INSTANCE_UID, "SOPInstanceUID");
        registerStandardTag(DicomTagConstants.Image.INSTANCE_NUMBER, "InstanceNumber");
        registerStandardTag(DicomTagConstants.Image.IMAGE_TYPE, "ImageType");
        registerStandardTag(DicomTagConstants.Image.ROWS, "Rows");
        registerStandardTag(DicomTagConstants.Image.COLUMNS, "Columns");
        registerStandardTag(DicomTagConstants.Image.BITS_ALLOCATED, "BitsAllocated");
        registerStandardTag(DicomTagConstants.Image.BITS_STORED, "BitsStored");
        registerStandardTag(DicomTagConstants.Image.HIGH_BIT, "HighBit");
        registerStandardTag(DicomTagConstants.Image.SAMPLES_PER_PIXEL, "SamplesPerPixel");
        registerStandardTag(DicomTagConstants.Image.PHOTOMETRIC_INTERPRETATION, "PhotometricInterpretation");
        registerStandardTag(DicomTagConstants.Image.WINDOW_CENTER, "WindowCenter");
        registerStandardTag(DicomTagConstants.Image.WINDOW_WIDTH, "WindowWidth");
        registerStandardTag(DicomTagConstants.Image.RESCALE_INTERCEPT, "RescaleIntercept");
        registerStandardTag(DicomTagConstants.Image.RESCALE_SLOPE, "RescaleSlope");
        registerStandardTag(DicomTagConstants.Image.PIXEL_SPACING, "PixelSpacing");
        registerStandardTag(DicomTagConstants.Image.IMAGE_POSITION_PATIENT, "ImagePositionPatient");
        registerStandardTag(DicomTagConstants.Image.IMAGE_ORIENTATION_PATIENT, "ImageOrientationPatient");
        registerStandardTag(DicomTagConstants.Image.PIXEL_DATA, "PixelData");
    }
    
    /**
     * 注册设备相关标签
     */
    public void registerEquipmentTags() {
        registerStandardTag(DicomTagConstants.Equipment.MANUFACTURER, "Manufacturer");
        registerStandardTag(DicomTagConstants.Equipment.MANUFACTURER_MODEL_NAME, "ManufacturerModelName");
        registerStandardTag(DicomTagConstants.Equipment.DEVICE_SERIAL_NUMBER, "DeviceSerialNumber");
        registerStandardTag(DicomTagConstants.Equipment.SOFTWARE_VERSIONS, "SoftwareVersions");
        registerStandardTag(DicomTagConstants.Equipment.STATION_NAME, "StationName");
        registerStandardTag(DicomTagConstants.Equipment.INSTITUTION_NAME, "InstitutionName");
        registerStandardTag(DicomTagConstants.Equipment.INSTITUTION_ADDRESS, "InstitutionAddress");
        registerStandardTag(DicomTagConstants.Equipment.INSTITUTIONAL_DEPARTMENT_NAME, "InstitutionalDepartmentName");
    }
    
    /**
     * 注册GE CT特有标签
     */
    public void registerGECTTags() {
        registerGECTTag(DicomTagConstants.GECT.CT_TUBE_CURRENT_IN_MA, "CTTubeCurrentInmA");
        registerGECTTag(DicomTagConstants.GECT.CT_TUBE_VOLTAGE_IN_KV, "CTTubeVoltageInkV");
        registerGECTTag(DicomTagConstants.GECT.RECONSTRUCTION_ALGORITHM, "ReconstructionAlgorithm");
        registerGECTTag(DicomTagConstants.GECT.ACQUISITION_WORKLIST_NUMBER, "AcquisitionWorklistNumber");
        registerGECTTag(DicomTagConstants.GECT.ACTUAL_SLICE_THICKNESS, "ActualSliceThickness");
    }
    
    /**
     * 从配置文件批量注册标签
     *
     * @param configPath 配置文件路径
     * @return 注册的标签数量
     */
    public int registerTagsFromConfig(String configPath) {
        try {
            // 加载标签名称映射
            Map<String, String> tagNameMap = TagConfigLoader.loadTagsFromXml(configPath);
            if (tagNameMap == null || tagNameMap.isEmpty()) {
                return 0;
            }
            
            int count = 0;
            for (Map.Entry<String, String> entry : tagNameMap.entrySet()) {
                String tagId = entry.getKey();
                String name = entry.getValue();
                
                int tagInt;
                try {
                    tagInt = DicomTagConstants.stringToTag(tagId);
                } catch (Exception e) {
                    LOG.warning("无法解析标签ID: " + tagId);
                    continue;
                }
                
                VR vr = DICT.vrOf(tagInt);
                DicomTagConstants.TagCategory category = DicomTagConstants.getCategoryFromTagId(tagId);
                
                registerTag(
                    tagId,
                    name,
                    vr,
                    category,
                    determineGroupForTag(tagId, category)
                );
                count++;
            }
            
            LOG.info("从配置文件加载了 " + count + " 个标签定义");
            return count;
        } catch (Exception e) {
            LOG.warning("加载标签配置失败: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * 从配置文件加载标签组定义
     *
     * @param configPath 配置文件路径
     * @return 是否加载成功
     */
    public boolean loadGroupsFromConfig(String configPath) {
        try {
            // 使用自定义实现加载组
            Map<String, Set<String>> groups = loadTagGroupsFromFile(configPath);
            return registerGroups(groups);
        } catch (Exception e) {
            LOG.warning("加载标签组配置失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 从文本文件加载标签组
     * 
     * @param configPath 配置文件路径
     * @return 标签组映射
     */
    private Map<String, Set<String>> loadTagGroupsFromFile(String configPath) {
        Map<String, Set<String>> result = new HashMap<>();
        
        File configFile = new File(configPath);
        if (!configFile.exists() || !configFile.isFile()) {
            LOG.warning("配置文件不存在: " + configPath);
            return result;
        }
        
        try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
            String line;
            String currentGroup = null;
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue; // 跳过空行和注释
                }
                
                if (line.startsWith("[") && line.endsWith("]")) {
                    // 这是一个组名
                    currentGroup = line.substring(1, line.length() - 1).trim();
                    result.put(currentGroup, new HashSet<>());
                } else if (currentGroup != null) {
                    // 这是组内的标签
                    result.get(currentGroup).add(line);
                }
            }
        } catch (Exception e) {
            LOG.warning("解析标签组文件失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 注册标签组定义
     *
     * @param groups 标签组定义
     * @return 是否成功注册
     */
    private boolean registerGroups(Map<String, Set<String>> groups) {
        if (groups == null || groups.isEmpty()) {
            return false;
        }
        
        int count = 0;
        
        for (Map.Entry<String, Set<String>> entry : groups.entrySet()) {
            String groupName = entry.getKey();
            Set<String> tagIds = entry.getValue();
            
            if (groupName != null && !groupName.trim().isEmpty()) {
                createGroup(groupName);
                
                for (String tagId : tagIds) {
                    if (addTagToGroup(groupName, tagId)) {
                        count++;
                    }
                }
            }
        }
        
        LOG.info("为 " + groups.size() + " 个组添加了 " + count + " 个标签");
        return count > 0;
    }
    
    /**
     * 确定标签所属组
     */
    private String determineGroupForTag(String tagId, DicomTagConstants.TagCategory category) {
        if (tagId == null) {
            return null;
        }
        
        if (DicomTagConstants.isPrivateTag(tagId)) {
            return "GE_CT";
        }
        
        return "STANDARD";
    }
    
    /**
     * 注册标准DICOM标签
     */
    private void registerStandardTag(String tagId, String name) {
        int tagInt = DicomTagConstants.stringToTag(tagId);
        VR vr = DICT.vrOf(tagInt);
        DicomTagConstants.TagCategory category = DicomTagConstants.getCategoryFromTagId(tagId);
        
        registerTag(tagId, name, vr, category, "STANDARD");
        
        // 根据类别添加到对应组
        switch (category) {
            case PATIENT:
                addTagToGroup("PATIENT", tagId);
                break;
            case STUDY:
                addTagToGroup("STUDY", tagId);
                break;
            case SERIES:
                addTagToGroup("SERIES", tagId);
                break;
            case IMAGE:
                addTagToGroup("IMAGE", tagId);
                break;
            case EQUIPMENT:
                addTagToGroup("EQUIPMENT", tagId);
                break;
            default:
                // 默认不添加到额外的组
        }
    }
    
    /**
     * 注册GE CT特有标签
     */
    private void registerGECTTag(String tagId, String name) {
        registerTag(tagId, name, VR.UN, DicomTagConstants.TagCategory.OTHER, "GE_CT");
    }
    
    /**
     * 注册标签
     * 
     * @param tagId 标签ID
     * @param name 标签名称
     * @param vr 值表示类型
     * @param category 标签分类
     * @param group 标签组
     */
    public void registerTag(String tagId, String name, VR vr, DicomTagConstants.TagCategory category, String group) {
        if (tagId == null || tagId.trim().isEmpty()) {
            return;
        }
        
        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        
        // 在注册中心注册标签
        registry.registerTag(normalizedId, name, vr, category);
        
        // 添加到标签组
        if (group != null && !group.trim().isEmpty()) {
            addTagToGroup(group, normalizedId);
        }
    }
    
    /**
     * 创建新标签组
     * 
     * @param groupName 组名
     * @return 操作是否成功
     */
    public boolean createGroup(String groupName) {
        if (groupName == null || groupName.trim().isEmpty() || tagGroups.containsKey(groupName)) {
            return false;
        }
        
        tagGroups.put(groupName, new HashSet<>());
        return true;
    }
    
    /**
     * 添加标签到组
     * 
     * @param groupName 组名
     * @param tagId 标签ID
     * @return 操作是否成功
     */
    public boolean addTagToGroup(String groupName, String tagId) {
        if (groupName == null || tagId == null) {
            return false;
        }
        
        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        tagGroups.computeIfAbsent(groupName, k -> new HashSet<>()).add(normalizedId);
        return true;
    }
    
    /**
     * 从组中移除标签
     * 
     * @param groupName 组名
     * @param tagId 标签ID
     * @return 操作是否成功
     */
    public boolean removeTagFromGroup(String groupName, String tagId) {
        if (groupName == null || tagId == null) {
            return false;
        }
        
        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        Set<String> group = tagGroups.get(groupName);
        if (group == null) {
            return false;
        }
        
        return group.remove(normalizedId);
    }
    
    /**
     * 检查标签是否在组中
     * 
     * @param groupName 组名
     * @param tagId 标签ID
     * @return 是否在组中
     */
    public boolean isTagInGroup(String groupName, String tagId) {
        if (groupName == null || tagId == null) {
            return false;
        }
        
        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        Set<String> group = tagGroups.get(groupName);
        return group != null && group.contains(normalizedId);
    }
    
    /**
     * 获取组内所有标签
     * 
     * @param groupName 组名
     * @return 标签ID集合
     */
    public Set<String> getTagsInGroup(String groupName) {
        if (groupName == null) {
            return Collections.emptySet();
        }
        
        Set<String> group = tagGroups.get(groupName);
        return group != null ? new HashSet<>(group) : new HashSet<>();
    }
    
    /**
     * 获取所有组名
     * 
     * @return 组名集合
     */
    public Set<String> getAllGroupNames() {
        return new HashSet<>(tagGroups.keySet());
    }
    
    /**
     * 获取标签所属的所有组
     * 
     * @param tagId 标签ID
     * @return 组名集合
     */
    public Set<String> getGroupsContainingTag(String tagId) {
        if (tagId == null) {
            return Collections.emptySet();
        }
        
        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        Set<String> result = new HashSet<>();
        tagGroups.forEach((groupName, tags) -> {
            if (tags.contains(normalizedId)) {
                result.add(groupName);
            }
        });
        
        return result;
    }
    
    /**
     * 加载默认配置
     * 从预设位置加载标签和组配置
     *
     * @return 是否成功加载
     */
    public boolean loadDefaultConfig() {
        boolean tagsLoaded = registerTagsFromConfig("config/dicom_tags.json") > 0;
        boolean groupsLoaded = loadGroupsFromConfig("config/dicom_groups.json");
        
        return tagsLoaded || groupsLoaded;
    }
} 