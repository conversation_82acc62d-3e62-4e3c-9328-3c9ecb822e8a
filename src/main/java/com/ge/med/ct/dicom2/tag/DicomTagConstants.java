package com.ge.med.ct.dicom2.tag;

import org.dcm4che3.data.ElementDictionary;
import org.dcm4che3.data.Tag;
import org.dcm4che3.data.VR;

import java.util.*;

/**
 * DICOM标签常量和工具类
 * 集中管理所有DICOM标签ID常量和提供标签操作工具方法
 * 整合原有DicomTags和DicomTagConstants的功能
 */
public final class DicomTagConstants {

    // 使用标准元素字典
    private static final ElementDictionary DICT = ElementDictionary.getStandardElementDictionary();

    // 存储所有标签和描述（包括标准标签和GE CT标签）
    private static final Map<String, String> TAG_MAPPINGS = new HashMap<>();

    // 存储GE CT特有标签
    private static final Map<String, String> GE_CT_TAG_MAPPINGS = new HashMap<>();

    // 存储标签VR信息
    private static final Map<String, VR> TAG_VR_MAPPINGS = new HashMap<>();

    // 存储标签名称到标签ID的映射
    private static final Map<String, String> TAG_NAME_TO_ID = new HashMap<>();

    // 禁止实例化
    private DicomTagConstants() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * DICOM标签分类
     */
    public enum TagCategory {
        PATIENT(0x0010), // 患者标签
        STUDY(0x0020), // 研究标签
        SERIES(0x0020), // 序列标签
        IMAGE(0x0028), // 图像标签
        EQUIPMENT(0x0018), // 设备标签
        GENERAL(0x0008), // 通用标签
        OTHER(0x0000); // 其他标签

        private final int groupNumber;

        TagCategory(int groupNumber) {
            this.groupNumber = groupNumber;
        }

        public int getGroupNumber() {
            return groupNumber;
        }

    }

    /**
     * 患者相关标签
     */
    public static final class Patient {
        // 患者标识
        public static final String PATIENT_ID = "(0010,0020)";
        public static final int PATIENT_ID_TAG = Tag.PatientID;

        // 患者姓名
        public static final String PATIENT_NAME = "(0010,0010)";
        public static final int PATIENT_NAME_TAG = Tag.PatientName;

        // 患者出生日期
        public static final String PATIENT_BIRTH_DATE = "(0010,0030)";
        public static final int PATIENT_BIRTH_DATE_TAG = Tag.PatientBirthDate;

        // 患者性别
        public static final String PATIENT_SEX = "(0010,0040)";
        public static final int PATIENT_SEX_TAG = Tag.PatientSex;

        // 患者年龄
        public static final String PATIENT_AGE = "(0010,1010)";
        public static final int PATIENT_AGE_TAG = Tag.PatientAge;

        // 患者体重
        public static final String PATIENT_WEIGHT = "(0010,1030)";
        public static final int PATIENT_WEIGHT_TAG = Tag.PatientWeight;

        // 患者身高
        public static final String PATIENT_SIZE = "(0010,1020)";
        public static final int PATIENT_SIZE_TAG = Tag.PatientSize;

        // 患者状态
        public static final String PATIENT_STATE = "(0038,0500)";
        public static final int PATIENT_STATE_TAG = Tag.PatientState;

        // 从DicomTags添加的额外标签
        public static final String OTHER_PATIENT_IDS = "(0010,1000)";
        public static final int OTHER_PATIENT_IDS_TAG = Tag.OtherPatientIDs;

        public static final String ADDITIONAL_PATIENT_HISTORY = "(0010,21B0)";
        public static final int ADDITIONAL_PATIENT_HISTORY_TAG = Tag.AdditionalPatientHistory;
    }

    /**
     * 研究相关标签
     */
    public static final class Study {
        // 研究实例UID
        public static final String STUDY_INSTANCE_UID = "(0020,000D)";
        public static final int STUDY_INSTANCE_UID_TAG = Tag.StudyInstanceUID;

        // 研究ID
        public static final String STUDY_ID = "(0020,0010)";
        public static final int STUDY_ID_TAG = Tag.StudyID;

        // 研究日期
        public static final String STUDY_DATE = "(0008,0020)";
        public static final int STUDY_DATE_TAG = Tag.StudyDate;

        // 研究时间
        public static final String STUDY_TIME = "(0008,0030)";
        public static final int STUDY_TIME_TAG = Tag.StudyTime;

        // 研究描述
        public static final String STUDY_DESCRIPTION = "(0008,1030)";
        public static final int STUDY_DESCRIPTION_TAG = Tag.StudyDescription;

        // 入院号
        public static final String ACCESSION_NUMBER = "(0008,0050)";
        public static final int ACCESSION_NUMBER_TAG = Tag.AccessionNumber;

        // 转诊医师姓名
        public static final String REFERRING_PHYSICIAN_NAME = "(0008,0090)";
        public static final int REFERRING_PHYSICIAN_NAME_TAG = Tag.ReferringPhysicianName;

        // 协议名称
        public static final String PROTOCOL_NAME = "(0018,1030)";
        public static final int PROTOCOL_NAME_TAG = Tag.ProtocolName;
    }

    /**
     * 序列相关标签
     */
    public static final class Series {
        // 序列实例UID
        public static final String SERIES_INSTANCE_UID = "(0020,000E)";
        public static final int SERIES_INSTANCE_UID_TAG = Tag.SeriesInstanceUID;

        // 序列号
        public static final String SERIES_NUMBER = "(0020,0011)";
        public static final int SERIES_NUMBER_TAG = Tag.SeriesNumber;

        // 序列描述
        public static final String SERIES_DESCRIPTION = "(0008,103E)";
        public static final int SERIES_DESCRIPTION_TAG = Tag.SeriesDescription;

        // 序列日期
        public static final String SERIES_DATE = "(0008,0021)";
        public static final int SERIES_DATE_TAG = Tag.SeriesDate;

        // 序列时间
        public static final String SERIES_TIME = "(0008,0031)";
        public static final int SERIES_TIME_TAG = Tag.SeriesTime;

        // 模态
        public static final String MODALITY = "(0008,0060)";
        public static final int MODALITY_TAG = Tag.Modality;

        // 检查身体部位
        public static final String BODY_PART_EXAMINED = "(0018,0015)";
        public static final int BODY_PART_EXAMINED_TAG = Tag.BodyPartExamined;

        // 从DicomTags添加的额外标签
        public static final String SERIES_STATUS = "(0040,0020)";
        public static final int SERIES_STATUS_TAG = 0x00400020;

        public static final String SERIES_PRIORITY = "(0040,0022)";
        public static final int SERIES_PRIORITY_TAG = 0x00400022;

        public static final String SERIES_COMPLETION_DATE = "(0040,0251)";
        public static final int SERIES_COMPLETION_DATE_TAG = 0x00400251;

        public static final String SERIES_COMPLETION_TIME = "(0040,0252)";
        public static final int SERIES_COMPLETION_TIME_TAG = 0x00400252;

        public static final String SERIES_VERIFIED_DATE = "(0040,0253)";
        public static final int SERIES_VERIFIED_DATE_TAG = 0x00400253;

        public static final String SERIES_VERIFIED_TIME = "(0040,0254)";
        public static final int SERIES_VERIFIED_TIME_TAG = 0x00400254;
    }

    /**
     * 图像相关标签
     */
    public static final class Image {
        // SOP实例UID
        public static final String SOP_INSTANCE_UID = "(0008,0018)";
        public static final int SOP_INSTANCE_UID_TAG = Tag.SOPInstanceUID;

        // 实例号
        public static final String INSTANCE_NUMBER = "(0020,0013)";
        public static final int INSTANCE_NUMBER_TAG = Tag.InstanceNumber;

        // 图像类型
        public static final String IMAGE_TYPE = "(0008,0008)";
        public static final int IMAGE_TYPE_TAG = Tag.ImageType;

        // 行数
        public static final String ROWS = "(0028,0010)";
        public static final int ROWS_TAG = Tag.Rows;

        // 列数
        public static final String COLUMNS = "(0028,0011)";
        public static final int COLUMNS_TAG = Tag.Columns;

        // 分配位数
        public static final String BITS_ALLOCATED = "(0028,0100)";
        public static final int BITS_ALLOCATED_TAG = Tag.BitsAllocated;

        // 存储位数
        public static final String BITS_STORED = "(0028,0101)";
        public static final int BITS_STORED_TAG = Tag.BitsStored;

        // 高位
        public static final String HIGH_BIT = "(0028,0102)";
        public static final int HIGH_BIT_TAG = Tag.HighBit;

        // 每像素样本数
        public static final String SAMPLES_PER_PIXEL = "(0028,0002)";
        public static final int SAMPLES_PER_PIXEL_TAG = Tag.SamplesPerPixel;

        // 光度解释
        public static final String PHOTOMETRIC_INTERPRETATION = "(0028,0004)";
        public static final int PHOTOMETRIC_INTERPRETATION_TAG = Tag.PhotometricInterpretation;

        // 窗位
        public static final String WINDOW_CENTER = "(0028,1050)";
        public static final int WINDOW_CENTER_TAG = Tag.WindowCenter;

        // 窗宽
        public static final String WINDOW_WIDTH = "(0028,1051)";
        public static final int WINDOW_WIDTH_TAG = Tag.WindowWidth;

        // 重缩放截距
        public static final String RESCALE_INTERCEPT = "(0028,1052)";
        public static final int RESCALE_INTERCEPT_TAG = Tag.RescaleIntercept;

        // 重缩放斜率
        public static final String RESCALE_SLOPE = "(0028,1053)";
        public static final int RESCALE_SLOPE_TAG = Tag.RescaleSlope;

        // 像素间距
        public static final String PIXEL_SPACING = "(0028,0030)";
        public static final int PIXEL_SPACING_TAG = Tag.PixelSpacing;

        // 图像位置(病人)
        public static final String IMAGE_POSITION_PATIENT = "(0020,0032)";
        public static final int IMAGE_POSITION_PATIENT_TAG = Tag.ImagePositionPatient;

        // 图像方向(病人)
        public static final String IMAGE_ORIENTATION_PATIENT = "(0020,0037)";
        public static final int IMAGE_ORIENTATION_PATIENT_TAG = Tag.ImageOrientationPatient;

        // 像素数据
        public static final String PIXEL_DATA = "(7FE0,0010)";
        public static final int PIXEL_DATA_TAG = Tag.PixelData;

        // 图像日期
        public static final String IMAGE_DATE = "(0008,0023)";
        public static final int IMAGE_DATE_TAG = Tag.ContentDate;

        // 图像时间
        public static final String IMAGE_TIME = "(0008,0033)";
        public static final int IMAGE_TIME_TAG = Tag.ContentTime;

        // 切片位置
        public static final String SLICE_LOCATION = "(0020,1041)";
        public static final int SLICE_LOCATION_TAG = Tag.SliceLocation;

        // 图像注释
        public static final String IMAGE_COMMENTS = "(0020,4000)";
        public static final int IMAGE_COMMENTS_TAG = Tag.ImageComments;

        // 从DicomTags添加的额外标签
        public static final String PIXEL_REPRESENTATION = "(0028,0103)";
        public static final int PIXEL_REPRESENTATION_TAG = Tag.PixelRepresentation;

        public static final String RESCALE_TYPE = "(0028,1054)";
        public static final int RESCALE_TYPE_TAG = Tag.RescaleType;

        public static final String SLICE_THICKNESS = "(0018,0050)";
        public static final int SLICE_THICKNESS_TAG = Tag.SliceThickness;

        public static final String SPACING_BETWEEN_SLICES = "(0018,0088)";
        public static final int SPACING_BETWEEN_SLICES_TAG = Tag.SpacingBetweenSlices;

        public static final String ACQUISITION_DATE = "(0008,0022)";
        public static final int ACQUISITION_DATE_TAG = Tag.AcquisitionDate;

        public static final String ACQUISITION_TIME = "(0008,0032)";
        public static final int ACQUISITION_TIME_TAG = Tag.AcquisitionTime;

        public static final String FRAME_OF_REFERENCE_UID = "(0020,0052)";
        public static final int FRAME_OF_REFERENCE_UID_TAG = Tag.FrameOfReferenceUID;

        public static final String POSITION_REFERENCE_INDICATOR = "(0020,1040)";
        public static final int POSITION_REFERENCE_INDICATOR_TAG = Tag.PositionReferenceIndicator;

        public static final String PHASE = "00204000";  // 相位标签ID
    }

    /**
     * 设备相关标签
     */
    public static final class Equipment {
        // 制造商
        public static final String MANUFACTURER = "(0008,0070)";
        public static final int MANUFACTURER_TAG = Tag.Manufacturer;

        // 制造商型号名称
        public static final String MANUFACTURER_MODEL_NAME = "(0008,1090)";
        public static final int MANUFACTURER_MODEL_NAME_TAG = Tag.ManufacturerModelName;

        // 设备序列号
        public static final String DEVICE_SERIAL_NUMBER = "(0018,1000)";
        public static final int DEVICE_SERIAL_NUMBER_TAG = Tag.DeviceSerialNumber;

        // 软件版本
        public static final String SOFTWARE_VERSIONS = "(0018,1020)";
        public static final int SOFTWARE_VERSIONS_TAG = Tag.SoftwareVersions;

        // 工作站名称
        public static final String STATION_NAME = "(0008,1010)";
        public static final int STATION_NAME_TAG = Tag.StationName;

        // 机构名称
        public static final String INSTITUTION_NAME = "(0008,0080)";
        public static final int INSTITUTION_NAME_TAG = Tag.InstitutionName;

        // 机构地址
        public static final String INSTITUTION_ADDRESS = "(0008,0081)";
        public static final int INSTITUTION_ADDRESS_TAG = Tag.InstitutionAddress;

        // 机构部门名称
        public static final String INSTITUTIONAL_DEPARTMENT_NAME = "(0008,1040)";
        public static final int INSTITUTIONAL_DEPARTMENT_NAME_TAG = Tag.InstitutionalDepartmentName;

        // 工作站AE标题
        public static final String STATION_AE_TITLE = "(0008,0054)";
        public static final int STATION_AE_TITLE_TAG = Tag.RetrieveAETitle;
    }

    /**
     * GE CT特有标签
     */
    public static final class GECT {
        // GE CT管电流(mA)
        public static final String CT_TUBE_CURRENT_IN_MA = "(0019,10BB)";
        public static final int CT_TUBE_CURRENT_IN_MA_TAG = 0x001910BB;

        // GE CT管电压(kV)
        public static final String CT_TUBE_VOLTAGE_IN_KV = "(0019,10BC)";
        public static final int CT_TUBE_VOLTAGE_IN_KV_TAG = 0x001910BC;

        // 重建算法
        public static final String RECONSTRUCTION_ALGORITHM = "(0043,1060)";
        public static final int RECONSTRUCTION_ALGORITHM_TAG = 0x00431060;

        // 采集工作列表编号
        public static final String ACQUISITION_WORKLIST_NUMBER = "(0045,1001)";
        public static final int ACQUISITION_WORKLIST_NUMBER_TAG = 0x00451001;

        // 实际切片厚度
        public static final String ACTUAL_SLICE_THICKNESS = "(0019,10DF)";
        public static final int ACTUAL_SLICE_THICKNESS_TAG = 0x001910DF;
    }

    /**
     * CT特有标签
     */
    public static final class CT {
        // 从DicomTags添加的CT特有标签
        public static final String KVP = "(0018,0060)";
        public static final int KVP_TAG = Tag.KVP;

        public static final String FIELD_OF_VIEW = "(0018,0090)";
        public static final int FIELD_OF_VIEW_TAG = Tag.DataCollectionDiameter;

        public static final String RECONSTRUCTION_DIAMETER = "(0018,1100)";
        public static final int RECONSTRUCTION_DIAMETER_TAG = Tag.ReconstructionDiameter;

        public static final String ACQUISITION_NUMBER = "(0020,0012)";
        public static final int ACQUISITION_NUMBER_TAG = Tag.AcquisitionNumber;

        public static final String GANTRY_TILT = "(0018,1120)";
        public static final int GANTRY_TILT_TAG = Tag.GantryDetectorTilt;

        public static final String CONVOLUTION_KERNEL = "(0018,1210)";
        public static final int CONVOLUTION_KERNEL_TAG = Tag.ConvolutionKernel;

        public static final String EXPOSURE = "(0018,1152)";
        public static final int EXPOSURE_TAG = Tag.Exposure;

        public static final String EXPOSURE_TIME = "(0018,1150)";
        public static final int EXPOSURE_TIME_TAG = Tag.ExposureTime;

        public static final String XRAY_TUBE_CURRENT = "(0018,1151)";
        public static final int XRAY_TUBE_CURRENT_TAG = Tag.XRayTubeCurrent;

        public static final String REVOLUTION_TIME = "(0018,9305)";
        public static final int REVOLUTION_TIME_TAG = 0x00189305;

        public static final String SINGLE_COLLIMATION_WIDTH = "(0018,9306)";
        public static final int SINGLE_COLLIMATION_WIDTH_TAG = 0x00189306;

        public static final String TOTAL_COLLIMATION_WIDTH = "(0018,9307)";
        public static final int TOTAL_COLLIMATION_WIDTH_TAG = 0x00189307;
        
        public static final String SPIRAL_PITCH_FACTOR = "(0018,9311)";
        public static final int SPIRAL_PITCH_FACTOR_TAG = 0x00189311;

        public static final String SCAN_OPTIONS = "(0018,0022)";
        public static final int SCAN_OPTIONS_TAG = Tag.ScanOptions;

        public static final String DATA_COLLECTION_DIAMETER = "(0018,0090)";
        public static final int DATA_COLLECTION_DIAMETER_TAG = Tag.DataCollectionDiameter;

        public static final String DISTANCE_SOURCE_TO_DETECTOR = "(0018,1110)";
        public static final int DISTANCE_SOURCE_TO_DETECTOR_TAG = Tag.DistanceSourceToDetector;

        public static final String DISTANCE_SOURCE_TO_PATIENT = "(0018,1111)";
        public static final int DISTANCE_SOURCE_TO_PATIENT_TAG = Tag.DistanceSourceToPatient;

        public static final String TABLE_HEIGHT = "(0018,1130)";
        public static final int TABLE_HEIGHT_TAG = Tag.TableHeight;

        public static final String ROTATION_DIRECTION = "(0018,1140)";
        public static final int ROTATION_DIRECTION_TAG = Tag.RotationDirection;

        public static final String FILTER_TYPE = "(0018,1160)";
        public static final int FILTER_TYPE_TAG = Tag.TypeOfFilters;

        public static final String GENERATOR_POWER = "(0018,1170)";
        public static final int GENERATOR_POWER_TAG = Tag.GeneratorPower;

        public static final String FOCAL_SPOTS = "(0018,1190)";
        public static final int FOCAL_SPOTS_TAG = Tag.FocalSpots;

        public static final String PATIENT_POSITION = "(0018,5100)";
        public static final int PATIENT_POSITION_TAG = Tag.PatientPosition;

        public static final String TABLE_POSITION = "(0018,9327)";
        public static final int TABLE_POSITION_TAG = 0x00189327;

        public static final String TABLE_SPEED = "(0018,9309)";
        public static final int TABLE_SPEED_TAG = 0x00189309;

        public static final String CT_DOSE_LENGTH_PRODUCT = "(0018,9302)";
        public static final int CT_DOSE_LENGTH_PRODUCT_TAG = 0x00189302;

        public static final String CT_DOSE_INDEX_VOL = "(0018,9345)";
        public static final int CT_DOSE_INDEX_VOL_TAG = 0x00189345;
    }

    // 必要的标签ID（用于验证DICOM文件）
    public static final String[] ESSENTIAL_TAG_IDS = {
            Patient.PATIENT_ID,
            Patient.PATIENT_NAME,
            Study.STUDY_INSTANCE_UID,
            Study.STUDY_DATE,
            Series.SERIES_INSTANCE_UID,
            Series.MODALITY,
            Image.SOP_INSTANCE_UID,
            Image.ROWS,
            Image.COLUMNS
    };

    // 必要的标签（整数形式）
    public static final int[] ESSENTIAL_TAGS = {
            Patient.PATIENT_ID_TAG,
            Patient.PATIENT_NAME_TAG,
            Study.STUDY_INSTANCE_UID_TAG,
            Study.STUDY_DATE_TAG,
            Series.SERIES_INSTANCE_UID_TAG,
            Series.MODALITY_TAG,
            Image.SOP_INSTANCE_UID_TAG,
            Image.ROWS_TAG,
            Image.COLUMNS_TAG
    };

    // GE CT序列相关的重要标签
    public static final String[] SERIES_TAG_IDS = {
            Series.SERIES_DESCRIPTION,
            Series.SERIES_NUMBER,
            Series.MODALITY,
            Image.SLICE_THICKNESS,
            Image.SPACING_BETWEEN_SLICES,
            CT.CONVOLUTION_KERNEL,
            CT.RECONSTRUCTION_DIAMETER,
            CT.FIELD_OF_VIEW,
            Study.PROTOCOL_NAME,
            CT.REVOLUTION_TIME,
            CT.SINGLE_COLLIMATION_WIDTH,
            CT.TOTAL_COLLIMATION_WIDTH
    };

    /**
     * 获取标签分类
     */
    public static TagCategory getCategoryFromTagId(String tagId) {
        if (tagId == null) {
            return TagCategory.OTHER;
        }

        String normalizedId = normalizeTagId(tagId);

        try {
            String groupHex = normalizedId.substring(1, 5);
            int group = Integer.parseInt(groupHex, 16);

            if (group == TagCategory.PATIENT.getGroupNumber()) {
                return TagCategory.PATIENT;
            } else if (group == TagCategory.STUDY.getGroupNumber() && normalizedId.substring(6, 10).startsWith("00")) {
                return TagCategory.STUDY;
            } else if (group == TagCategory.GENERAL.getGroupNumber()
                    && normalizedId.substring(6, 10).startsWith("00")) {
                String elemHex = normalizedId.substring(6, 10);
                int elem = Integer.parseInt(elemHex, 16);
                if (elem >= 0x0050 && elem <= 0x0090) {
                    return TagCategory.STUDY;
                } else if (elem >= 0x0070 && elem <= 0x1090) {
                    return TagCategory.EQUIPMENT;
                }
            } else if ((group == TagCategory.GENERAL.getGroupNumber() && normalizedId.startsWith("103E", 6))
                    ||
                    (group == TagCategory.SERIES.getGroupNumber() && normalizedId.startsWith("000E", 6))) {
                return TagCategory.SERIES;
            } else if (group == TagCategory.IMAGE.getGroupNumber() ||
                    (group == TagCategory.SERIES.getGroupNumber() && normalizedId.substring(6, 10).startsWith("00"))) {
                return TagCategory.IMAGE;
            } else if (group == TagCategory.EQUIPMENT.getGroupNumber()
                    && Integer.parseInt(normalizedId.substring(6, 10), 16) >= 0x1000) {
                return TagCategory.EQUIPMENT;
            }
        } catch (Exception e) {
            return TagCategory.OTHER;
        }

        return TagCategory.OTHER;
    }

    /**
     * 检查标签是否为私有标签
     *
     * @param tagId 标签ID
     * @return 是否为私有标签
     */
    public static boolean isPrivateTag(String tagId) {
        if (tagId == null) {
            return false;
        }

        String normalizedId = normalizeTagId(tagId);

        try {
            // 提取组号
            String groupHex = normalizedId.substring(1, 5);
            int group = Integer.parseInt(groupHex, 16);

            // 私有标签的组号是奇数
            return (group & 1) == 1;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查标签是否为GE CT特有标签
     *
     * @param tagId 标签ID
     * @return 是否为GE CT特有标签
     */
    public static boolean isGECTTag(String tagId) {
        if (tagId == null) {
            return false;
        }

        String normalizedTagId = normalizeTagId(tagId);

        // 通过TAG_MAPPINGS查询更可靠
        return GE_CT_TAG_MAPPINGS.containsKey(normalizedTagId);
    }

    /**
     * 将标签整数值转换为字符串表示形式
     *
     * @param tag 标签整数值
     * @return 字符串形式的标签ID
     */
    public static String tagToString(int tag) {
        try {
            int group = (tag >> 16) & 0xFFFF;
            int element = tag & 0xFFFF;

            return String.format("(%04X,%04X)", group, element);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将字符串形式的标签ID转换为整数值
     *
     * @param tagId 字符串形式的标签ID
     * @return 标签的整数值
     */
    public static int stringToTag(String tagId) {
        if (tagId == null) {
            return 0;
        }

        String normalizedId = normalizeTagId(tagId);

        try {
            // 提取十六进制部分
            String group = normalizedId.substring(1, 5);
            String element = normalizedId.substring(6, 10);

            int groupInt = Integer.parseInt(group, 16);
            int elemInt = Integer.parseInt(element, 16);

            return (groupInt << 16) | elemInt;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 标准化标签ID格式
     *
     * @param tagId 原始标签ID
     * @return 标准化后的标签ID，格式为(XXXX,XXXX)
     */
    public static String normalizeTagId(String tagId) {
        if (tagId == null) {
            return null;
        }

        if (tagId.matches("\\([0-9A-Fa-f]{4},[0-9A-Fa-f]{4}\\)")) {
            // 已经是标准格式
            return tagId.toUpperCase();
        }

        // 移除所有非十六进制字符
        String hexOnly = tagId.replaceAll("[^0-9A-Fa-f]", "");

        // 如果长度等于8，重新格式化
        if (hexOnly.length() == 8) {
            return "(" + hexOnly.substring(0, 4).toUpperCase() + "," +
                    hexOnly.substring(4, 8).toUpperCase() + ")";
        }

        // 无法标准化，返回原始值
        return tagId;
    }

    // 注册所有标签
    static {
        registerStandardTags();
        registerGECTTags();
    }

    /**
     * 注册所有标准DICOM标签
     */
    private static void registerStandardTags() {
        // 患者相关
        registerTag(Patient.PATIENT_ID, "PatientID", VR.LO);
        registerTag(Patient.PATIENT_NAME, "PatientName", VR.PN);
        registerTag(Patient.PATIENT_BIRTH_DATE, "PatientBirthDate", VR.DA);
        registerTag(Patient.PATIENT_SEX, "PatientSex", VR.CS);
        registerTag(Patient.PATIENT_AGE, "PatientAge", VR.AS);
        registerTag(Patient.PATIENT_WEIGHT, "PatientWeight", VR.DS);
        registerTag(Patient.PATIENT_SIZE, "PatientSize", VR.DS);
        registerTag(Patient.PATIENT_STATE, "PatientState", VR.CS);
        registerTag(Patient.OTHER_PATIENT_IDS, "OtherPatientIDs", VR.LO);
        registerTag(Patient.ADDITIONAL_PATIENT_HISTORY, "AdditionalPatientHistory", VR.LT);

        // 添加其他标准标签注册...
        // 研究相关
        registerTag(Study.STUDY_INSTANCE_UID, "StudyInstanceUID", VR.UI);
        registerTag(Study.STUDY_ID, "StudyID", VR.SH);
        registerTag(Study.STUDY_DATE, "StudyDate", VR.DA);
        registerTag(Study.STUDY_TIME, "StudyTime", VR.TM);
        registerTag(Study.STUDY_DESCRIPTION, "StudyDescription", VR.LO);
        registerTag(Study.ACCESSION_NUMBER, "AccessionNumber", VR.SH);
        registerTag(Study.REFERRING_PHYSICIAN_NAME, "ReferringPhysicianName", VR.PN);
        registerTag(Study.PROTOCOL_NAME, "ProtocolName", VR.LO);

        // 序列相关
        registerTag(Series.SERIES_INSTANCE_UID, "SeriesInstanceUID", VR.UI);
        registerTag(Series.SERIES_NUMBER, "SeriesNumber", VR.IS);
        registerTag(Series.SERIES_DESCRIPTION, "SeriesDescription", VR.LO);
        registerTag(Series.SERIES_DATE, "SeriesDate", VR.DA);
        registerTag(Series.SERIES_TIME, "SeriesTime", VR.TM);
        registerTag(Series.MODALITY, "Modality", VR.CS);
        registerTag(Series.BODY_PART_EXAMINED, "BodyPartExamined", VR.CS);
        registerTag(Series.SERIES_STATUS, "SeriesStatus", VR.CS);
        registerTag(Series.SERIES_PRIORITY, "SeriesPriority", VR.CS);
        registerTag(Series.SERIES_COMPLETION_DATE, "SeriesCompletionDate", VR.DA);
        registerTag(Series.SERIES_COMPLETION_TIME, "SeriesCompletionTime", VR.TM);
        registerTag(Series.SERIES_VERIFIED_DATE, "SeriesVerifiedDate", VR.DA);
        registerTag(Series.SERIES_VERIFIED_TIME, "SeriesVerifiedTime", VR.TM);

        // 图像相关
        registerTag(Image.SOP_INSTANCE_UID, "SOPInstanceUID", VR.UI);
        registerTag(Image.INSTANCE_NUMBER, "InstanceNumber", VR.IS);
        registerTag(Image.IMAGE_TYPE, "ImageType", VR.CS);
        registerTag(Image.ROWS, "Rows", VR.US);
        registerTag(Image.COLUMNS, "Columns", VR.US);
        registerTag(Image.BITS_ALLOCATED, "BitsAllocated", VR.US);
        registerTag(Image.BITS_STORED, "BitsStored", VR.US);
        registerTag(Image.HIGH_BIT, "HighBit", VR.US);
        registerTag(Image.SAMPLES_PER_PIXEL, "SamplesPerPixel", VR.US);
        registerTag(Image.PIXEL_REPRESENTATION, "PixelRepresentation", VR.US);
        registerTag(Image.PIXEL_REPRESENTATION, "PhotometricInterpretation", VR.CS);
        registerTag(Image.WINDOW_CENTER, "WindowCenter", VR.DS);
        registerTag(Image.WINDOW_WIDTH, "WindowWidth", VR.DS);
        registerTag(Image.RESCALE_INTERCEPT, "RescaleIntercept", VR.DS);
        registerTag(Image.RESCALE_SLOPE, "RescaleSlope", VR.DS);
        registerTag(Image.RESCALE_TYPE, "RescaleType", VR.LO);
        registerTag(Image.PIXEL_SPACING, "PixelSpacing", VR.DS);
        registerTag(Image.SLICE_THICKNESS, "SliceThickness", VR.DS);
        registerTag(Image.SPACING_BETWEEN_SLICES, "SpacingBetweenSlices", VR.DS);
        registerTag(Image.IMAGE_POSITION_PATIENT, "ImagePositionPatient", VR.DS);
        registerTag(Image.IMAGE_ORIENTATION_PATIENT, "ImageOrientationPatient", VR.DS);
        registerTag(Image.PIXEL_DATA, "PixelData", VR.OW);
        registerTag(Image.IMAGE_DATE, "ContentDate", VR.DA);
        registerTag(Image.IMAGE_TIME, "ContentTime", VR.TM);
        registerTag(Image.ACQUISITION_DATE, "AcquisitionDate", VR.DA);
        registerTag(Image.ACQUISITION_TIME, "AcquisitionTime", VR.TM);
        registerTag(Image.SLICE_LOCATION, "SliceLocation", VR.DS);
        registerTag(Image.IMAGE_COMMENTS, "ImageComments", VR.LT);
        registerTag(Image.FRAME_OF_REFERENCE_UID, "FrameOfReferenceUID", VR.UI);
        registerTag(Image.POSITION_REFERENCE_INDICATOR, "PositionReferenceIndicator", VR.LO);

        // 设备相关
        registerTag(Equipment.MANUFACTURER, "Manufacturer", VR.LO);
        registerTag(Equipment.MANUFACTURER_MODEL_NAME, "ManufacturerModelName", VR.LO);
        registerTag(Equipment.DEVICE_SERIAL_NUMBER, "DeviceSerialNumber", VR.LO);
        registerTag(Equipment.SOFTWARE_VERSIONS, "SoftwareVersions", VR.LO);
        registerTag(Equipment.STATION_NAME, "StationName", VR.SH);
        registerTag(Equipment.INSTITUTION_NAME, "InstitutionName", VR.LO);
        registerTag(Equipment.INSTITUTION_ADDRESS, "InstitutionAddress", VR.ST);
        registerTag(Equipment.INSTITUTIONAL_DEPARTMENT_NAME, "InstitutionalDepartmentName", VR.LO);
        registerTag(Equipment.STATION_AE_TITLE, "StationAETitle", VR.AE);

        // CT相关
        registerTag(CT.KVP, "KVP", VR.DS);
        registerTag(CT.FIELD_OF_VIEW, "FieldOfView", VR.DS);
        registerTag(CT.RECONSTRUCTION_DIAMETER, "ReconstructionDiameter", VR.DS);
        registerTag(CT.ACQUISITION_NUMBER, "AcquisitionNumber", VR.IS);
        registerTag(CT.GANTRY_TILT, "GantryTilt", VR.DS);
        registerTag(CT.CONVOLUTION_KERNEL, "ConvolutionKernel", VR.SH);
        registerTag(CT.EXPOSURE, "Exposure", VR.IS);
        registerTag(CT.EXPOSURE_TIME, "ExposureTime", VR.IS);
        registerTag(CT.XRAY_TUBE_CURRENT, "XRayTubeCurrent", VR.IS);
        registerTag(CT.REVOLUTION_TIME, "RevolutionTime", VR.DS);
        registerTag(CT.SINGLE_COLLIMATION_WIDTH, "SingleCollimationWidth", VR.DS);
        registerTag(CT.TOTAL_COLLIMATION_WIDTH, "TotalCollimationWidth", VR.DS);
        registerTag(CT.SCAN_OPTIONS, "ScanOptions", VR.CS);
        registerTag(CT.DATA_COLLECTION_DIAMETER, "DataCollectionDiameter", VR.DS);
        registerTag(CT.DISTANCE_SOURCE_TO_DETECTOR, "DistanceSourceToDetector", VR.DS);
        registerTag(CT.DISTANCE_SOURCE_TO_PATIENT, "DistanceSourceToPatient", VR.DS);
        registerTag(CT.TABLE_HEIGHT, "TableHeight", VR.DS);
        registerTag(CT.ROTATION_DIRECTION, "RotationDirection", VR.CS);
        registerTag(CT.FILTER_TYPE, "FilterType", VR.SH);
        registerTag(CT.GENERATOR_POWER, "GeneratorPower", VR.IS);
        registerTag(CT.FOCAL_SPOTS, "FocalSpots", VR.DS);
        registerTag(CT.PATIENT_POSITION, "PatientPosition", VR.CS);
        registerTag(CT.TABLE_POSITION, "TablePosition", VR.DS);
        registerTag(CT.TABLE_SPEED, "TableSpeed", VR.DS);
        registerTag(CT.CT_DOSE_LENGTH_PRODUCT, "CTDoseLengthProduct", VR.DS);
        registerTag(CT.CT_DOSE_INDEX_VOL, "CTDoseIndexVol", VR.DS);
    }

    /**
     * 注册所有GE CT特有标签
     * 从原DicomTags移植
     */
    private static void registerGECTTags() {
        // GE CT特有标签
        registerGECTTag(GECT.CT_TUBE_CURRENT_IN_MA, "GE CT Tube Current", VR.DS);
        registerGECTTag(GECT.CT_TUBE_VOLTAGE_IN_KV, "GE CT Tube Voltage", VR.DS);
        registerGECTTag(GECT.RECONSTRUCTION_ALGORITHM, "Reconstruction Algorithm", VR.SH);
        registerGECTTag(GECT.ACQUISITION_WORKLIST_NUMBER, "Acquisition Worklist Number", VR.SS);
        registerGECTTag(GECT.ACTUAL_SLICE_THICKNESS, "Actual Slice Thickness", VR.DS);

        // 时间相关标签
        registerGECTTag("(0008,0012)", "InstanceCreationDate", VR.DA);
        registerGECTTag("(0008,0013)", "InstanceCreationTime", VR.TM);

        // GE 私有标签组 (详细分组)
        // GEMS_IDEN_01 组 (0009,xxxx)
        registerGECTTag("(0009,0010)", "Private Creator GEMS_IDEN_01", VR.LO);
        registerGECTTag("(0009,1001)", "CT_LIGHTSPEED", VR.LO);
        registerGECTTag("(0009,1002)", "StationID", VR.SH);
        registerGECTTag("(0009,1004)", "DeviceModelName", VR.SH);
        registerGECTTag("(0009,1027)", "TimestampSecondsSinceEpoch", VR.SL);
        registerGECTTag("(0009,10E3)", "Unknown Private Tag", VR.UI);

        // GEMS_ACQU_01 组 (0019,xxxx)
        registerGECTTag("(0019,0010)", "Private Creator GEMS_ACQU_01", VR.LO);
        registerGECTTag("(0019,1002)", "View Offset", VR.SL);
        registerGECTTag("(0019,1003)", "View Center Point", VR.DS);
        registerGECTTag("(0019,100F)", "Horizontal Frame Of Reference", VR.DS);
        registerGECTTag("(0019,1011)", "Series Contrast", VR.SS);
        registerGECTTag("(0019,1018)", "First Scan RAS", VR.LO);
        registerGECTTag("(0019,101A)", "Last Scan RAS", VR.LO);
        registerGECTTag("(0019,1023)", "Table Speed", VR.DS);
        registerGECTTag("(0019,1024)", "Mid Scan Time", VR.DS);
        registerGECTTag("(0019,1025)", "Mid Scan Flag", VR.SS);
        registerGECTTag("(0019,1026)", "Eff Duration", VR.SL);
        registerGECTTag("(0019,1027)", "CTDIvol", VR.DS);
        registerGECTTag("(0019,102C)", "Num Of Ref Channels", VR.SL);
        registerGECTTag("(0019,102E)", "Phantom Type", VR.DS);
        registerGECTTag("(0019,102F)", "Source Current", VR.DS);
        registerGECTTag("(0019,1039)", "View Matrix Size", VR.SS);
        registerGECTTag("(0019,1042)", "Data Type", VR.SS);
        registerGECTTag("(0019,1043)", "Padding", VR.SS);
        registerGECTTag("(0019,1047)", "Interpolation Type", VR.SS);
        registerGECTTag("(0019,1052)", "Recon Filter Type", VR.SS);
        registerGECTTag("(0019,106A)", "Scan Mode", VR.SS);

        // 还有更多GE私有标签可以从DicomTags.java中移植
        // 由于篇幅限制，未列出全部标签
    }

    /**
     * 注册标准DICOM标签
     */
    private static void registerTag(String tagId, String name, VR vr) {
        if (tagId != null && name != null) {
            String normalizedTagId = normalizeTagId(tagId);
            TAG_MAPPINGS.put(normalizedTagId, name);
            TAG_VR_MAPPINGS.put(normalizedTagId, vr);

            // 添加标签名称到标签ID的映射
            TAG_NAME_TO_ID.put(name, normalizedTagId);
        }
    }

    /**
     * 注册GE CT特有标签
     */
    private static void registerGECTTag(String tagId, String name, VR vr) {
        if (tagId != null && name != null) {
            String normalizedTagId = normalizeTagId(tagId);
            GE_CT_TAG_MAPPINGS.put(normalizedTagId, name);
            TAG_MAPPINGS.put(normalizedTagId, name);
            TAG_VR_MAPPINGS.put(normalizedTagId, vr);

            // 添加标签名称到标签ID的映射
            TAG_NAME_TO_ID.put(name, normalizedTagId);
        }
    }

    /**
     * 获取标签名称
     * 优先使用GE CT特有标签，然后是标准标签
     *
     * @param tagId 标签ID
     * @return 标签名称
     */
    public static String getTagName(String tagId) {
        if (tagId == null) {
            return "Unknown";
        }

        String normalizedTagId = normalizeTagId(tagId);

        // 从注册表中获取
        String name = TAG_MAPPINGS.get(normalizedTagId);
        if (name != null) {
            return name;
        }

        // 尝试从dcm4che字典获取
        try {
            int tag = stringToTag(tagId);
            String dictName = DICT.keywordOf(tag);
            return dictName != null ? dictName : "Tag-" + tagId;
        } catch (Exception e) {
            return "Tag-" + tagId;
        }
    }

    /**
     * 获取标签的VR类型
     *
     * @param tagId 标签ID
     * @return 标签的VR类型
     */
    public static VR getTagVR(String tagId) {
        if (tagId == null) {
            return VR.UN;
        }

        String normalizedTagId = normalizeTagId(tagId);

        // 从映射中获取VR
        VR vr = TAG_VR_MAPPINGS.get(normalizedTagId);
        if (vr != null) {
            return vr;
        }

        // 尝试仍cm4che字典获取
        try {
            int tag = stringToTag(tagId);
            return DICT.vrOf(tag);
        } catch (Exception e) {
            return VR.UN;
        }
    }

    /**
     * 获取所有已注册标签的ID集合
     *
     * @return 标签ID集合
     */
    public static Set<String> getAllTagIds() {
        return Collections.unmodifiableSet(TAG_MAPPINGS.keySet());
    }

    /**
     * 获取GE CT特有标签ID集合
     *
     * @return GE CT特有标签ID集合
     */
    public static Set<String> getGECTTagIds() {
        return Collections.unmodifiableSet(GE_CT_TAG_MAPPINGS.keySet());
    }

    /**
     * 获取所有标签名集合
     *
     * @return 标签名集合
     */
    public static Set<String> getAllTagNames() {
        Set<String> tagNames = new HashSet<>(TAG_MAPPINGS.values());
        return Collections.unmodifiableSet(tagNames);
    }

    /**
     * 检查标签名是否存在
     *
     * @param tagName 标签名
     * @return 是否存在
     */
    public static boolean containsTagName(String tagName) {
        if (tagName == null) {
            return false;
        }

        // 特殊处理：对于特殊列名，直接返回true
        if ("ImagePosition".equals(tagName) || "ImageCount".equals(tagName) ||
            "FieldOfView".equals(tagName) || "ReconstructionDiameter".equals(tagName) ||
            "ConvolutionKernel".equals(tagName) || "KVP".equals(tagName) ||
            "GantryTilt".equals(tagName)) {
            return true;
        }

        return TAG_MAPPINGS.containsValue(tagName);
    }

    /**
     * 检查标签是否有效
     *
     * @param tagId 标签ID
     * @return 是否有效
     */
    public static boolean isValidTag(String tagId) {
        if (tagId == null) {
            return false;
        }

        // 特殊处理：对于特殊标签ID，直接返回true
        if ("special".equals(tagId)) {
            return true;
        }

        return TAG_MAPPINGS.containsKey(normalizeTagId(tagId));
    }

    /**
     * 根据标签名称获取标签ID
     *
     * @param tagName 标签名称，如PatientID、StudyDate等
     * @return 标签ID，如果未找到返回空字符串
     */
    public static String getTagIdByName(String tagName) {
        if (tagName == null || tagName.isEmpty()) {
            return "";
        }

        // 直接从映射中获取
        String tagId = TAG_NAME_TO_ID.get(tagName);
        if (tagId != null) {
            return tagId;
        }

        // 如果没有直接匹配，尝试使用内置的常量字段
        // 患者相关标签
        if ("PatientID".equals(tagName)) return Patient.PATIENT_ID;
        if ("PatientName".equals(tagName)) return Patient.PATIENT_NAME;
        if ("PatientBirthDate".equals(tagName)) return Patient.PATIENT_BIRTH_DATE;
        if ("PatientSex".equals(tagName)) return Patient.PATIENT_SEX;
        if ("PatientAge".equals(tagName)) return Patient.PATIENT_AGE;
        if ("PatientWeight".equals(tagName)) return Patient.PATIENT_WEIGHT;

        // 检查相关标签
        if ("StudyID".equals(tagName)) return Study.STUDY_ID;
        if ("StudyDate".equals(tagName)) return Study.STUDY_DATE;
        if ("StudyTime".equals(tagName)) return Study.STUDY_TIME;
        if ("StudyDescription".equals(tagName)) return Study.STUDY_DESCRIPTION;
        if ("StudyInstanceUID".equals(tagName)) return Study.STUDY_INSTANCE_UID;

        // 序列相关标签
        if ("SeriesNumber".equals(tagName)) return Series.SERIES_NUMBER;
        if ("SeriesDescription".equals(tagName)) return Series.SERIES_DESCRIPTION;
        if ("SeriesInstanceUID".equals(tagName)) return Series.SERIES_INSTANCE_UID;
        if ("Modality".equals(tagName)) return Series.MODALITY;
        if ("StationName".equals(tagName)) return Equipment.STATION_NAME;
        if ("Manufacturer".equals(tagName)) return Equipment.MANUFACTURER;

        // 图像相关标签
        if ("InstanceNumber".equals(tagName)) return Image.INSTANCE_NUMBER;
        // 特殊处理：ImagePosition 映射到 IMAGE_POSITION_PATIENT
        if ("ImagePosition".equals(tagName)) return Image.IMAGE_POSITION_PATIENT;
        if ("SliceThickness".equals(tagName)) return Image.SLICE_THICKNESS;
        if ("GantryTilt".equals(tagName)) return CT.GANTRY_TILT;
        if ("FieldOfView".equals(tagName)) return CT.FIELD_OF_VIEW;
        if ("ReconstructionDiameter".equals(tagName)) return CT.RECONSTRUCTION_DIAMETER;
        if ("ConvolutionKernel".equals(tagName)) return CT.CONVOLUTION_KERNEL;
        if ("KVP".equals(tagName)) return CT.KVP;
        if ("Rows".equals(tagName)) return Image.ROWS;
        if ("Columns".equals(tagName)) return Image.COLUMNS;
        if ("ImageComments".equals(tagName)) return Image.IMAGE_COMMENTS;

        // 如果没有匹配，返回空字符串
        return "";
    }
}