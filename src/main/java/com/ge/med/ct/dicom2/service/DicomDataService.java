package com.ge.med.ct.dicom2.service;

import com.ge.med.ct.dcm_se.core.cfg.ConfigManager;
import com.ge.med.ct.dcm_se.core.cfg.dicom.DicomConfigService;
import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.dicom2.core.DicomDataProviderImpl;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.LogManager;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.logging.Logger;

/**
 * DICOM数据服务
 * 负责DICOM目录扫描和数据管理
 */
public class DicomDataService implements AutoCloseable {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomDataService.class);
    private static volatile DicomDataService instance;

    private final DicomConfigService dicomConfig;
    private final DicomFileService fileManager;
    private DicomDataProvider dicomProvider;

    private DicomDataService() {
        ConfigManager configManager = ConfigManager.getInstance();
        this.dicomConfig = configManager.getDicomConfig();
        this.fileManager = DicomFileService.getInstance();
        initializeProvider();
    }

    public static DicomDataService getInstance() {
        if (instance == null) {
            synchronized (DicomDataService.class) {
                if (instance == null) {
                    instance = new DicomDataService();
                }
            }
        }
        return instance;
    }

    public static class LoadingProgress {
        private final int processed;
        private final int total;

        public LoadingProgress(int processed, int total) {
            this.processed = processed;
            this.total = total;
        }

        public int getProcessed() {
            return processed;
        }

        public int getTotal() {
            return total;
        }

    }

    @HandleException(errorCode = ErrorCode.DICOM_INIT)
    private void initializeProvider() throws DicomException {
        try {
            dicomProvider = DicomDataProviderImpl.getInstance();

            // 获取DICOM扫描目录
            String scanDir = dicomConfig.getRootDirectory();
            File dicomDir = new File(scanDir);

            if (!dicomDir.exists()) {
                LOG.warning("DICOM扫描目录不存在: " + scanDir);
            }

            fileManager.startMonitoring();

        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_INIT, DicomMessages.VALIDATION_ERROR, e,
                    "DICOM服务初始化失败");
        }
    }

    /**
     * 处理单个DICOM文件
     *
     * @param file     DICOM文件
     * @param callback 处理成功后的回调
     * @throws DicomException 如果处理失败
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void processFile(File file, Consumer<DicomFileModel> callback) throws DicomException {
        if (file == null || !file.exists() || !file.isFile()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.FILE_NOT_FOUND, file != null ? file.getPath() : "null");
        }

        try {
            DicomFileModel model = fileManager.validateFile(file.getAbsolutePath());
            if (model != null && callback != null) {
                callback.accept(model);
            }
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.PROCESSING,
                    DicomMessages.PROCESSING_ERROR, e, file.getName(), e.getMessage());
        }
    }

    /**
     * 加载DICOM文件
     * 改进版：与其他方法保持一致的处理逻辑
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    public void loadDicomFiles(String directory, Consumer<LoadingProgress> progressCallback) throws DicomException {
        File dir = new File(directory);
        if (!dir.exists() || !dir.isDirectory())
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.DIRECTORY_NOT_EXIST,
                    directory);

        File[] files = dir.listFiles((d, name) -> name.toLowerCase().endsWith(".dcm"));
        if (files == null || files.length == 0) {
            LOG.info("目录中没有找到DICOM文件: " + directory);
            return;
        }

        int total = files.length;
        LOG.info("开始加载 " + total + " 个DICOM文件");

        // 尝试处理所有文件，收集成功的模型
        List<DicomFileModel> successfulModels = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        for (File file : files) {
            try {
                DicomFileModel model = fileManager.validateFile(file.getAbsolutePath());
                successfulModels.add(model);
                if (progressCallback != null) {
                    progressCallback.accept(new LoadingProgress(successfulModels.size(), total));
                }
            } catch (Exception e) {
                failedFiles.add(file.getName());
                LOG.warning("处理文件失败: " + file.getName() + ", 原因: " + e.getMessage());
            }
        }

        // 使用收集到的模型创建检查
        for (DicomFileModel model : successfulModels) {
            dicomProvider.addFileModel(model);
        }

        LOG.info("DICOM文件加载完成: 成功=" + successfulModels.size() + ", 失败=" + failedFiles.size());
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    public void refreshDicomData() throws DicomException {
        if (dicomProvider != null) {
            loadDicomFiles(dicomConfig.getRootDirectory(), null);
            LOG.info("DICOM数据已刷新");
        } else {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_INIT, DicomMessages.VALIDATION_ERROR,
                    "DICOM数据提供者未初始化");
        }
    }

    public DicomDataProvider getDicomProvider() {
        return dicomProvider;
    }

    @Override
    public void close() {
        if (fileManager != null)
            fileManager.stopMonitoring();
        if (dicomProvider != null)
            dicomProvider.shutdown();
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    public void loadDicomData(String directoryPath, Consumer<LoadingProgress> progressCallback) throws DicomException {
        // 1. 初始化进度
        if (progressCallback != null) {
            progressCallback.accept(new LoadingProgress(0, 0));
        }

        // 2. 确定扫描路径
        String scanPath = directoryPath != null ? directoryPath : dicomConfig.getRootDirectory();

        // 3. 预扫描目录
        LOG.info("开始加载DICOM数据: " + scanPath);
        DicomFileService.PreScanResult preScanResult = fileManager.preScanDirectory(scanPath, true);

        // 4. 处理预扫描结果
        processPreScanResult(preScanResult, progressCallback);

        // 5. 启动文件监控
        fileManager.startMonitoring();
    }

    /**
     * 处理预扫描结果
     */
    private void processPreScanResult(DicomFileService.PreScanResult result, Consumer<LoadingProgress> progressCallback)
            throws DicomException {
        int totalFiles = result.getTotalFileCount();
        if (totalFiles == 0) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, "没有找到DICOM文件");
        }

        initializeProgress(progressCallback, totalFiles);
        ProcessingStats stats = new ProcessingStats(totalFiles);

        // 处理有效的检查
        result.getExamToFilesMap().values().forEach(examFiles -> processExamFiles(examFiles, stats, progressCallback));

        // 处理缺少检查信息的文件
        processEmptyExamFiles(result.getEmptyExamFiles(), stats, progressCallback);

        // 记录处理结果
        logProcessingResults(stats);
        finalizeProgress(progressCallback, stats);
    }

    /**
     * 初始化进度
     */
    private void initializeProgress(Consumer<LoadingProgress> progressCallback, int totalFiles) {
        if (progressCallback != null) {
            progressCallback.accept(new LoadingProgress(0, totalFiles));
        }
    }

    /**
     * 处理缺少检查信息的文件
     * 改进版：与 processExamFiles 保持一致的处理逻辑
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void processEmptyExamFiles(List<String> emptyExamFiles, ProcessingStats stats,
            Consumer<LoadingProgress> progressCallback) {
        if (emptyExamFiles.isEmpty()) {
            return;
        }

        LOG.info("处理 " + emptyExamFiles.size() + " 个缺少检查信息的文件");

        // 尝试处理所有文件，收集成功的模型
        List<DicomFileModel> successfulModels = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        for (String filePath : emptyExamFiles) {
            try {
                DicomFileModel model = fileManager.validateFile(filePath);
                successfulModels.add(model);
                stats.incrementProcessed();
                updateProgressWithStats(stats, progressCallback);
            } catch (Exception e) {
                LOG.warning("处理文件失败: " + new File(filePath).getName() + ", 原因: " + e.getMessage());
                failedFiles.add(filePath);
                stats.failed.incrementAndGet();
            }
        }

        // 如果没有成功处理任何文件，则记录失败
        if (successfulModels.isEmpty()) {
            LOG.warning("缺少检查信息的文件处理失败: 所有" + emptyExamFiles.size() + "个文件都无法处理");
            return;
        }

        // 使用收集到的模型创建检查
        for (DicomFileModel model : successfulModels) {
            dicomProvider.addFileModel(model);
        }

        // 记录成功率
        if (!failedFiles.isEmpty()) {
            LOG.info("缺少检查信息的文件处理统计: 成功=" + successfulModels.size() + ", 失败=" + failedFiles.size());
        }
    }

    /**
     * 记录处理结果
     */
    private void logProcessingResults(ProcessingStats stats) {
        if (stats.getFailed() > 0) {
            LOG.warning("处理完成: 成功=" + stats.getProcessed() + ", 失败=" + stats.getFailed());
        } else {
            LOG.info("处理完成: 成功=" + stats.getProcessed());
        }
    }

    /**
     * 完成进度
     */
    private void finalizeProgress(Consumer<LoadingProgress> progressCallback, ProcessingStats stats) {
        if (progressCallback != null) {
            progressCallback.accept(new LoadingProgress(stats.getProcessed(), stats.getTotal()));
        }
    }

    /**
     * 处理统计类
     */
    private class ProcessingStats {
        private final AtomicInteger processed = new AtomicInteger(0);
        public final AtomicInteger failed = new AtomicInteger(0);
        public final StringBuilder errors = new StringBuilder();
        private final int totalFiles;

        public ProcessingStats(int totalFiles) {
            this.totalFiles = totalFiles;
        }

        public int getProcessed() {
            return processed.get();
        }

        public int getFailed() {
            return failed.get();
        }

        public int getTotal() {
            return totalFiles;
        }

        public String getErrors() {
            return errors.toString();
        }

        public void incrementProcessed() {
            processed.incrementAndGet();
        }
    }

    /**
     * 处理检查文件
     * 改进版：不再特别依赖第一个文件，尝试处理所有文件，只要有一个成功就能创建检查模型
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void processExamFiles(List<String> examFiles, ProcessingStats stats,
            Consumer<LoadingProgress> progressCallback) {
        if (examFiles.isEmpty()) {
            return;
        }

        List<DicomFileModel> successfulModels = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        for (String filePath : examFiles) {
            try {
                DicomFileModel model = fileManager.validateFile(filePath);
                successfulModels.add(model);
                stats.incrementProcessed();
                updateProgressWithStats(stats, progressCallback);
            } catch (Exception e) {
                String fileName = new File(filePath).getName();
                String errorMessage = e.getMessage();
                if (e.getCause() != null) {
                    errorMessage += " (" + e.getCause().getMessage() + ")";
                }
                LOG.warning("处理失败: " + fileName + " - " + errorMessage);
                failedFiles.add(filePath);
                stats.failed.incrementAndGet();
            }
        }

        if (successfulModels.isEmpty()) {
            LOG.warning("检查处理失败: " + examFiles.size() + "个文件");
            return;
        }

        for (DicomFileModel model : successfulModels) {
            dicomProvider.addFileModel(model);
        }

        if (!failedFiles.isEmpty()) {
            LOG.info("处理统计: 成功=" + successfulModels.size() + ", 失败=" + failedFiles.size());
        }
    }

    /**
     * 更新进度
     */
    private void updateProgressWithStats(ProcessingStats stats, Consumer<LoadingProgress> progressCallback) {
        if (progressCallback != null) {
            progressCallback.accept(new LoadingProgress(stats.getProcessed(), stats.getTotal()));
        }
    }

    /**
     * 处理新文件
     * 改进版：与其他方法保持一致的处理逻辑
     *
     * @param files 文件列表
     * @throws DicomException 如果处理失败
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    public void processNewFiles(List<String> files) throws DicomException {
        if (files == null || files.isEmpty()) {
            return;
        }

        // 尝试处理所有文件，收集成功的模型
        List<DicomFileModel> successfulModels = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        for (String filePath : files) {
            try {
                DicomFileModel model = fileManager.validateFile(filePath);
                successfulModels.add(model);
            } catch (Exception e) {
                failedFiles.add(filePath);
                // 简化错误消息，避免重复记录
                String fileName = new File(filePath).getName();
                String errorType = e.getClass().getSimpleName();
                LOG.fine("处理文件失败: " + fileName + ", 类型: " + errorType);
            }
        }

        // 使用收集到的模型创建检查
        for (DicomFileModel model : successfulModels) {
            dicomProvider.addFileModel(model);
        }

        // 记录处理结果
        if (!failedFiles.isEmpty()) {
            LOG.warning("新文件处理完成: 成功=" + successfulModels.size() + ", 失败=" + failedFiles.size());
        } else {
            LOG.info("新文件处理完成: 成功=" + successfulModels.size());
        }
    }
}