package com.ge.med.ct.dicom2.model;

import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * DICOM图像类
 * 表示DICOM文件中的图像数据
 */
public class DicomImage {
    private static final Logger LOG = Logger.getLogger(DicomImage.class.getName());

    // 图像标识符
    private final String id;

    // 图像基本信息
    private int rows;
    private int columns;
    private int bitsAllocated;
    private int bitsStored;
    private int highBit;
    private int samplesPerPixel;
    private int pixelRepresentation;
    private String photometricInterpretation;

    // 图像位置和方向
    private float[] imagePosition;
    private float[] imageOrientation;
    private float[] pixelSpacing;

    // 窗宽窗位
    private float windowCenter;
    private float windowWidth;
    private float rescaleSlope = 1.0f;
    private float rescaleIntercept = 0.0f;

    // 图像数据（可选，通常不会预先加载）
    private byte[] pixelData;

    // 存储DICOM标签
    private final Map<String, DicomTag> tags;

    /**
     * 构造函数
     *
     * @param id 图像ID，通常与SOP实例UID相同
     * @throws DicomException 如果ID为空
     */
    public DicomImage(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_IMAGE_PROCESSING, DicomMessages.ID_EMPTY);
        }
        this.id = id;
        this.tags = new HashMap<>();
        LOG.fine("创建新的DICOM图像: " + id);
    }

    /**
     * 添加标签
     *
     * @param tagId 标签ID
     * @param tag   标签对象
     */
    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            LOG.fine("添加标签 " + tagId + " 到图像 " + id);
        }
    }

    /**
     * 添加标签（支持字节数组值）
     *
     * @param tagId 标签ID
     * @param value 字节数组值
     */
    public void addTag(String tagId, byte[] value) {
        if (tagId != null && value != null) {
            try {
                // 如果是像素数据标签，创建DicomTag对象并添加
                if (tagId.equals(DicomTagConstants.Image.PIXEL_DATA)) {
                    setPixelData(value);
                    LOG.fine("设置像素数据 " + tagId + " 到图像 " + id);
                } else {
                    // 其他二进制标签，创建DicomTag对象并添加
                    DicomTag tag = new DicomTag(tagId, new String(value), org.dcm4che3.data.VR.OB);
                    tags.put(tagId, tag);
                    LOG.fine("添加二进制标签 " + tagId + " 到图像 " + id);
                }
            } catch (DicomException e) {
                LOG.warning("添加标签失败 " + tagId + ": " + e.getMessage());
            }
        }
    }

    /**
     * 获取标签
     *
     * @param tagId 标签ID
     * @return 标签对象
     */
    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    /**
     * 获取标签值
     *
     * @param tagId 标签ID
     * @return 标签值的字符串表示，如果标签不存在则返回空字符串
     */
    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : "";
    }

    /**
     * 获取所有标签
     *
     * @return 标签映射
     */
    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    public String getId() {
        return id;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public int getColumns() {
        return columns;
    }

    public void setColumns(int columns) {
        this.columns = columns;
    }

    public int getBitsAllocated() {
        return bitsAllocated;
    }

    public void setBitsAllocated(int bitsAllocated) {
        this.bitsAllocated = bitsAllocated;
    }

    public int getBitsStored() {
        return bitsStored;
    }

    public void setBitsStored(int bitsStored) {
        this.bitsStored = bitsStored;
    }

    public int getHighBit() {
        return highBit;
    }

    public void setHighBit(int highBit) {
        this.highBit = highBit;
    }

    public int getSamplesPerPixel() {
        return samplesPerPixel;
    }

    public void setSamplesPerPixel(int samplesPerPixel) {
        this.samplesPerPixel = samplesPerPixel;
    }

    public int getPixelRepresentation() {
        return pixelRepresentation;
    }

    public void setPixelRepresentation(int pixelRepresentation) {
        this.pixelRepresentation = pixelRepresentation;
    }

    public String getPhotometricInterpretation() {
        return photometricInterpretation;
    }

    public void setPhotometricInterpretation(String photometricInterpretation) {
        this.photometricInterpretation = photometricInterpretation;
    }

    public float[] getImagePosition() {
        return imagePosition;
    }

    public void setImagePosition(float[] imagePosition) {
        this.imagePosition = imagePosition;
    }

    public float[] getImageOrientation() {
        return imageOrientation;
    }

    public void setImageOrientation(float[] imageOrientation) {
        this.imageOrientation = imageOrientation;
    }

    public float[] getPixelSpacing() {
        return pixelSpacing;
    }

    public void setPixelSpacing(float[] pixelSpacing) {
        this.pixelSpacing = pixelSpacing;
    }

    public float getWindowCenter() {
        return windowCenter;
    }

    public void setWindowCenter(float windowCenter) {
        this.windowCenter = windowCenter;
    }

    public float getWindowWidth() {
        return windowWidth;
    }

    public void setWindowWidth(float windowWidth) {
        this.windowWidth = windowWidth;
    }

    public float getRescaleSlope() {
        return rescaleSlope;
    }

    public void setRescaleSlope(float rescaleSlope) {
        this.rescaleSlope = rescaleSlope;
    }

    public float getRescaleIntercept() {
        return rescaleIntercept;
    }

    public void setRescaleIntercept(float rescaleIntercept) {
        this.rescaleIntercept = rescaleIntercept;
    }

    public byte[] getPixelData() {
        return pixelData;
    }

    public void setPixelData(byte[] pixelData) {
        this.pixelData = pixelData;
        LOG.fine("设置像素数据，大小: " + (pixelData != null ? pixelData.length : 0) + " 字节");
    }

    /**
     * 像素数据是否已加载
     */
    public boolean isPixelDataLoaded() {
        return pixelData != null && pixelData.length > 0;
    }

    /**
     * 图像尺寸（像素数）
     */
    public int getPixelCount() {
        return rows * columns;
    }

    /**
     * 图像内存大小（字节）
     */
    public int getImageSizeInBytes() {
        return getPixelCount() * (bitsAllocated / 8) * samplesPerPixel;
    }

    /**
     * 清除像素数据以释放内存
     */
    public void clearPixelData() {
        if (pixelData != null) {
            LOG.fine("清除像素数据，释放 " + pixelData.length + " 字节");
            pixelData = null;
        }
    }

    @Override
    public String toString() {
        return String.format("DicomImage[id=%s, rows=%d, columns=%d, bitsAllocated=%d, pixelDataLoaded=%b]",
                id, rows, columns, bitsAllocated, isPixelDataLoaded());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;
        DicomImage other = (DicomImage) obj;
        return id != null && id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @HandleException(errorCode = ErrorCode.DICOM_IMAGE_PROCESSING)
    public void setPath(DicomFileModel model) throws DicomException {
        if (model != null) {
            DicomTag tag = model.getTag(DicomTagConstants.Image.SOP_INSTANCE_UID);
            if (tag != null) {
                tag.getValueAsString();
            }
        }
    }

    @HandleException(errorCode = ErrorCode.DICOM_IMAGE_PROCESSING)
    public void populateFromModel(DicomFileModel model) throws DicomException {
        if (model == null) {
            return;
        }

        model.getTagValueAsString(DicomTagConstants.Image.SOP_INSTANCE_UID);
        model.getTagValueAsInteger(DicomTagConstants.Image.INSTANCE_NUMBER);
        model.getTagValueAsInteger(DicomTagConstants.CT.ACQUISITION_NUMBER);
        model.getTagValueAsString(DicomTagConstants.Image.IMAGE_TYPE);
        model.getTagValueAsString(DicomTagConstants.Image.IMAGE_COMMENTS);
        model.getTagValueAsFloat(DicomTagConstants.Image.SLICE_LOCATION);
        model.getTagValueAsFloat(DicomTagConstants.Image.SLICE_THICKNESS);

        try {
            rows = model.getTagValueAsInteger(DicomTagConstants.Image.ROWS);
            columns = model.getTagValueAsInteger(DicomTagConstants.Image.COLUMNS);

            pixelData = model.getTagBytes(DicomTagConstants.Image.PIXEL_DATA);
        } catch (Exception e) {
            LOG.warning("无法设置像素数据: " + e.getMessage());
        }
    }
}