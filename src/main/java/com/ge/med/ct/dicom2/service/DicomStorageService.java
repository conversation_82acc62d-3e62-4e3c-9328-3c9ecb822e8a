package com.ge.med.ct.dicom2.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.ge.med.ct.dcm_se.core.cfg.ConfigManager;
import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.IOOperator;
import com.ge.med.ct.service.LogManager;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.io.DicomInputStream;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM存储服务
 * 负责DICOM文件的存储、检索、导出和JSON格式处理
 */
@HandleException(errorCode = ErrorCode.DICOM_STORAGE_ERROR)
public class DicomStorageService {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomStorageService.class);
    private static volatile DicomStorageService instance;

    // 存储路径
    private final String baseStoragePath;
    private final String exportPath;
    private final Path jsonMetadataPath;
    
    // JSON处理
    private final ObjectMapper objectMapper;

    /**
     * 私有构造函数，使用单例模式
     */
    private DicomStorageService() {
        ConfigManager configManager = ConfigManager.getInstance();
        this.baseStoragePath = configManager.getString("dicom.storage.path", "./dicom_storage");
        this.exportPath = configManager.getString("dicom.export.path", "./export");
        this.jsonMetadataPath = Paths.get(configManager.getString("dicom.json.metadata.path", "./metadata/dcm-tags.json"));
        
        // 初始化ObjectMapper
        this.objectMapper = new ObjectMapper();
        this.objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        
        // 创建存储目录
        IOOperator.createDirectories(baseStoragePath);
        IOOperator.createDirectories(exportPath);
        IOOperator.createDirectories(jsonMetadataPath.getParent().toString());
        
        LOG.info("DICOM存储服务初始化完成: 存储路径=" + baseStoragePath + ", 导出路径=" + exportPath);
    }

    /**
     * 获取单例实例
     */
    public static DicomStorageService getInstance() {
        if (instance == null) {
            synchronized (DicomStorageService.class) {
                if (instance == null) {
                    instance = new DicomStorageService();
                }
            }
        }
        return instance;
    }

    /**
     * 加载DICOM文件
     * @param studyInstanceUID 研究实例UID
     * @param seriesInstanceUID 序列实例UID
     * @param sopInstanceUID SOP实例UID
     * @return DICOM属性
     * @throws DicomException 如果加载失败
     */
    public Attributes loadDicomFile(String studyInstanceUID, String seriesInstanceUID, String sopInstanceUID)
            throws DicomException {
        validateUIDs(studyInstanceUID, seriesInstanceUID);
        
        try {
            Path filePath = getFilePath(studyInstanceUID, seriesInstanceUID, sopInstanceUID);
            
            if (!Files.exists(filePath)) {
                throw new DicomException(ErrorCode.READ, DicomMessages.FILE_NOT_FOUND, filePath.toString());
            }
            
            try (DicomInputStream dis = new DicomInputStream(filePath.toFile())) {
                return dis.readDataset();
            }
            
        } catch (IOException e) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e, 
                    "无法读取DICOM文件");
        }
    }

    /**
     * 导出DICOM数据到JSON
     * @param provider DICOM数据提供者
     * @param prefix 文件名前缀
     * @return 导出的JSON文件路径
     * @throws DicomException 如果导出失败
     */
    public String exportToJson(DicomDataProvider provider, String prefix) throws DicomException {
        if (provider == null) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, 
                    "DICOM数据提供者为空");
        }
        
        // 获取所有文件模型
        List<DicomFileModel> fileModels = provider.getAllFileModels();
        if (fileModels == null || fileModels.isEmpty()) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, 
                    "没有可导出的DICOM文件");
        }
        
        return exportToJson(fileModels, prefix);
    }

    /**
     * 导出DICOM文件模型列表到JSON
     * @param fileModels DICOM文件模型列表
     * @param prefix 文件名前缀
     * @return 导出的JSON文件路径
     * @throws DicomException 如果导出失败
     */
    public String exportToJson(List<DicomFileModel> fileModels, String prefix) throws DicomException {
        if (fileModels == null || fileModels.isEmpty()) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, 
                    "没有可导出的DICOM文件");
        }
        
        try {
            // 创建时间戳文件名
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String timestamp = dateFormat.format(new Date());
            
            // 构建文件名
            String fileName = String.format("%s_%s.json", prefix, timestamp);
            
            // 完整文件路径
            File jsonFile = Paths.get(exportPath, fileName).toFile();
            
            LOG.info("开始导出DICOM标签到JSON: " + jsonFile.getAbsolutePath());
            
            // 创建JSON结构
            Map<String, Object> rootJson = new LinkedHashMap<>();
            
            // 处理每个DICOM文件模型
            for (DicomFileModel model : fileModels) {
                Map<String, Object> fileJson = new LinkedHashMap<>();
                fileJson.put("file", model.getFilePath());
                fileJson.put("id", model.getId());
                
                // 处理标签
                Map<String, Object> tagsJson = new LinkedHashMap<>();
                for (Map.Entry<String, DicomTag> entry : model.getTags().entrySet()) {
                    String tagId = entry.getKey();
                    DicomTag tag = entry.getValue();
                    
                    Map<String, Object> tagJson = new HashMap<>();
                    tagJson.put("name", tag.getName());
                    tagJson.put("value", tag.getValueAsString());
                    tagJson.put("vr", tag.getVr().toString());
                    
                    // 添加标签分类
                    DicomTagConstants.TagCategory category = DicomTagConstants.getCategoryFromTagId(tagId);
                    tagJson.put("category", category != null ? category.name() : "OTHER");
                    
                    tagsJson.put(tagId, tagJson);
                }
                
                fileJson.put("tags", tagsJson);
                rootJson.put(model.getId(), fileJson);
            }
            
            // 写入JSON文件
            objectMapper.writeValue(jsonFile, rootJson);
            
            LOG.info("导出DICOM标签到JSON完成: " + jsonFile.getAbsolutePath());
            return jsonFile.getAbsolutePath();
            
        } catch (Exception e) {
            throw new DicomException(ErrorCode.DICOM_STORAGE_ERROR, DicomMessages.FILE_WRITE_ERROR, e, 
                    "导出DICOM标签到JSON失败");
        }
    }

    /**
     * 将DICOM标签信息保存为JSON格式
     * @param fileModel DICOM文件模型
     * @throws DicomException 如果保存失败
     */
    public void saveTagsToJson(DicomFileModel fileModel) throws DicomException {
        if (fileModel == null) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, 
                    "DICOM文件模型为空");
        }
        
        try {
            Map<String, Object> tagInfo = new HashMap<>();
            
            // 添加基本信息
            tagInfo.put("filePath", fileModel.getFilePath());
            tagInfo.put("timestamp", System.currentTimeMillis());
            
            // 添加标签信息
            Map<String, Object> tags = new HashMap<>();
            Map<String, DicomTag> dicomTags = fileModel.getTags();
            if (dicomTags != null) {
                for (Map.Entry<String, DicomTag> entry : dicomTags.entrySet()) {
                    DicomTag tag = entry.getValue();
                    Map<String, String> tagData = new HashMap<>();
                    tagData.put("value", tag.getValueAsString());
                    tagData.put("vr", tag.getVr().toString());
                    tags.put(entry.getKey(), tagData);
                }
            }
            tagInfo.put("tags", tags);
            
            // 读取现有JSON数组
            List<Map<String, Object>> jsonArray = loadTagsFromJson();
            
            // 检查是否已存在相同文件路径的条目
            boolean found = false;
            for (Map<String, Object> entry : jsonArray) {
                if (fileModel.getFilePath().equals(entry.get("filePath"))) {
                    // 更新现有条目
                    entry.put("tags", tags);
                    entry.put("timestamp", System.currentTimeMillis());
                    found = true;
                    break;
                }
            }
            
            // 如果没有找到，添加新条目
            if (!found) {
                jsonArray.add(tagInfo);
            }
            
            // 写入JSON文件
            objectMapper.writeValue(jsonMetadataPath.toFile(), jsonArray);
            
            LOG.fine("DICOM标签已保存到JSON: " + fileModel.getFilePath());
            
        } catch (IOException e) {
            throw new DicomException(ErrorCode.DICOM_STORAGE_ERROR, DicomMessages.FILE_WRITE_ERROR, e, 
                    "保存DICOM标签到JSON失败");
        }
    }

    /**
     * 从JSON文件加载标签信息
     * @return 标签信息列表
     * @throws DicomException 如果加载失败
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> loadTagsFromJson() throws DicomException {
        try {
            if (Files.exists(jsonMetadataPath)) {
                String content = new String(Files.readAllBytes(jsonMetadataPath));
                if (!content.trim().isEmpty()) {
                    return objectMapper.readValue(content, List.class);
                }
            }
            return new ArrayList<>();
        } catch (IOException e) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e, 
                    "加载DICOM标签从JSON失败");
        }
    }

    /**
     * 验证UID参数
     */
    private void validateUIDs(String studyInstanceUID, String seriesInstanceUID)
            throws DicomException {
        if (studyInstanceUID == null || studyInstanceUID.isEmpty()) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, 
                    "研究实例UID不能为空");
        }
        
        if (seriesInstanceUID == null || seriesInstanceUID.isEmpty()) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, 
                    "序列实例UID不能为空");
        }
        
        // SOP实例UID可以为空，取决于操作
    }

    /**
     * 获取存储路径
     */
    private Path getStoragePath(String studyInstanceUID, String seriesInstanceUID) {
        return Paths.get(baseStoragePath, studyInstanceUID, seriesInstanceUID);
    }

    /**
     * 获取文件路径
     */
    private Path getFilePath(String studyInstanceUID, String seriesInstanceUID, String sopInstanceUID) {
        Path storagePath = getStoragePath(studyInstanceUID, seriesInstanceUID);
        return storagePath.resolve(sopInstanceUID + ".dcm");
    }

}
