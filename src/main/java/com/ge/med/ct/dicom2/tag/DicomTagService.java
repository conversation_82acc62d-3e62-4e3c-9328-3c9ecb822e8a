package com.ge.med.ct.dicom2.tag;

import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.ElementDictionary;
import org.dcm4che3.data.VR;

import java.util.*;
import java.util.function.Predicate;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * DICOM标签服务
 * 提供标签元数据访问、查询和标签创建功能
 * 整合了原有TagMetadataProvider和TagQueryService功能
 */
public class DicomTagService {
    private static final Logger LOG = Logger.getLogger(DicomTagService.class.getName());

    // 使用标准元素字典
    private static final ElementDictionary DICT = ElementDictionary.getStandardElementDictionary();

    // 标签注册中心
    private final DicomTagRegistry registry;

    // 标签管理器
    private final DicomTagManager manager;

    // 标签解析组件
    private final DicomTagParser parser;

    // 标签缓存
    private final Map<String, DicomTag> tagCache = new HashMap<>();

    // 单例实例
    private static DicomTagService INSTANCE;

    /**
     * 获取单例实例
     */
    @HandleException(errorCode = ErrorCode.DICOM_INIT)
    public static DicomTagService getInstance() throws DicomException {
        if (INSTANCE == null) {
            DicomTagRegistry registry = new DicomTagRegistry();
            INSTANCE = new DicomTagService(registry, new DicomTagManager(registry));
        }
        return INSTANCE;
    }

    /**
     * 构造函数
     *
     * @param registry 标签注册中心
     * @param manager  标签管理器
     */
    public DicomTagService(DicomTagRegistry registry, DicomTagManager manager) throws DicomException {
        if (registry == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "标签注册中心不能为空");
        }
        if (manager == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "标签管理器不能为空");
        }
        this.registry = registry;
        this.manager = manager;

        new DicomTagFactory(new TagMetadataProvider());

        // 创建解析器
        this.parser = new DicomTagParser(new TagCreatorAdapter());

        LOG.info("DicomTagService初始化完成");
    }

    /**
     * 构造函数（用于测试）
     */
    public DicomTagService(DicomTagRegistry registry) throws DicomException {
        if (registry == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "标签注册中心不能为空");
        }
        this.registry = registry;
        this.manager = new DicomTagManager(registry);
        new DicomTagFactory(new TagMetadataProvider());
        this.parser = new DicomTagParser(new TagCreatorAdapter());
        LOG.info("DicomTagService测试实例初始化完成");
    }

    // ---------------------- 标签元数据访问 ----------------------//

    /**
     * 获取标签名称
     *
     * @param tagId 标签ID
     * @return 标签名称
     */
    public String getTagName(String tagId) {
        if (tagId == null) {
            return "Unknown";
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        String name = registry.getTagName(normalizedId);

        if (name != null && !name.startsWith("Tag-")) {
            return name;
        }

        // 尝试从DicomTagConstants获取
        String constantsName = DicomTagConstants.getTagName(normalizedId);
        if (!constantsName.startsWith("Tag-")) {
            return constantsName;
        }

        return "Tag-" + normalizedId;
    }

    /**
     * 获取标签VR类型
     *
     * @param tagId 标签ID
     * @return 标签VR类型
     */
    public VR getTagVR(String tagId) {
        if (tagId == null) {
            return VR.UN;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        VR vr = registry.getTagVR(normalizedId);

        if (vr != null && vr != VR.UN) {
            return vr;
        }

        // 尝试从标准字典查找
        try {
            int tag = DicomTagConstants.stringToTag(normalizedId);
            VR dictVr = DICT.vrOf(tag);
            if (dictVr != null) {
                return dictVr;
            }
        } catch (Exception e) {
            // 忽略异常，继续下一步尝试
        }

        return VR.UN;
    }

    /**
     * 获取标签分类
     *
     * @param tagId 标签ID
     * @return 标签分类
     */
    public DicomTagConstants.TagCategory getTagCategory(String tagId) {
        if (tagId == null) {
            return DicomTagConstants.TagCategory.OTHER;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        DicomTagConstants.TagCategory category = registry.getTagCategory(normalizedId);

        if (category != null) {
            return category;
        }

        return DicomTagConstants.getCategoryFromTagId(normalizedId);
    }

    /**
     * 判断是否为标准DICOM标签
     *
     * @param tagId 标签ID
     * @return 是否为标准标签
     */
    public boolean isStandardTag(String tagId) {
        if (tagId == null) {
            return false;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        return manager.isTagInGroup("STANDARD", normalizedId);
    }

    /**
     * 判断是否为GE CT特有标签
     *
     * @param tagId 标签ID
     * @return 是否为GE CT特有标签
     */
    public boolean isGECTTag(String tagId) {
        if (tagId == null) {
            return false;
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        return manager.isTagInGroup("GE_CT", normalizedId);
    }

    /**
     * 判断是否为私有标签
     *
     * @param tagId 标签ID
     * @return 是否为私有标签
     */
    public boolean isPrivateTag(String tagId) {
        if (tagId == null) {
            return false;
        }

        return DicomTagConstants.isPrivateTag(tagId);
    }

    // ---------------------- 标签查询 ----------------------//

    /**
     * 获取所有标签ID
     *
     * @return 所有标签ID集合
     */
    public Set<String> getAllTagIds() {
        return registry.getAllTagIds();
    }

    /**
     * 获取GE CT标签ID
     *
     * @return GE CT标签ID集合
     */
    public Set<String> getGECTTagIds() {
        return manager.getTagsInGroup("GE_CT");
    }

    /**
     * 获取标准DICOM标签ID
     *
     * @return 标准DICOM标签ID集合
     */
    public Set<String> getStandardTagIds() {
        return manager.getTagsInGroup("STANDARD");
    }

    /**
     * 根据标签分类获取标签ID
     *
     * @param category 标签分类
     * @return 指定分类的标签ID集合
     */
    public Set<String> getTagIdsByCategory(DicomTagConstants.TagCategory category) {
        if (category == null) {
            return Collections.emptySet();
        }

        String groupName;
        switch (category) {
            case PATIENT:
                groupName = "PATIENT";
                break;
            case STUDY:
                groupName = "STUDY";
                break;
            case SERIES:
                groupName = "SERIES";
                break;
            case IMAGE:
                groupName = "IMAGE";
                break;
            case EQUIPMENT:
                groupName = "EQUIPMENT";
                break;
            default:
                return registry.getTagIdsByCategory(category);
        }

        return manager.getTagsInGroup(groupName);
    }

    /**
     * 获取指定组内的标签ID
     *
     * @param groupName 组名
     * @return 组内的标签ID集合
     */
    public Set<String> getTagIdsByGroup(String groupName) {
        if (groupName == null || groupName.trim().isEmpty()) {
            return Collections.emptySet();
        }

        return manager.getTagsInGroup(groupName);
    }

    /**
     * 获取标签ID中包含指定子串的标签
     *
     * @param substring 子串
     * @return 匹配的标签ID集合
     */
    public Set<String> searchTagIdsContaining(String substring) {
        if (substring == null || substring.trim().isEmpty()) {
            return Collections.emptySet();
        }

        String searchTerm = substring.toLowerCase();

        return registry.getAllTagIds().stream()
                .filter(tagId -> {
                    String name = getTagName(tagId);
                    return tagId.toLowerCase().contains(searchTerm) ||
                            (name != null && name.toLowerCase().contains(searchTerm));
                })
                .collect(Collectors.toSet());
    }

    /**
     * 获取私有标签ID
     *
     * @return 私有标签ID集合
     */
    public Set<String> getPrivateTagIds() {
        return registry.getAllTagIds().stream()
                .filter(DicomTagConstants::isPrivateTag)
                .collect(Collectors.toSet());
    }

    /**
     * 获取序列相关标签ID
     *
     * @return 序列相关标签ID数组
     */
    public String[] getSeriesTagIds() {
        return DicomTagConstants.SERIES_TAG_IDS;
    }

    // ---------------------- 标签创建 ----------------------//

    /**
     * 创建DICOM标签
     *
     * @param tagId 标签ID
     * @param value 标签值
     * @return 创建的DICOM标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public DicomTag createTag(String tagId, String value) throws DicomException {
        if (tagId == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.TAG_INVALID,
                    "tagId", "null");
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        VR vr = getTagVR(normalizedId);
        String name = getTagName(normalizedId);

        return new DicomTag(normalizedId, name, value, vr, null, isPrivateTag(normalizedId));
    }

    /**
     * 获取标签的完整元数据信息
     *
     * @param tagId 标签ID
     * @return 完整的标签元数据信息字符串
     */
    public String getTagMetadataInfo(String tagId) {
        if (tagId == null) {
            return "Invalid Tag";
        }

        String normalizedId = DicomTagConstants.normalizeTagId(tagId);
        String name = getTagName(normalizedId);
        VR vr = getTagVR(normalizedId);
        DicomTagConstants.TagCategory category = getTagCategory(normalizedId);
        boolean isPrivate = isPrivateTag(normalizedId);
        boolean isGECT = isGECTTag(normalizedId);

        StringBuilder info = new StringBuilder();
        info.append("ID: ").append(normalizedId).append("\n");
        info.append("Name: ").append(name).append("\n");
        info.append("VR: ").append(vr).append("\n");
        info.append("Category: ").append(category).append("\n");
        info.append("Private: ").append(isPrivate ? "Yes" : "No").append("\n");
        info.append("GE CT: ").append(isGECT ? "Yes" : "No");

        return info.toString();
    }

    /**
     * 解析DICOM属性中的标签
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Map<String, String> parseTags(Attributes attrs) throws DicomException {
        if (attrs == null) {
            return Collections.emptyMap();
        }

        return parser.parseTags(attrs);
    }

    /**
     * 查找匹配指定条件的标签
     */
    public List<String> findTags(Predicate<String> predicate) {
        List<String> matchingTags = new ArrayList<>();

        for (String tagId : getAllTagIds()) {
            if (predicate.test(tagId)) {
                matchingTags.add(tagId);
            }
        }

        return matchingTags;
    }

    /**
     * 清除所有缓存
     */
    public void clearCache() {
        tagCache.clear();
    }

    /**
     * 标签创建适配器，用于连接解析器和工厂
     */
    private class TagCreatorAdapter implements DicomTagParser.TagCreator {
        @Override
        @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
        public DicomTag createTag(String tagId, String value) throws DicomException {
            try {
                return DicomTagService.this.createTag(tagId, value);
            } catch (DicomException e) {
                // 直接重新抛出DicomException
                throw e;
            } catch (Exception e) {
                throw ExceptionFactory.createDicomException(ErrorCode.TAG_PROCESSING,
                        DicomMessages.TAG_CREATE_FAILED, e, tagId);
            }
        }

        @Override
        public String normalizeTagId(String tagId) {
            return DicomTagConstants.normalizeTagId(tagId);
        }
    }

    /**
     * 标签元数据提供者，用于连接工厂和注册表
     */
    private class TagMetadataProvider implements DicomTagFactory.MetadataProvider {
        @Override
        public String getTagName(String tagId) {
            return registry.getTagName(tagId);
        }

        @Override
        public VR getTagVR(String tagId) {
            return registry.getTagVR(tagId);
        }

        @Override
        public boolean isPrivateTag(String tagId) {
            return DicomTagConstants.isPrivateTag(tagId);
        }

        @Override
        public String normalizeTagId(String tagId) {
            return DicomTagConstants.normalizeTagId(tagId);
        }
    }
}