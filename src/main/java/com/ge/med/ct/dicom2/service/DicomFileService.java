package com.ge.med.ct.dicom2.service;

import com.ge.med.ct.dcm_se.core.cfg.ConfigManager;
import com.ge.med.ct.dcm_se.core.cfg.dicom.DicomConfigService;
import com.ge.med.ct.dicom2.service.DicomValidationService.ValidationResult;
import com.ge.med.ct.dicom2.reader.DicomMetadataReader;
import com.ge.med.ct.dicom2.model.DicomFileModel;

import com.ge.med.ct.dicom2.core.ValidationErrorHandler;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.LogManager;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * DICOM文件服务
 * 负责DICOM文件的读取、验证、扫描和监控
 */
@HandleException(errorCode = ErrorCode.PROCESSING)
public class DicomFileService implements AutoCloseable {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomFileService.class);
    private static final int MAX_SCAN_DEPTH = 10;
    private static volatile DicomFileService instance;

    // 核心组件
    private final String rootDirectory;
    private final DicomMetadataReader metadataReader;
    private final DicomValidationService validationService;
    private final ExecutorService executor;
    private final ValidationErrorHandler errorHandler;

    // 配置参数
    private final boolean detailedWarnings;
    private final boolean groupWarnings;
    private final boolean skipInvalidFiles;
    private final boolean useParallelScan;

    // 监听器和监控相关
    private final List<Consumer<List<String>>> progressListeners = new ArrayList<>();
    private WatchService watchService;
    private boolean isMonitoring;

    private DicomFileService() {
        ConfigManager configManager = ConfigManager.getInstance();
        DicomConfigService dicomConfig = configManager.getDicomConfig();
        this.rootDirectory = dicomConfig.getRootDirectory();

        this.metadataReader = new DicomMetadataReader();
        this.validationService = DicomValidationService.getInstance();
        int maxThreads = configManager.getInt("dicom.max.threads", 4);
        this.executor = Executors.newFixedThreadPool(maxThreads, r -> {
            Thread thread = new Thread(r, "DicomFileService-Worker");
            thread.setDaemon(true);
            return thread;
        });

        // 读取配置
        this.detailedWarnings = configManager.getBoolean("dicom.validation.detailed_warnings", true);
        this.groupWarnings = configManager.getBoolean("dicom.validation.group_warnings", true);
        this.skipInvalidFiles = configManager.getBoolean("dicom.validation.skip_invalid", false);
        this.useParallelScan = configManager.getBoolean("dicom.scan.parallel", true);

        // 初始化错误处理器
        this.errorHandler = new ValidationErrorHandler(configManager, metadataReader);
        this.isMonitoring = false;

        LOG.info("DICOM文件服务初始化完成: 根目录=" + rootDirectory);
    }

    /**
     * 获取单例实例
     */
    public static DicomFileService getInstance() {
        if (instance == null) {
            synchronized (DicomFileService.class) {
                if (instance == null) {
                    instance = new DicomFileService();
                }
            }
        }
        return instance;
    }

    /**
     * 获取根目录
     */
    public String getRootDirectory() {
        return rootDirectory;
    }

    /**
     * 读取DICOM文件
     *
     * @param filePath DICOM文件路径
     * @return DICOM文件模型
     * @throws DicomException 如果读取失败
     */
    public DicomFileModel readFile(String filePath) throws DicomException {
        validateFilePath(filePath);

        try {
            // 直接调用readMetadata方法，不使用retry逻辑
            return metadataReader.readMetadata(filePath);
        } catch (Exception e) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e, filePath);
        }
    }

    /**
     * 验证DICOM文件
     *
     * @param filePath DICOM文件路径
     * @return DICOM文件模型
     * @throws DicomException 如果验证失败
     */
    public DicomFileModel validateFile(String filePath) throws DicomException {
        // 验证文件路径
        validateFilePath(filePath);

        // 对所有文件一视同仕，不做特殊处理

        // 读取文件
        DicomFileModel model = readFile(filePath);
        if (model == null) {
            String fileName = new File(filePath).getName();
            errorHandler.recordError(filePath, "READ_ERROR", "无法读取文件");
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, fileName);
        }

        try {
            // 使用验证服务验证文件
            ValidationResult result = validationService.validateFile(filePath, model);
            if (!result.isValid()) {
                String errorKey = result.getErrorMessage();
                String fileName = new File(filePath).getName();

                // 根据错误类型记录不同级别的警告
                if ("EXAM_EMPTY".equals(errorKey)) {
                    errorHandler.recordWarning(filePath, "EXAM_EMPTY", "缺少检查信息 (StudyInstanceUID)");
                    // 如果配置为跳过无效文件，则抛出异常
                    if (skipInvalidFiles) {
                        throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, "缺少检查信息",
                                fileName);
                    }
                } else if ("SERIES_EMPTY".equals(errorKey)) {
                    errorHandler.recordWarning(filePath, "SERIES_EMPTY", "缺少序列信息 (SeriesInstanceUID)");
                    // 如果配置为跳过无效文件，则抛出异常
                    if (skipInvalidFiles) {
                        throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, "缺少序列信息",
                                fileName);
                    }
                } else {
                    errorHandler.recordError(filePath, errorKey, result.getErrorMessage());
                    // 对于其他错误，始终抛出异常
                    throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR,
                            result.getErrorMessage(), fileName);
                }
            }
        } catch (DicomException e) {
            // 直接抛出 DicomException
            throw e;
        } catch (Exception e) {
            // 其他异常包装为 DicomException
            String fileName = new File(filePath).getName();
            errorHandler.recordError(filePath, "VALIDATION_EXCEPTION", e.getMessage());
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_FAILED, e, fileName);
        }

        return model;
    }

    /**
     * 验证文件路径
     *
     * @param filePath 文件路径
     * @throws DicomException 如果文件路径无效
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    private void validateFilePath(String filePath) throws DicomException {
        if (filePath == null || filePath.isEmpty()) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_NOT_FOUND, "文件路径不能为空");
        }

        File file = new File(filePath);
        if (!file.exists() || !file.isFile() || !file.canRead()) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_NOT_FOUND, filePath);
        }

        // 检查文件是否为DICOM格式
        if (!DicomValidationService.isDicomFile(file)) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.FILE_INVALID, "不是有效的DICOM文件", filePath);
        }
    }

    /**
     * 扫描目录，返回DICOM文件路径
     *
     * @param directory 目录路径
     * @param recursive 是否递归扫描
     * @return DICOM文件路径列表
     * @throws DicomException 如果扫描失败
     */
    public List<String> scanDirectory(String directory, boolean recursive) throws DicomException {
        String scanPath = directory != null ? directory : rootDirectory;
        LOG.info("开始扫描目录: " + scanPath + (recursive ? " 递归" : ""));

        try {
            List<String> files = findDicomFiles(scanPath, recursive, MAX_SCAN_DEPTH);
            LOG.info("扫描完成: 发现 " + files.size() + " 个文件");

            // 通知监听器
            notifyProgressListeners(files);

            return files;
        } catch (IOException e) {
            throw new DicomException(ErrorCode.PROCESSING, DicomMessages.SCAN_FAILED, e, e.getMessage());
        }
    }

    /**
     * 查找DICOM文件
     * 扫描指定目录，返回DICOM文件路径列表
     *
     * @param directory 目录路径
     * @param recursive 是否递归扫描
     * @param maxDepth  最大扫描深度
     * @return DICOM文件路径列表
     * @throws IOException 如果扫描失败
     */
    public static List<String> findDicomFiles(String directory, boolean recursive, int maxDepth) throws IOException {
        if (directory == null || directory.isEmpty()) {
            return new ArrayList<>();
        }

        Path dirPath = Paths.get(directory);
        if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
            LOG.warning("目录不存在或不是目录: " + directory);
            return new ArrayList<>();
        }

        int depth = recursive ? maxDepth : 1;

        try (Stream<Path> pathStream = Files.walk(dirPath, depth)) {
            return pathStream
                    .filter(Files::isRegularFile)
                    .filter(path -> DicomValidationService.isDicomFile(path))
                    .map(Path::toString)
                    .collect(Collectors.toList());
        } catch (IOException e) {
            LOG.warning("扫描目录时出错: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 预扫描目录，按Exam和Series分组
     *
     * @param directory 目录路径
     * @param recursive 是否递归扫描
     * @return 预扫描结果
     * @throws DicomException 如果扫描失败
     */
    public PreScanResult preScanDirectory(String directory, boolean recursive) throws DicomException {
        // 验证目录
        if (directory == null || directory.isEmpty()) {
            throw new DicomException(ErrorCode.PROCESSING, DicomMessages.SCAN_FAILED, "目录路径不能为空");
        }

        // 扫描目录，获取所有DICOM文件
        List<String> files = scanDirectory(directory, recursive);
        if (files.isEmpty()) {
            LOG.warning("目录中没有找到DICOM文件: " + directory);
            return new PreScanResult(
                    new HashMap<>(),
                    new HashMap<>(),
                    new ArrayList<>(),
                    new ArrayList<>());
        }

        // 预扫描结果
        Map<String, List<String>> examToFilesMap = new ConcurrentHashMap<>();
        Map<String, Map<String, List<String>>> examToSeriesMap = new ConcurrentHashMap<>();
        List<String> invalidFiles = Collections.synchronizedList(new ArrayList<>());
        List<String> emptyExamFiles = Collections.synchronizedList(new ArrayList<>());

        LOG.info("开始预扫描 " + files.size() + " 个DICOM文件...");

        // 根据配置决定是否使用并行流
        Stream<String> fileStream = useParallelScan ? files.parallelStream() : files.stream();

        // 使用流进行预扫描
        fileStream.forEach(filePath -> {
            try {
                // 对所有文件一视同仕，不做特殊处理

                // 读取关键标签
                Map<String, String> keyTags = metadataReader.readKeyTags(filePath);

                // 提取检查和序列ID
                String examId = keyTags.getOrDefault("StudyInstanceUID", "");
                String seriesId = keyTags.getOrDefault("SeriesInstanceUID", "");

                // 处理缺少检查信息的文件
                if (examId.isEmpty()) {
                    emptyExamFiles.add(filePath);
                    return;
                }

                // 按Exam分组
                examToFilesMap.computeIfAbsent(examId, k -> Collections.synchronizedList(new ArrayList<>()))
                        .add(filePath);

                // 按Series分组
                examToSeriesMap.computeIfAbsent(examId, k -> new ConcurrentHashMap<>())
                        .computeIfAbsent(seriesId, k -> Collections.synchronizedList(new ArrayList<>()))
                        .add(filePath);
            } catch (Exception e) {
                // 获取详细的错误信息
                String errorMessage = e.getMessage();
                if (errorMessage == null) {
                    if (e.getCause() != null && e.getCause().getMessage() != null) {
                        errorMessage = e.getCause().getMessage();
                    } else {
                        errorMessage = "未知错误: " + e.getClass().getSimpleName();
                    }
                }

                LOG.warning("预扫描文件失败: " + filePath + ", 原因: " + errorMessage);
                invalidFiles.add(filePath);
                errorHandler.recordError(filePath, "PRESCAN_ERROR", errorMessage);
            }
        });

        // 记录详细的警告信息
        if (detailedWarnings) {
            logEmptyExamFiles(emptyExamFiles);
            logInvalidFiles(invalidFiles);
        }

        // 生成验证错误汇总报告
        if (groupWarnings) {
            errorHandler.generateSummaryReport();
        }

        // 记录预扫描结果
        int examCount = examToFilesMap.size();
        int seriesCount = countSeries(examToSeriesMap);
        int emptyCount = emptyExamFiles.size();
        int invalidCount = invalidFiles.size();

        LOG.info(String.format("预扫描完成: 发现 %d 个检查, %d 个序列, %d 个缺少检查信息的文件, %d 个无效文件",
                examCount, seriesCount, emptyCount, invalidCount));

        return new PreScanResult(examToFilesMap, examToSeriesMap, emptyExamFiles, invalidFiles);
    }

    /**
     * 记录缺少检查信息的文件
     */
    private void logEmptyExamFiles(List<String> emptyExamFiles) {
        if (emptyExamFiles.isEmpty()) {
            return;
        }

        LOG.warning("发现 " + emptyExamFiles.size() + " 个文件缺少检查信息 (StudyInstanceUID)");

        // 只记录前5个文件名
        emptyExamFiles.stream()
                .limit(5)
                .map(path -> new File(path).getName())
                .forEach(name -> LOG.warning("缺少检查信息的文件: " + name));

        if (emptyExamFiles.size() > 5) {
            LOG.warning("及其他 " + (emptyExamFiles.size() - 5) + " 个文件");
        }
    }

    /**
     * 记录无效文件
     */
    private void logInvalidFiles(List<String> invalidFiles) {
        if (invalidFiles.isEmpty()) {
            return;
        }

        LOG.warning("发现 " + invalidFiles.size() + " 个无效的DICOM文件");

        // 只记录前5个文件名作为示例
        invalidFiles.stream()
                .limit(5)
                .forEach(path -> LOG.warning("无效文件: " + new File(path).getName()));

        if (invalidFiles.size() > 5) {
            LOG.warning("及其他 " + (invalidFiles.size() - 5) + " 个文件");
        }
    }

    /**
     * 计算序列总数
     */
    private int countSeries(Map<String, Map<String, List<String>>> examToSeriesMap) {
        return examToSeriesMap.values().stream()
                .mapToInt(Map::size)
                .sum();
    }

    /**
     * 启动文件监控
     */
    public void startMonitoring() {
        if (isMonitoring) {
            LOG.info("文件监控已经在运行中");
            return;
        }

        try {
            this.watchService = FileSystems.getDefault().newWatchService();
            Path path = Paths.get(rootDirectory);
            path.register(watchService, StandardWatchEventKinds.ENTRY_CREATE);

            isMonitoring = true;

            // 启动监控线程
            CompletableFuture.runAsync(this::monitorFiles, executor);

            LOG.info("开始监控目录: " + rootDirectory);
        } catch (IOException e) {
            LOG.warning("启动文件监控失败: " + e.getMessage());
        }
    }

    /**
     * 停止文件监控
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }

        isMonitoring = false;

        try {
            if (watchService != null) {
                watchService.close();
                watchService = null;
            }
            LOG.info("停止监控目录: " + rootDirectory);
        } catch (IOException e) {
            LOG.warning("停止文件监控失败: " + e.getMessage());
        }
    }

    /**
     * 监控文件变化
     */
    private void monitorFiles() {
        int retryCount = 0;
        final int MAX_RETRIES = 3;
        final long RETRY_DELAY = 5000; // 5秒

        while (isMonitoring) {
            try {
                if (watchService == null) {
                    // 重新初始化 WatchService
                    this.watchService = FileSystems.getDefault().newWatchService();
                    Path path = Paths.get(rootDirectory);
                    path.register(watchService, StandardWatchEventKinds.ENTRY_CREATE);
                    retryCount = 0; // 重置重试计数
                    LOG.info("重新初始化文件监控服务");
                }

                WatchKey key = watchService.poll(1, TimeUnit.SECONDS);
                if (key == null) {
                    continue;
                }

                List<String> newFiles = new ArrayList<>();

                for (WatchEvent<?> event : key.pollEvents()) {
                    if (event.kind() == StandardWatchEventKinds.ENTRY_CREATE) {
                        Path newPath = ((Path) key.watchable()).resolve((Path) event.context());
                        File file = newPath.toFile();

                        if (file.isFile() && DicomValidationService.isDicomFile(file)) {
                            newFiles.add(file.getAbsolutePath());
                        }
                    }
                }

                if (!newFiles.isEmpty()) {
                    LOG.info("检测到 " + newFiles.size() + " 个新文件");
                    notifyProgressListeners(newFiles);
                }

                if (!key.reset()) {
                    LOG.warning("监控键已失效，准备重新初始化监控服务");
                    closeWatchService();
                    continue;
                }

            } catch (ClosedWatchServiceException e) {
                LOG.warning("监控服务已关闭，尝试重新初始化");
                closeWatchService();

                if (retryCount < MAX_RETRIES) {
                    retryCount++;
                    try {
                        Thread.sleep(RETRY_DELAY);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    LOG.severe("重试次数超过限制，停止监控");
                    isMonitoring = false;
                    break;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                String errorMessage = e.getMessage();
                if (errorMessage == null) {
                    errorMessage = "未知异常: " + e.getClass().getSimpleName();
                }
                LOG.warning("文件监控异常: " + errorMessage);

                if (LOG.isLoggable(java.util.logging.Level.FINE)) {
                    StringWriter sw = new StringWriter();
                    e.printStackTrace(new PrintWriter(sw));
                    LOG.fine("文件监控异常堆栈: \n" + sw.toString());
                }

                // 对于其他异常，也尝试重新初始化
                closeWatchService();
            }
        }
    }

    private void closeWatchService() {
        if (watchService != null) {
            try {
                watchService.close();
            } catch (IOException e) {
                LOG.warning("关闭监控服务失败: " + e.getMessage());
            } finally {
                watchService = null;
            }
        }
    }

    /**
     * 添加进度监听器
     */
    public void addProgressListener(Consumer<List<String>> listener) {
        if (listener != null) {
            progressListeners.add(listener);
        }
    }

    /**
     * 移除进度监听器
     */
    public void removeProgressListener(Consumer<List<String>> listener) {
        progressListeners.remove(listener);
    }

    /**
     * 通知所有进度监听器
     */
    private void notifyProgressListeners(List<String> files) {
        for (Consumer<List<String>> listener : progressListeners) {
            try {
                listener.accept(files);
            } catch (Exception e) {
                LOG.warning("通知监听器失败: " + e.getMessage());
            }
        }
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        stopMonitoring();
        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 预扫描结果类
     */
    public static class PreScanResult {
        private final Map<String, List<String>> examToFilesMap;
        private final Map<String, Map<String, List<String>>> examToSeriesMap;
        private final List<String> emptyExamFiles;
        private final List<String> invalidFiles;

        public PreScanResult(
                Map<String, List<String>> examToFilesMap,
                Map<String, Map<String, List<String>>> examToSeriesMap,
                List<String> emptyExamFiles,
                List<String> invalidFiles) {
            this.examToFilesMap = examToFilesMap;
            this.examToSeriesMap = examToSeriesMap;
            this.emptyExamFiles = emptyExamFiles;
            this.invalidFiles = invalidFiles;
        }

        public Map<String, List<String>> getExamToFilesMap() {
            return examToFilesMap;
        }

        public Map<String, Map<String, List<String>>> getExamToSeriesMap() {
            return examToSeriesMap;
        }

        public List<String> getEmptyExamFiles() {
            return emptyExamFiles;
        }

        public List<String> getInvalidFiles() {
            return invalidFiles;
        }

        public int getExamCount() {
            return examToFilesMap.size();
        }

        public int getSeriesCount() {
            return examToSeriesMap.values().stream()
                    .mapToInt(Map::size)
                    .sum();
        }

        public int getTotalFileCount() {
            return examToFilesMap.values().stream()
                    .mapToInt(List::size)
                    .sum() + emptyExamFiles.size() + invalidFiles.size();
        }

        public int getValidFileCount() {
            return examToFilesMap.values().stream()
                    .mapToInt(List::size)
                    .sum();
        }
    }
}
