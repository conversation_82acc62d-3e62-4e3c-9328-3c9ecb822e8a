package com.ge.med.ct.dcm.core.cfg.dicom;

import com.ge.med.ct.dcm.core.cfg.AppDefaults;
import com.ge.med.ct.dcm.core.cfg.ConfigManager;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.ConfigMessages;
import com.ge.med.ct.service.PlatformUtils;

import java.io.File;
import java.util.logging.Logger;

/**
 * DICOM配置服务
 * 负责管理DICOM相关配置
 */
public class DicomConfigService {
    private static final Logger LOG = Logger.getLogger(DicomConfigService.class.getName());

    // 配置键常�?    private static final String KEY_ROOT_DIR_WIN = "dicom.scan.directory.win";
    private static final String KEY_ROOT_DIR_LINUX = "dicom.scan.directory.linux";
    private static final String KEY_CACHE_ENABLED = "dicom.cache.enabled";
    private static final String KEY_CACHE_SIZE = "dicom.cache.size";
    private static final String KEY_CHARSET = "dicom.charset";
    private static final String KEY_DETAILED_WARNINGS = "dicom.validation.detailed_warnings";
    private static final String KEY_SKIP_INVALID = "dicom.validation.skip_invalid";
    private static final String KEY_MAX_THREADS = "dicom.max.threads";
    private static final String KEY_GROUP_WARNINGS = "dicom.validation.group_warnings";
    private static final String KEY_MAX_EXAMPLES = "dicom.validation.max_examples";
    private static final String KEY_SCAN_PARALLEL = "dicom.scan.parallel";

    // 依赖的配置管理器
    private final ConfigManager configManager;

    /**
     * 构造函�?     * 
     * @param configManager 配置管理�?     */
    public DicomConfigService(ConfigManager configManager) {
        this.configManager = configManager;
    }

    /**
     * 获取DICOM根目�?     */
    public String getRootDirectory() {
        return PlatformUtils.getPlatformSpecificValue(
            configManager.getString(KEY_ROOT_DIR_WIN, AppDefaults.DEFAULT_DICOM_ROOT_DIR),
            configManager.getString(KEY_ROOT_DIR_LINUX, AppDefaults.DEFAULT_DICOM_ROOT_DIR)
        );
    }

    /**
     * 获取缓存是否启用
     */
    public boolean isCacheEnabled() {
        return configManager.getBoolean(KEY_CACHE_ENABLED, AppDefaults.DEFAULT_DICOM_CACHE_ENABLED);
    }

    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return configManager.getInt(KEY_CACHE_SIZE, AppDefaults.DEFAULT_DICOM_CACHE_SIZE);
    }

    /**
     * 获取DICOM字符�?     */
    public String getCharset() {
        return configManager.getString(KEY_CHARSET, AppDefaults.DEFAULT_DICOM_CHARSET);
    }

    /**
     * 获取标签名称
     */
    public String getTagName(String tagId) {
        return DicomTagConstants.getTagName(tagId);
    }

    /**
     * 检查标签是否有�?     */
    public boolean isValidTag(String tagId) {
        return DicomTagConstants.isValidTag(tagId);
    }

    /**
     * 根据标签名称获取标签ID
     */
    public String getTagIdByName(String tagName) {
        return DicomTagConstants.getTagIdByName(tagName);
    }

    /**
     * 检查标签名是否存在
     */
    public boolean containsTagName(String tagName) {
        return DicomTagConstants.containsTagName(tagName);
    }

    /**
     * 验证配置
     * 
     * @throws QAToolException 如果配置无效
     */
    @HandleException(errorCode = ErrorCode.CONFIG)
    public void validate() throws QAToolException {
        validateRootDirectory();
        validateCacheSize();
    }

    private void validateRootDirectory() throws QAToolException {
        String rootDir = getRootDirectory();
        if (rootDir == null || rootDir.trim().isEmpty()) {
            throw ExceptionFactory.createConfigValidationException(
                    ErrorCode.CONFIG,
                    ConfigMessages.CONFIG_INVALID.format("DICOM根目�?));
        }

        File rootDirFile = new File(rootDir);
        if (!rootDirFile.exists()) {
            LOG.warning("DICOM根目录不存在: " + rootDir);
        } else if (!rootDirFile.isDirectory()) {
            throw ExceptionFactory.createConfigValidationException(
                    ErrorCode.CONFIG,
                    ConfigMessages.CONFIG_INVALID.format("DICOM根目�?),
                    rootDir);
        }
    }

    private void validateCacheSize() throws QAToolException {
        int cacheSize = getCacheSize();
        if (cacheSize < 0) {
            throw ExceptionFactory.createConfigValidationException(
                    ErrorCode.CONFIG,
                    ConfigMessages.CONFIG_INVALID.format("缓存大小"),
                    String.valueOf(cacheSize));
        }
    }

    /**
     * 重新加载配置
     * 目前没有需要特别重新加载的内容，保留方法以保持接口一致�?     */
    public void reload() {
        LOG.info("DICOM配置已重新加�?);
        try {
            validate();
        } catch (QAToolException e) {
            LOG.warning("DICOM配置重新加载验证失败: " + e.getMessage());
        }
    }

    // 验证配置
    public boolean isDetailedWarningsEnabled() {
        return configManager.getBoolean(KEY_DETAILED_WARNINGS, AppDefaults.DEFAULT_DETAILED_WARNINGS);
    }

    public boolean shouldSkipInvalidFiles() {
        return configManager.getBoolean(KEY_SKIP_INVALID, AppDefaults.DEFAULT_SKIP_INVALID);
    }
    
    public boolean shouldGroupWarnings() {
        return configManager.getBoolean(KEY_GROUP_WARNINGS, AppDefaults.DEFAULT_GROUP_WARNINGS);
    }
    
    public int getMaxExamples() {
        return configManager.getInt(KEY_MAX_EXAMPLES, AppDefaults.DEFAULT_MAX_EXAMPLES);
    }
    
    public boolean isParallelScanEnabled() {
        return configManager.getBoolean(KEY_SCAN_PARALLEL, AppDefaults.DEFAULT_SCAN_PARALLEL);
    }

    // 扫描配置
    public int getMaxThreads() {
        return configManager.getInt(KEY_MAX_THREADS, AppDefaults.DEFAULT_MAX_THREADS);
    }
}
