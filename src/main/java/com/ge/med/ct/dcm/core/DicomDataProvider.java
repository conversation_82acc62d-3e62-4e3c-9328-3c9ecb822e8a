package com.ge.med.ct.dcm.core;

import com.ge.med.ct.dcm.model.DicomExam;
import com.ge.med.ct.dcm.model.DicomSeries;
import com.ge.med.ct.dcm.model.DicomImage;
import com.ge.med.ct.dcm.model.DicomFileModel;
import com.ge.med.ct.exception.core.DicomException;

import java.util.List;
import java.util.Map;

/**
 * DICOM数据提供者统一接口
 * 支持文件系统和PESI两种数据源的统一访问
 */
public interface DicomDataProvider {

    // === 检�?Exam)相关操作 ===

    /**
     * 获取所有检查列�?     */
    List<DicomExam> getAllExams();

    /**
     * 获取指定检�?     * @param examId 检查ID
     * @return 检查对象，如果不存在返回null
     */
    DicomExam getExam(String examId);

    /**
     * 搜索检�?     * @param criteria 搜索条件
     * @return 匹配的检查列�?     */
    List<DicomExam> searchExams(DicomSearchCriteria criteria);

    // === 序列(Series)相关操作 ===

    /**
     * 获取指定序列
     * @param seriesId 序列ID
     * @return 序列对象，如果不存在返回null
     */
    DicomSeries getSeries(String seriesId);

    /**
     * 获取检查下的所有序�?     * @param examId 检查ID
     * @return 序列列表
     */
    List<DicomSeries> getSeriesForExam(String examId);

    // === 图像(Image)相关操作 ===

    /**
     * 获取指定图像
     * @param imageId 图像ID
     * @return 图像对象，如果不存在返回null
     */
    DicomImage getImage(String imageId);

    /**
     * 获取序列下的所有图�?     * @param seriesId 序列ID
     * @return 图像列表
     */
    List<DicomImage> getImagesForSeries(String seriesId);

    // === 文件路径操作 (关键功能) ===

    /**
     * 获取图像文件的物理路�?     * - 文件系统模式: 返回实际文件路径
     * - PESI模式: 返回PESI路径
     * @param imageId 图像ID
     * @return 文件的物理路�?     */
    String getImageFilePath(String imageId);

    /**
     * 批量获取图像文件路径
     * @param imageIds 图像ID列表
     * @return 图像ID到路径的映射
     */
    Map<String, String> getImageFilePaths(List<String> imageIds);

    /**
     * 根据PESI路径获取图像信息 (仅PESI模式)
     * @param pesiPath PESI路径
     * @return 图像对象，如果不存在返回null
     */
    DicomImage getImageByPesiPath(String pesiPath);

    // === 元数据操�?===

    /**
     * 获取图像的DICOM标签�?     * @param imageId 图像ID
     * @param tagId 标签ID
     * @return 标签值，如果不存在返回null
     */
    String getTagValue(String imageId, String tagId);

    /**
     * 获取图像的所有元数据
     * @param imageId 图像ID
     * @return 标签ID到值的映射
     */
    Map<String, String> getImageMetadata(String imageId);

    // === 文件模型操作 ===

    /**
     * 添加DICOM文件模型
     * @param model 文件模型
     * @throws DicomException 如果添加过程中发生错�?     */
    void addFileModel(DicomFileModel model) throws DicomException;

    /**
     * 获取所有DICOM文件模型
     * @return 文件模型列表
     */
    List<DicomFileModel> getAllFileModels();

    // === 系统操作 ===

    /**
     * 获取当前数据源类�?     */
    DataSourceType getDataSourceType();

    /**
     * 获取数据统计信息
     */
    DicomDataStats getDataStats();

    /**
     * 清除所有数�?     * @throws DicomException 如果清除过程中发生错�?     */
    void clearData() throws DicomException;

    /**
     * 关闭资源
     */
    void shutdown();
}
