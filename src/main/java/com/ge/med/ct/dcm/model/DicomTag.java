package com.ge.med.ct.dcm.model;

import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

/**
 * DICOM标签�?(简化版)
 * 表示DICOM文件中的标签及其�? * 设计为不可变类，避免意外状态修�? */
public final class DicomTag {
    
    private final String tagId;      // 标签16进制ID
    private final String name;       // 标签名称
    private final String value;      // 标签�?    private final VR vr;            // 值表示类�?    private final String description; // 标签描述
    private final boolean isPrivate; // 是否为私有标�?    
    /**
     * 完整构造函�?     */
    public DicomTag(String tagId, String name, String value, VR vr, String description, boolean isPrivate) 
            throws DicomException {
        if (tagId == null || tagId.trim().isEmpty()) {
            throw new DicomException("标签ID不能为空");
        }
        
        this.tagId = normalizeTagId(tagId);
        this.name = (name != null && !name.isEmpty()) ? name : "Tag-" + this.tagId;
        this.value = (value != null) ? value : "";
        this.vr = vr != null ? vr : VR.UN;
        this.description = (description != null) ? description : "DICOM标签 " + this.name;
        this.isPrivate = isPrivate;
    }
    
    /**
     * 简化构造函�?     */
    public DicomTag(String tagId, String value, VR vr) throws DicomException {
        this(tagId, "Tag-" + tagId, value, vr, null, false);
    }
    
    /**
     * 创建带有新值的标签副本
     */
    public DicomTag withValue(String newValue) throws DicomException {
        return new DicomTag(this.tagId, this.name, newValue, this.vr, this.description, this.isPrivate);
    }
    
    // === Getter方法 ===
    
    public String getTagId() {
        return tagId;
    }
    
    public String getName() {
        return name;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getValueAsString() {
        return value;
    }
    
    public VR getVr() {
        return vr;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isPrivate() {
        return isPrivate;
    }
    
    // === 值类型转换方�?===
    
    public Integer getValueAsInteger() {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    public Float getValueAsFloat() {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Float.parseFloat(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    public Double getValueAsDouble() {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    // === 工具方法 ===
    
    /**
     * 标准化标签ID格式
     */
    private static String normalizeTagId(String tagId) {
        if (tagId == null) {
            return null;
        }
        
        String normalized = tagId.trim().toUpperCase();
        
        // 如果不是标准格式，尝试转�?        if (!normalized.matches("\\([0-9A-F]{4},[0-9A-F]{4}\\)")) {
            // 移除所有非十六进制字符
            String hex = normalized.replaceAll("[^0-9A-F]", "");
            if (hex.length() == 8) {
                normalized = "(" + hex.substring(0, 4) + "," + hex.substring(4, 8) + ")";
            }
        }
        
        return normalized;
    }
    
    @Override
    public String toString() {
        return String.format("%s[%s]=\"%s\"", name, tagId, value);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DicomTag other = (DicomTag) obj;
        return tagId.equals(other.tagId) && 
               ((value == null && other.value == null) || 
                (value != null && value.equals(other.value)));
    }
    
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((tagId == null) ? 0 : tagId.hashCode());
        result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }
}
