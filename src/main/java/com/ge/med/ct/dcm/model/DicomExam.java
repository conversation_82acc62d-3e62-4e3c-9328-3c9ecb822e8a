package com.ge.med.ct.dcm.model;

import com.ge.med.ct.exception.core.DicomException;

import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM检查类 (简化版)
 * 管理DICOM检查属性和序列
 */
public class DicomExam {
    private static final Logger LOG = Logger.getLogger(DicomExam.class.getName());
    
    private final String id;
    private final Map<String, DicomTag> tags;
    private final List<DicomSeries> series;
    
    // 常用属性缓�?    private String patientID;
    private String patientName;
    private String studyDate;
    private String studyTime;
    private String studyInstanceUID;
    
    public DicomExam(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException("检查ID不能为空");
        }
        this.id = id;
        this.tags = new HashMap<>();
        this.series = new ArrayList<>();
    }
    
    // === 基本属性访�?===
    
    public String getId() {
        return id;
    }
    
    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }
    
    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            updateCachedAttributes(tagId, tag.getValueAsString());
            LOG.fine("添加标签 " + tagId + " 到检�?" + id);
        }
    }
    
    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }
    
    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }
    
    // === 患者信�?===
    
    public String getPatientID() {
        return patientID != null ? patientID : getTagValue("(0010,0020)");
    }
    
    public void setPatientID(String patientID) {
        this.patientID = patientID;
        setTagValue("(0010,0020)", patientID);
    }
    
    public String getPatientName() {
        return patientName != null ? patientName : getTagValue("(0010,0010)");
    }
    
    public void setPatientName(String patientName) {
        this.patientName = patientName;
        setTagValue("(0010,0010)", patientName);
    }
    
    // === 检查信�?===
    
    public String getStudyInstanceUID() {
        return studyInstanceUID != null ? studyInstanceUID : getTagValue("(0020,000D)");
    }
    
    public void setStudyInstanceUID(String studyInstanceUID) {
        this.studyInstanceUID = studyInstanceUID;
        setTagValue("(0020,000D)", studyInstanceUID);
    }
    
    public String getStudyDate() {
        return studyDate != null ? studyDate : getTagValue("(0008,0020)");
    }
    
    public void setStudyDate(String studyDate) {
        this.studyDate = studyDate;
        setTagValue("(0008,0020)", studyDate);
    }
    
    public String getStudyTime() {
        return studyTime != null ? studyTime : getTagValue("(0008,0030)");
    }
    
    public void setStudyTime(String studyTime) {
        this.studyTime = studyTime;
        setTagValue("(0008,0030)", studyTime);
    }
    
    public String getStudyDescription() {
        return getTagValue("(0008,1030)");
    }
    
    public void setStudyDescription(String description) {
        setTagValue("(0008,1030)", description);
    }
    
    // === 序列管理 ===
    
    public List<DicomSeries> getSeries() {
        return Collections.unmodifiableList(series);
    }
    
    public void addSeries(DicomSeries series) {
        if (series != null && !this.series.contains(series)) {
            this.series.add(series);
            series.setExam(this);
            LOG.fine("添加序列 " + series.getId() + " 到检�?" + id);
        }
    }
    
    public void removeSeries(DicomSeries series) {
        if (series != null && this.series.contains(series)) {
            this.series.remove(series);
            if (series.getExam() == this) {
                series.setExam(null);
            }
            LOG.fine("从检�?" + id + " 移除序列 " + series.getId());
        }
    }
    
    public int getSeriesCount() {
        return series.size();
    }
    
    // === 工具方法 ===
    
    private void setTagValue(String tagId, String value) {
        if (value != null) {
            try {
                DicomTag tag = new DicomTag(tagId, value, org.dcm4che3.data.VR.LO);
                addTag(tagId, tag);
            } catch (DicomException e) {
                LOG.warning("设置标签值失�? " + tagId + " = " + value);
            }
        }
    }
    
    private void updateCachedAttributes(String tagId, String value) {
        switch (tagId) {
            case "(0010,0020)":
                this.patientID = value;
                break;
            case "(0010,0010)":
                this.patientName = value;
                break;
            case "(0020,000D)":
                this.studyInstanceUID = value;
                break;
            case "(0008,0020)":
                this.studyDate = value;
                break;
            case "(0008,0030)":
                this.studyTime = value;
                break;
        }
    }
    
    @Override
    public String toString() {
        return String.format("DicomExam[id=%s, studyInstanceUID=%s, patientID=%s, patientName=%s, studyDate=%s, seriesCount=%d]",
                id, getStudyInstanceUID(), getPatientID(), getPatientName(), getStudyDate(), series.size());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DicomExam other = (DicomExam) obj;
        return id.equals(other.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
