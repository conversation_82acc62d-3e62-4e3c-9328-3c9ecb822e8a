package com.ge.med.ct.dcm.table;

import com.ge.med.ct.dcm.model.DicomExam;
import com.ge.med.ct.dcm.model.DicomSeries;
import com.ge.med.ct.dcm.model.DicomImage;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * 表格数据引擎 (简化版)
 * 负责将DICOM数据转换为表格显示格�? */
public class TableDataEngine {
    private static final Logger LOG = Logger.getLogger(TableDataEngine.class.getName());
    
    private final Map<Class<?>, DataConverter<?>> dataConverters;
    private final TableColumnConfig columnConfig;
    
    public TableDataEngine() {
        this.columnConfig = new TableColumnConfig();
        this.dataConverters = initDataConverters();
    }
    
    /**
     * 初始化数据转换器
     */
    private Map<Class<?>, DataConverter<?>> initDataConverters() {
        Map<Class<?>, DataConverter<?>> converters = new ConcurrentHashMap<>();
        
        TagFormatter formatter = new TagFormatter();
        
        converters.put(DicomExam.class, new ExamConverter(formatter, columnConfig));
        converters.put(DicomSeries.class, new SeriesConverter(formatter, columnConfig));
        converters.put(DicomImage.class, new ImageConverter(formatter, columnConfig));
        
        return converters;
    }
    
    /**
     * 转换检查数据为表格数据
     */
    public Vector<Vector<String>> convertExamData(List<DicomExam> exams) {
        return convertData(exams, TableType.EXAM);
    }
    
    /**
     * 转换序列数据为表格数�?     */
    public Vector<Vector<String>> convertSeriesData(List<DicomSeries> seriesList) {
        return convertData(seriesList, TableType.SERIES);
    }
    
    /**
     * 转换图像数据为表格数�?     */
    public Vector<Vector<String>> convertImageData(List<DicomImage> images) {
        return convertData(images, TableType.IMAGE);
    }
    
    /**
     * 通用数据转换方法
     */
    @SuppressWarnings("unchecked")
    public <T> Vector<Vector<String>> convertData(List<T> dataList, TableType tableType) {
        if (dataList == null || dataList.isEmpty()) {
            return new Vector<>();
        }
        
        Vector<Vector<String>> data = new Vector<>();
        String[] columns = getTableColumnNames(tableType);
        
        // 获取数据类型
        Class<?> dataClass = dataList.get(0).getClass();
        
        // 获取对应的转换器
        DataConverter<?> rawConverter = dataConverters.get(dataClass);
        if (rawConverter == null) {
            LOG.warning("未找到数据类型的转换�? " + dataClass.getSimpleName());
            return data;
        }
        
        DataConverter<T> converter = (DataConverter<T>) rawConverter;
        
        // 转换数据
        for (T item : dataList) {
            Vector<String> row = new Vector<>();
            for (String column : columns) {
                try {
                    String value = converter.getColumnValue(item, column, tableType);
                    row.add(value != null ? value : "");
                } catch (Exception e) {
                    LOG.warning(String.format("获取列值失�? %s - %s", column, e.getMessage()));
                    row.add("");
                }
            }
            data.add(row);
        }
        
        LOG.info(String.format("转换%s数据完成: %d�?x %d�?, 
                tableType, data.size(), columns.length));
        
        return data;
    }
    
    /**
     * 获取表格列名
     */
    public String[] getTableColumnNames(TableType tableType) {
        List<String> columns = columnConfig.getColumnNames(tableType);
        return columns.toArray(new String[0]);
    }
    
    /**
     * 获取表格列显示名�?     */
    public String[] getTableColumnDisplayNames(TableType tableType) {
        List<String> displayNames = columnConfig.getColumnDisplayNames(tableType);
        return displayNames.toArray(new String[0]);
    }
    
    /**
     * 表格类型枚举
     */
    public enum TableType {
        EXAM("exam", "检�?),
        SERIES("series", "序列"),
        IMAGE("image", "图像");
        
        private final String code;
        private final String displayName;
        
        TableType(String code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    /**
     * 数据转换器接�?     */
    public interface DataConverter<T> {
        /**
         * 获取指定列的�?         */
        String getColumnValue(T data, String columnName, TableType tableType);
    }
    
    /**
     * 表格列配�?     */
    public static class TableColumnConfig {
        private final Map<TableType, List<String>> columnNames = new HashMap<>();
        private final Map<TableType, List<String>> columnDisplayNames = new HashMap<>();
        private final Map<String, String> columnTagMappings = new HashMap<>();
        
        public TableColumnConfig() {
            initializeColumnConfigs();
        }
        
        private void initializeColumnConfigs() {
            // 检查表格列配置
            columnNames.put(TableType.EXAM, Arrays.asList(
                    "PatientID", "PatientName", "PatientSex", "PatientAge",
                    "StudyDate", "StudyTime", "StudyDescription", "SeriesCount"
            ));
            
            columnDisplayNames.put(TableType.EXAM, Arrays.asList(
                    "患者ID", "患者姓�?, "性别", "年龄",
                    "检查日�?, "检查时�?, "检查描�?, "序列数量"
            ));
            
            // 序列表格列配�?            columnNames.put(TableType.SERIES, Arrays.asList(
                    "SeriesNumber", "SeriesDescription", "Modality", "SeriesDate",
                    "SeriesTime", "ImageCount", "SliceThickness", "PixelSpacing"
            ));
            
            columnDisplayNames.put(TableType.SERIES, Arrays.asList(
                    "序列�?, "序列描述", "模�?, "序列日期",
                    "序列时间", "图像数量", "层厚", "像素间距"
            ));
            
            // 图像表格列配�?            columnNames.put(TableType.IMAGE, Arrays.asList(
                    "InstanceNumber", "ImageType", "Rows", "Columns",
                    "SliceLocation", "ImagePosition", "WindowCenter", "WindowWidth"
            ));
            
            columnDisplayNames.put(TableType.IMAGE, Arrays.asList(
                    "实例�?, "图像类型", "行数", "列数",
                    "层位�?, "图像位置", "窗位", "窗宽"
            ));
            
            // 初始化标签映�?            initializeTagMappings();
        }
        
        private void initializeTagMappings() {
            // 检查相关标签映�?            columnTagMappings.put("PatientID", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Patient.PATIENT_ID);
            columnTagMappings.put("PatientName", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Patient.PATIENT_NAME);
            columnTagMappings.put("PatientSex", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Patient.PATIENT_SEX);
            columnTagMappings.put("PatientAge", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Patient.PATIENT_AGE);
            columnTagMappings.put("StudyDate", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Study.STUDY_DATE);
            columnTagMappings.put("StudyTime", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Study.STUDY_TIME);
            columnTagMappings.put("StudyDescription", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Study.STUDY_DESCRIPTION);
            
            // 序列相关标签映射
            columnTagMappings.put("SeriesNumber", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Series.SERIES_NUMBER);
            columnTagMappings.put("SeriesDescription", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Series.SERIES_DESCRIPTION);
            columnTagMappings.put("Modality", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Series.MODALITY);
            columnTagMappings.put("SeriesDate", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Series.SERIES_DATE);
            columnTagMappings.put("SeriesTime", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Series.SERIES_TIME);
            
            // 图像相关标签映射
            columnTagMappings.put("InstanceNumber", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.INSTANCE_NUMBER);
            columnTagMappings.put("ImageType", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.IMAGE_TYPE);
            columnTagMappings.put("Rows", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.ROWS);
            columnTagMappings.put("Columns", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.COLUMNS);
            columnTagMappings.put("SliceLocation", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.SLICE_LOCATION);
            columnTagMappings.put("SliceThickness", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.SLICE_THICKNESS);
            columnTagMappings.put("PixelSpacing", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.PIXEL_SPACING);
            columnTagMappings.put("ImagePosition", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.IMAGE_POSITION_PATIENT);
            columnTagMappings.put("WindowCenter", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.WINDOW_CENTER);
            columnTagMappings.put("WindowWidth", com.ge.med.ct.dcm_se.tag.DicomTagConstants.Image.WINDOW_WIDTH);
        }
        
        public List<String> getColumnNames(TableType tableType) {
            return columnNames.getOrDefault(tableType, Collections.emptyList());
        }
        
        public List<String> getColumnDisplayNames(TableType tableType) {
            return columnDisplayNames.getOrDefault(tableType, Collections.emptyList());
        }
        
        public String getTagId(String columnName) {
            return columnTagMappings.get(columnName);
        }
        
        public boolean isSpecialColumn(String columnName) {
            // 特殊列不对应DICOM标签，需要特殊处�?            return "SeriesCount".equals(columnName) || 
                   "ImageCount".equals(columnName) ||
                   "ImagePosition".equals(columnName);
        }
    }
}
