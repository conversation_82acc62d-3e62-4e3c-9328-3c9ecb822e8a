package com.ge.med.ct.dcm.core;

/**
 * DICOM搜索条件
 * 支持多种查询方式
 */
public class DicomSearchCriteria {
    private String patientName;
    private String patientId;
    private String studyDate;
    private String seriesDescription;
    private String modality;

    // 支持PESI查询的扩展字�?    private DatabaseQueryParams dbParams;

    // 私有构造函数，强制使用Builder
    private DicomSearchCriteria() {}

    // Getter方法
    public String getPatientName() {
        return patientName;
    }

    public String getPatientId() {
        return patientId;
    }

    public String getStudyDate() {
        return studyDate;
    }

    public String getSeriesDescription() {
        return seriesDescription;
    }

    public String getModality() {
        return modality;
    }

    public DatabaseQueryParams getDbParams() {
        return dbParams;
    }

    /**
     * Builder模式构建搜索条件
     */
    public static class Builder {
        private final DicomSearchCriteria criteria = new DicomSearchCriteria();

        public Builder patientName(String patientName) {
            criteria.patientName = patientName;
            return this;
        }

        public Builder patientId(String patientId) {
            criteria.patientId = patientId;
            return this;
        }

        public Builder studyDate(String studyDate) {
            criteria.studyDate = studyDate;
            return this;
        }

        public Builder seriesDescription(String seriesDescription) {
            criteria.seriesDescription = seriesDescription;
            return this;
        }

        public Builder modality(String modality) {
            criteria.modality = modality;
            return this;
        }

        public Builder databaseQuery(DatabaseQueryParams params) {
            criteria.dbParams = params;
            return this;
        }

        public DicomSearchCriteria build() {
            return criteria;
        }
    }

    /**
     * 数据库查询参�?(用于PESI方案)
     */
    public static class DatabaseQueryParams {
        private String patientNameUnicode;
        private String seriesDescription;
        private String customSqlWhere;

        public DatabaseQueryParams(String patientNameUnicode, String seriesDescription) {
            this.patientNameUnicode = patientNameUnicode;
            this.seriesDescription = seriesDescription;
        }

        public DatabaseQueryParams(String customSqlWhere) {
            this.customSqlWhere = customSqlWhere;
        }

        // Getter和Setter方法
        public String getPatientNameUnicode() {
            return patientNameUnicode;
        }

        public void setPatientNameUnicode(String patientNameUnicode) {
            this.patientNameUnicode = patientNameUnicode;
        }

        public String getSeriesDescription() {
            return seriesDescription;
        }

        public void setSeriesDescription(String seriesDescription) {
            this.seriesDescription = seriesDescription;
        }

        public String getCustomSqlWhere() {
            return customSqlWhere;
        }

        public void setCustomSqlWhere(String customSqlWhere) {
            this.customSqlWhere = customSqlWhere;
        }
    }

    @Override
    public String toString() {
        return String.format("DicomSearchCriteria{patientName='%s', patientId='%s', studyDate='%s', seriesDescription='%s', modality='%s'}",
                patientName, patientId, studyDate, seriesDescription, modality);
    }
}
