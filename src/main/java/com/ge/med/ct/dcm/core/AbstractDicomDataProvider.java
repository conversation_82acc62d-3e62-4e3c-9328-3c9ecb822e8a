package com.ge.med.ct.dcm.core;

import com.ge.med.ct.dcm.model.DicomExam;
import com.ge.med.ct.dcm.model.DicomSeries;
import com.ge.med.ct.dcm.model.DicomImage;
import com.ge.med.ct.dcm.model.DicomFileModel;
import com.ge.med.ct.exception.core.DicomException;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * DICOM数据提供者抽象基�? * 提供通用的数据管理功�? */
public abstract class AbstractDicomDataProvider implements DicomDataProvider {
    protected static final Logger LOG = Logger.getLogger(AbstractDicomDataProvider.class.getName());
    
    // 数据存储
    protected final Map<String, DicomExam> exams = new ConcurrentHashMap<>();
    protected final Map<String, DicomSeries> series = new ConcurrentHashMap<>();
    protected final Map<String, DicomImage> images = new ConcurrentHashMap<>();
    protected final Map<String, DicomFileModel> fileModels = new ConcurrentHashMap<>();
    
    // 索引映射
    protected final Map<String, List<String>> examToSeries = new ConcurrentHashMap<>();
    protected final Map<String, List<String>> seriesToImages = new ConcurrentHashMap<>();
    protected final Map<String, String> imageToFilePath = new ConcurrentHashMap<>();
    
    protected volatile DataSourceType dataSourceType;
    protected volatile boolean shutdown = false;
    
    // === 检查相关操�?===
    
    @Override
    public List<DicomExam> getAllExams() {
        checkNotShutdown();
        return new ArrayList<>(exams.values());
    }
    
    @Override
    public DicomExam getExam(String examId) {
        checkNotShutdown();
        return exams.get(examId);
    }
    
    @Override
    public List<DicomExam> searchExams(DicomSearchCriteria criteria) {
        checkNotShutdown();
        
        if (criteria == null) {
            return getAllExams();
        }
        
        return exams.values().stream()
                .filter(exam -> matchesExamCriteria(exam, criteria))
                .collect(Collectors.toList());
    }
    
    // === 序列相关操作 ===
    
    @Override
    public DicomSeries getSeries(String seriesId) {
        checkNotShutdown();
        return series.get(seriesId);
    }
    
    @Override
    public List<DicomSeries> getSeriesForExam(String examId) {
        checkNotShutdown();
        
        List<String> seriesIds = examToSeries.get(examId);
        if (seriesIds == null || seriesIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return seriesIds.stream()
                .map(series::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    // === 图像相关操作 ===
    
    @Override
    public DicomImage getImage(String imageId) {
        checkNotShutdown();
        return images.get(imageId);
    }
    
    @Override
    public List<DicomImage> getImagesForSeries(String seriesId) {
        checkNotShutdown();
        
        List<String> imageIds = seriesToImages.get(seriesId);
        if (imageIds == null || imageIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return imageIds.stream()
                .map(images::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    // === 文件路径操作 ===
    
    @Override
    public String getImageFilePath(String imageId) {
        checkNotShutdown();
        return imageToFilePath.get(imageId);
    }
    
    @Override
    public Map<String, String> getImageFilePaths(List<String> imageIds) {
        checkNotShutdown();
        
        Map<String, String> result = new HashMap<>();
        for (String imageId : imageIds) {
            String path = getImageFilePath(imageId);
            if (path != null) {
                result.put(imageId, path);
            }
        }
        return result;
    }
    
    @Override
    public DicomImage getImageByPesiPath(String pesiPath) {
        checkNotShutdown();
        
        // 通过路径反向查找图像
        for (Map.Entry<String, String> entry : imageToFilePath.entrySet()) {
            if (pesiPath.equals(entry.getValue())) {
                return images.get(entry.getKey());
            }
        }
        return null;
    }
    
    // === 元数据操�?===
    
    @Override
    public String getTagValue(String imageId, String tagId) {
        checkNotShutdown();
        
        DicomImage image = images.get(imageId);
        return image != null ? image.getTagValue(tagId) : null;
    }
    
    @Override
    public Map<String, String> getImageMetadata(String imageId) {
        checkNotShutdown();
        
        DicomImage image = images.get(imageId);
        if (image == null) {
            return Collections.emptyMap();
        }
        
        Map<String, String> metadata = new HashMap<>();
        image.getTags().forEach((tagId, tag) -> {
            metadata.put(tagId, tag.getValueAsString());
        });
        return metadata;
    }
    
    // === 文件模型操作 ===
    
    @Override
    public void addFileModel(DicomFileModel model) throws DicomException {
        checkNotShutdown();
        
        if (model == null) {
            throw new DicomException("文件模型不能为空");
        }
        
        if (!model.isValid()) {
            throw new DicomException("文件模型无效: " + model.getId());
        }
        
        fileModels.put(model.getId(), model);
        processFileModel(model);
        
        LOG.fine("添加文件模型: " + model.getId());
    }
    
    @Override
    public List<DicomFileModel> getAllFileModels() {
        checkNotShutdown();
        return new ArrayList<>(fileModels.values());
    }
    
    // === 系统操作 ===
    
    @Override
    public DataSourceType getDataSourceType() {
        return dataSourceType;
    }
    
    @Override
    public DicomDataStats getDataStats() {
        checkNotShutdown();
        
        return new DicomDataStats(
                exams.size(),
                series.size(),
                images.size(),
                fileModels.size(),
                dataSourceType,
                getDataSourceDescription()
        );
    }
    
    @Override
    public void clearData() throws DicomException {
        checkNotShutdown();
        
        LOG.info("清除所有DICOM数据");
        
        exams.clear();
        series.clear();
        images.clear();
        fileModels.clear();
        examToSeries.clear();
        seriesToImages.clear();
        imageToFilePath.clear();
    }
    
    @Override
    public void shutdown() {
        if (shutdown) {
            return;
        }
        
        LOG.info("关闭DICOM数据提供�? " + getClass().getSimpleName());
        
        shutdown = true;
        
        try {
            clearData();
        } catch (DicomException e) {
            LOG.warning("清除数据时发生错�? " + e.getMessage());
        }
        
        performShutdown();
    }
    
    // === 抽象方法 ===
    
    /**
     * 获取数据源描�?     */
    protected abstract String getDataSourceDescription();
    
    /**
     * 执行具体的关闭操�?     */
    protected abstract void performShutdown();
    
    // === 工具方法 ===
    
    protected void checkNotShutdown() {
        if (shutdown) {
            throw new IllegalStateException("数据提供者已关闭");
        }
    }
    
    protected void processFileModel(DicomFileModel model) throws DicomException {
        // 处理文件模型，构建数据结�?        // 子类可以重写此方法以实现特定的处理逻辑
        
        DicomImage image = model.getImage();
        if (image != null) {
            images.put(image.getId(), image);
            imageToFilePath.put(image.getId(), model.getFilePath());
        }
    }
    
    protected boolean matchesExamCriteria(DicomExam exam, DicomSearchCriteria criteria) {
        if (criteria.getPatientName() != null && 
            !criteria.getPatientName().equals(exam.getPatientName())) {
            return false;
        }
        
        if (criteria.getPatientId() != null && 
            !criteria.getPatientId().equals(exam.getPatientID())) {
            return false;
        }
        
        if (criteria.getStudyDate() != null && 
            !criteria.getStudyDate().equals(exam.getStudyDate())) {
            return false;
        }
        
        return true;
    }
}
