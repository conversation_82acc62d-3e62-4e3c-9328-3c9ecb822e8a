package com.ge.med.ct.dcm.core;

import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * 系统命令执行服务实现
 * 封装executequery.sh和find命令的执�? */
public class SystemCommandServiceImpl implements SystemCommandService {
    private static final Logger LOG = Logger.getLogger(SystemCommandServiceImpl.class.getName());
    
    private final String executeQueryScript;
    private int commandTimeout = 30; // 默认30秒超�?    
    public SystemCommandServiceImpl(PesiDataSource dataSource) {
        this.executeQueryScript = dataSource.getExecuteQueryScript();
        this.commandTimeout = dataSource.getQueryTimeout();
    }
    
    @Override
    public CommandResult executeQuery(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return new CommandResult(-1, "", "SQL查询语句不能为空", false, 0);
        }
        
        LOG.info("执行PESI查询: " + sql.substring(0, Math.min(sql.length(), 100)) + "...");
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 构建executequery.sh命令
            List<String> command = new ArrayList<>();
            command.add(executeQueryScript);
            command.add("-c");
            command.add(sql);
            
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(false);
            
            Process process = processBuilder.start();
            
            // 读取输出
            String stdout = readStream(process.getInputStream());
            String stderr = readStream(process.getErrorStream());
            
            // 等待命令完成
            boolean finished = process.waitFor(commandTimeout, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                long executionTime = System.currentTimeMillis() - startTime;
                return new CommandResult(-1, "", "命令执行超时", false, executionTime);
            }
            
            int exitCode = process.exitValue();
            long executionTime = System.currentTimeMillis() - startTime;
            
            boolean success = (exitCode == 0);
            
            LOG.info(String.format("PESI查询完成: 退出码=%d, 执行时间=%dms", exitCode, executionTime));
            
            return new CommandResult(exitCode, stdout, stderr, success, executionTime);
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String error = "执行executequery.sh失败: " + e.getMessage();
            LOG.severe(error);
            return new CommandResult(-1, "", error, false, executionTime);
        }
    }
    
    @Override
    public List<String> findFiles(String searchPattern) {
        if (searchPattern == null || searchPattern.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        LOG.fine("执行文件查找: " + searchPattern);
        
        try {
            // 构建find命令
            List<String> command = new ArrayList<>();
            command.add("find");
            command.add(searchPattern);
            
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(false);
            
            Process process = processBuilder.start();
            
            // 读取输出
            String output = readStream(process.getInputStream());
            String stderr = readStream(process.getErrorStream());
            
            // 等待命令完成
            boolean finished = process.waitFor(commandTimeout, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                LOG.warning("find命令执行超时: " + searchPattern);
                return Collections.emptyList();
            }
            
            int exitCode = process.exitValue();
            
            if (exitCode != 0) {
                LOG.warning(String.format("find命令执行失败: 退出码=%d, 错误=%s", exitCode, stderr));
                return Collections.emptyList();
            }
            
            // 解析输出为文件路径列�?            List<String> files = new ArrayList<>();
            if (output != null && !output.trim().isEmpty()) {
                String[] lines = output.split("\n");
                for (String line : lines) {
                    String trimmed = line.trim();
                    if (!trimmed.isEmpty()) {
                        files.add(trimmed);
                    }
                }
            }
            
            LOG.fine(String.format("find命令找到 %d 个文�?, files.size()));
            return files;
            
        } catch (Exception e) {
            LOG.warning("执行find命令失败: " + e.getMessage());
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean isExecuteQueryAvailable() {
        try {
            // 尝试执行executequery.sh --help来检查可用�?            List<String> command = new ArrayList<>();
            command.add(executeQueryScript);
            command.add("--help");
            
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            
            Process process = processBuilder.start();
            
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            
            // 如果能够执行且退出码�?�?（某些脚本的help返回1），则认为可�?            int exitCode = process.exitValue();
            boolean available = (exitCode == 0 || exitCode == 1);
            
            LOG.info(String.format("executequery.sh可用性检�? %s (退出码: %d)", 
                    available ? "可用" : "不可�?, exitCode));
            
            return available;
            
        } catch (Exception e) {
            LOG.warning("检查executequery.sh可用性失�? " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public void setCommandTimeout(int timeoutSeconds) {
        if (timeoutSeconds > 0) {
            this.commandTimeout = timeoutSeconds;
            LOG.info("设置命令超时时间: " + timeoutSeconds + "�?);
        }
    }
    
    // === 私有方法 ===
    
    private String readStream(InputStream inputStream) throws IOException {
        StringBuilder output = new StringBuilder();
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (output.length() > 0) {
                    output.append("\n");
                }
                output.append(line);
            }
        }
        
        return output.toString();
    }
    
    /**
     * 解析executequery.sh的输出为查询结果
     */
    public List<Map<String, String>> parseQueryOutput(String output) {
        List<Map<String, String>> results = new ArrayList<>();
        
        if (output == null || output.trim().isEmpty()) {
            return results;
        }
        
        String[] lines = output.split("\n");
        List<String> columnNames = null;
        boolean inDataSection = false;
        
        for (String line : lines) {
            line = line.trim();
            
            // 跳过警告信息
            if (line.startsWith("warning:")) {
                continue;
            }
            
            // 跳过空行
            if (line.isEmpty()) {
                continue;
            }
            
            // 检查是否是表头分隔�?            if (line.matches("^-+\\+-+.*")) {
                inDataSection = true;
                continue;
            }
            
            // 解析列名
            if (columnNames == null && line.contains("|")) {
                columnNames = parseTableRow(line);
                continue;
            }
            
            // 解析数据�?            if (inDataSection && line.contains("|") && !line.startsWith("(")) {
                List<String> values = parseTableRow(line);
                if (columnNames != null && values.size() == columnNames.size()) {
                    Map<String, String> row = new HashMap<>();
                    for (int i = 0; i < columnNames.size(); i++) {
                        row.put(columnNames.get(i), values.get(i));
                    }
                    results.add(row);
                }
            }
            
            // 检查是否到达结果统计行
            if (line.startsWith("(") && line.endsWith("rows)")) {
                break;
            }
        }
        
        LOG.info(String.format("解析查询输出: %d 行数�?, results.size()));
        return results;
    }
    
    private List<String> parseTableRow(String line) {
        List<String> columns = new ArrayList<>();
        String[] parts = line.split("\\|");
        
        for (String part : parts) {
            columns.add(part.trim());
        }
        
        return columns;
    }
}
