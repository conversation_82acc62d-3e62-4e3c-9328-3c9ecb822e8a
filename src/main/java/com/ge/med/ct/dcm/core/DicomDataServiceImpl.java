package com.ge.med.ct.dcm.core;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Logger;

/**
 * DICOM数据服务实现�? * 支持文件系统和PESI两种数据�? */
public class DicomDataServiceImpl implements DicomDataService {
    private static final Logger LOG = Logger.getLogger(DicomDataServiceImpl.class.getName());
    
    private final AtomicReference<DicomDataProvider> dataProvider = new AtomicReference<>();
    private final AtomicReference<DicomDataSource> currentDataSource = new AtomicReference<>();
    private final AtomicReference<ProgressCallback> currentCallback = new AtomicReference<>();
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicBoolean loading = new AtomicBoolean(false);
    
    // 服务组件
    private FileSystemDataProvider fileSystemProvider;
    private PesiDataProvider pesiProvider;
    
    @Override
    public void loadDicomData(DicomDataSource source, ProgressCallback callback) {
        if (source == null) {
            throw new IllegalArgumentException("数据源不能为�?);
        }
        
        if (!source.isValid()) {
            String error = "数据源配置无�? " + source.getDescription();
            LOG.warning(error);
            if (callback != null) {
                callback.onError(error);
            }
            return;
        }
        
        if (loading.get()) {
            String error = "数据加载正在进行中，请等待完�?;
            LOG.warning(error);
            if (callback != null) {
                callback.onError(error);
            }
            return;
        }
        
        loading.set(true);
        currentDataSource.set(source);
        currentCallback.set(callback);
        
        try {
            LOG.info("开始加载DICOM数据: " + source.getDescription());
            
            switch (source.getType()) {
                case FILE_SYSTEM:
                    loadFromFileSystem((FileSystemDataSource) source, callback);
                    break;
                    
                case PESI:
                    loadFromPesi((PesiDataSource) source, callback);
                    break;
                    
                default:
                    throw new IllegalArgumentException("不支持的数据源类�? " + source.getType());
            }
            
            initialized.set(true);
            LOG.info("DICOM数据加载完成: " + source.getType());
            
            if (callback != null) {
                callback.onComplete();
            }
            
        } catch (Exception e) {
            String error = "加载DICOM数据失败: " + e.getMessage();
            LOG.severe(error);
            
            if (callback != null) {
                callback.onError(error);
            }
            
            // 清理状�?            dataProvider.set(null);
            initialized.set(false);
            
        } finally {
            loading.set(false);
        }
    }
    
    @Override
    public DicomDataProvider getDataProvider() {
        DicomDataProvider provider = dataProvider.get();
        if (provider == null) {
            throw new IllegalStateException("数据服务未初始化，请先调用loadDicomData()");
        }
        return provider;
    }
    
    @Override
    public DataSourceType getCurrentSourceType() {
        DicomDataSource source = currentDataSource.get();
        return source != null ? source.getType() : null;
    }
    
    @Override
    public void refreshData() {
        DicomDataSource source = currentDataSource.get();
        ProgressCallback callback = currentCallback.get();
        
        if (source == null) {
            throw new IllegalStateException("没有当前数据源可以刷�?);
        }
        
        LOG.info("刷新DICOM数据: " + source.getType());
        loadDicomData(source, callback);
    }
    
    @Override
    public DicomDataStats getDataStats() {
        DicomDataProvider provider = dataProvider.get();
        if (provider == null) {
            return new DicomDataStats(0, 0, 0, 0, null, "未初始化");
        }
        return provider.getDataStats();
    }
    
    @Override
    public boolean isInitialized() {
        return initialized.get() && dataProvider.get() != null;
    }
    
    @Override
    public void close() {
        LOG.info("关闭DICOM数据服务");
        
        loading.set(false);
        initialized.set(false);
        
        DicomDataProvider provider = dataProvider.getAndSet(null);
        if (provider != null) {
            try {
                provider.shutdown();
            } catch (Exception e) {
                LOG.warning("关闭数据提供者时发生错误: " + e.getMessage());
            }
        }
        
        // 清理组件
        if (fileSystemProvider != null) {
            try {
                fileSystemProvider.shutdown();
            } catch (Exception e) {
                LOG.warning("关闭文件系统提供者时发生错误: " + e.getMessage());
            }
            fileSystemProvider = null;
        }
        
        if (pesiProvider != null) {
            try {
                pesiProvider.shutdown();
            } catch (Exception e) {
                LOG.warning("关闭PESI提供者时发生错误: " + e.getMessage());
            }
            pesiProvider = null;
        }
        
        currentDataSource.set(null);
        currentCallback.set(null);
        
        LOG.info("DICOM数据服务已关�?);
    }
    
    // === 私有方法 ===
    
    private void loadFromFileSystem(FileSystemDataSource source, ProgressCallback callback) {
        LOG.info("从文件系统加载DICOM数据: " + source.getRootDirectory());
        
        if (callback != null) {
            callback.onProgress(new ProgressInfo("初始化文件系统扫描器...", 0));
        }
        
        // 创建或重用文件系统提供�?        if (fileSystemProvider == null) {
            fileSystemProvider = new FileSystemDataProvider();
        }
        
        // 加载数据
        fileSystemProvider.loadFromFileSystem(source, callback);
        dataProvider.set(fileSystemProvider);
    }
    
    private void loadFromPesi(PesiDataSource source, ProgressCallback callback) {
        LOG.info("从PESI数据库加载DICOM数据: " + source.getExecuteQueryScript());
        
        if (callback != null) {
            callback.onProgress(new ProgressInfo("初始化PESI查询服务...", 0));
        }
        
        // 创建或重用PESI提供�?        if (pesiProvider == null) {
            pesiProvider = new PesiDataProvider();
        }
        
        // 加载数据
        pesiProvider.loadFromPesi(source, callback);
        dataProvider.set(pesiProvider);
    }
    
    /**
     * 获取单例实例
     */
    private static volatile DicomDataServiceImpl instance;
    private static final Object lock = new Object();
    
    public static DicomDataServiceImpl getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new DicomDataServiceImpl();
                }
            }
        }
        return instance;
    }
    
    @Override
    public String toString() {
        DataSourceType type = getCurrentSourceType();
        boolean init = isInitialized();
        return String.format("DicomDataServiceImpl{type=%s, initialized=%s, loading=%s}",
                type, init, loading.get());
    }
}
