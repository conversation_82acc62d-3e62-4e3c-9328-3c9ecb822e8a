package com.ge.med.ct.dcm.table;

import com.ge.med.ct.dcm.model.DicomSeries;

import java.util.logging.Logger;

/**
 * 序列数据转换�? * 负责将DicomSeries对象转换为表格显示格�? */
public class SeriesConverter implements TableDataEngine.DataConverter<DicomSeries> {
    private static final Logger LOG = Logger.getLogger(SeriesConverter.class.getName());
    
    private final TagFormatter formatter;
    private final TableDataEngine.TableColumnConfig columnConfig;
    
    public SeriesConverter(TagFormatter formatter, TableDataEngine.TableColumnConfig columnConfig) {
        this.formatter = formatter;
        this.columnConfig = columnConfig;
    }
    
    @Override
    public String getColumnValue(DicomSeries series, String columnName, TableDataEngine.TableType tableType) {
        if (series == null) {
            return "";
        }
        
        try {
            // 处理特殊�?            if (columnConfig.isSpecialColumn(columnName)) {
                return getSpecialColumnValue(series, columnName);
            }
            
            // 获取标签ID
            String tagId = columnConfig.getTagId(columnName);
            if (tagId == null || tagId.isEmpty()) {
                LOG.fine("�?" + columnName + " 未找到对应的标签ID");
                return "";
            }
            
            // 获取标签�?            String tagValue = series.getTagValue(tagId);
            if (tagValue == null || tagValue.trim().isEmpty()) {
                return "";
            }
            
            // 根据列名进行特殊格式�?            return formatColumnValue(columnName, tagId, tagValue);
            
        } catch (Exception e) {
            LOG.warning(String.format("获取序列列值失�? %s - %s", columnName, e.getMessage()));
            return "";
        }
    }
    
    /**
     * 获取特殊列的�?     */
    private String getSpecialColumnValue(DicomSeries series, String columnName) {
        switch (columnName) {
            case "ImageCount":
                return String.valueOf(series.getImageCount());
            default:
                return "";
        }
    }
    
    /**
     * 根据列名格式化�?     */
    private String formatColumnValue(String columnName, String tagId, String tagValue) {
        switch (columnName) {
            case "SeriesNumber":
                return formatter.formatInteger(tagValue);
                
            case "SeriesDescription":
                return formatter.formatSafeText(tagValue);
                
            case "Modality":
                return formatter.formatModality(tagValue);
                
            case "SeriesDate":
                return formatter.formatDate(tagValue);
                
            case "SeriesTime":
                return formatter.formatTime(tagValue);
                
            case "SliceThickness":
                return formatter.formatSliceThickness(tagValue);
                
            case "PixelSpacing":
                return formatter.formatPixelSpacing(tagValue);
                
            default:
                // 使用通用格式�?                return formatter.format(tagId, tagValue);
        }
    }
}
