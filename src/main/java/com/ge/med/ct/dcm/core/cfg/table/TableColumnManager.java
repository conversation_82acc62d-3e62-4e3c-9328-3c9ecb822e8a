package com.ge.med.ct.dcm.core.cfg.table;

import com.ge.med.ct.dcm.core.cfg.AppDefaults;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.ConfigMessages;
import com.ge.med.ct.exception.message.Message;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * 表格列管理器 - 负责管理表格列的配置信息、验证和错误处理
 */
public class TableColumnManager {
    private static final Logger LOG = Logger.getLogger(TableColumnManager.class.getName());

    // 表格名称常量
    public static final String TABLE_EXAM = "exam";
    public static final String TABLE_SERIES = "series";
    public static final String TABLE_IMAGE = "image";

    // 配置键常�?    private static final String CONFIG_KEY_COLUMNS = "table.%s.columns";
    private static final String CONFIG_KEY_COLUMN_BASE = "table.%s.column.%s";
    private static final String CONFIG_KEY_DISPLAY_NAME = ".display-name";
    private static final String CONFIG_KEY_WIDTH = ".width";
    private static final String CONFIG_KEY_VISIBLE = ".visible";
    private static final String CONFIG_KEY_ORDER = ".order";
    private static final String CONFIG_KEY_SPECIAL = ".special";

    // 特殊标记常量
    private static final String SPECIAL_TAG_ID = "special";

    private final Map<String, List<TableColumn>> tableConfigs = new HashMap<>();
    private final Properties properties;
    private final Set<String> specialColumns;

    public TableColumnManager(Properties properties) {
        this.properties = properties;
        this.specialColumns = initializeSpecialColumns();
        initializeAllTables();
    }

    private Set<String> initializeSpecialColumns() {
        Set<String> columns = new HashSet<>(Arrays.asList(
            "PatientID", "StudyID", "Series", "Images", "Description", "Type", "Name",
            "Date", "Modality", "Manufacturer", "Image", "imgctr", "ImagePosition",
            "ImageCount", "FieldOfView", "ReconstructionDiameter", "ConvolutionKernel",
            "KVP", "GantryTilt"));
        return Collections.unmodifiableSet(columns);
    }

    private void initializeAllTables() {
        loadColumnsForTable(TABLE_EXAM);
        loadColumnsForTable(TABLE_SERIES);
        loadColumnsForTable(TABLE_IMAGE);
    }

    private void loadColumnsForTable(String tableName) {
        List<TableColumn> columns = new ArrayList<>();
        String columnsKey = String.format(CONFIG_KEY_COLUMNS, tableName);
        String columnsStr = properties.getProperty(columnsKey, "");

        if (columnsStr.isEmpty()) {
            LOG.warning("表格 " + tableName + " 没有列配�?);
            tableConfigs.put(tableName, columns);
            return;
        }

        Arrays.stream(columnsStr.split(","))
            .map(String::trim)
            .filter(name -> !name.isEmpty())
            .forEach(columnName -> {
                try {
                    columns.add(buildColumnFromProperties(tableName, columnName));
                } catch (NumberFormatException e) {
                    LOG.warning("配置解析错误: " + columnName + ", " + e.getMessage());
                }
            });

        columns.sort(Comparator.comparingInt(TableColumn::getOrder));
        tableConfigs.put(tableName, columns);
    }

    private TableColumn buildColumnFromProperties(String tableName, String columnName) {
        String baseKey = String.format(CONFIG_KEY_COLUMN_BASE, tableName, columnName);

        // 读取配置属�?        String displayName = properties.getProperty(baseKey + CONFIG_KEY_DISPLAY_NAME, columnName);
        int width = Integer.parseInt(properties.getProperty(baseKey + CONFIG_KEY_WIDTH,
                String.valueOf(AppDefaults.DEFAULT_COLUMN_WIDTH)));
        boolean visible = Boolean.parseBoolean(properties.getProperty(baseKey + CONFIG_KEY_VISIBLE,
                String.valueOf(AppDefaults.DEFAULT_COLUMN_VISIBLE)));
        int order = Integer.parseInt(properties.getProperty(baseKey + CONFIG_KEY_ORDER,
                String.valueOf(AppDefaults.DEFAULT_COLUMN_ORDER)));
        boolean isSpecial = Boolean.parseBoolean(properties.getProperty(baseKey + CONFIG_KEY_SPECIAL, "false"));

        // 创建列配�?        TableColumnBuilder builder = new TableColumnBuilder(columnName)
                .displayName(displayName)
                .width(width)
                .visible(visible)
                .order(order);

        // 处理特殊�?        if (isSpecial) {
            builder.tagId(SPECIAL_TAG_ID);
        } else {
            // 使用DICOM标签名获取标签ID
            String tagId = DicomTagConstants.getTagIdByName(columnName);
            if (tagId.isEmpty()) {
                LOG.warning("无法为列 " + columnName + " 找到对应的标签ID");
            }
            builder.tagId(tagId);
        }

        return builder.build();
    }

    public List<TableColumn> getColumns(String tableName) {
        return tableConfigs.getOrDefault(tableName, new ArrayList<>());
    }

    public List<TableColumn> getVisibleColumns(String tableName) {
        return getColumns(tableName).stream()
                .filter(TableColumn::isVisible)
                .collect(Collectors.toList());
    }

    public List<String> getColumnNames(String tableName) {
        return getColumns(tableName).stream()
                .filter(TableColumn::isVisible)
                .map(TableColumn::getName)
                .collect(Collectors.toList());
    }

    public String resolveTagId(String tableName, String columnName) {
        // 先检查是否是特殊�?        String specialMark = getColumns(tableName).stream()
                .filter(col -> col.getName().equals(columnName))
                .findFirst()
                .map(TableColumn::getTagId)
                .orElse("");

        if (SPECIAL_TAG_ID.equals(specialMark)) {
            // 对于特殊列，返回特殊标记
            return SPECIAL_TAG_ID;
        }

        // 使用DICOM标签名称获取标签ID
        String tagId = DicomTagConstants.getTagIdByName(columnName);
        if (tagId.isEmpty()) {
            LOG.warning("表格 " + tableName + " 中的�?" + columnName + " 无法找到对应的标签ID");
        }
        return tagId;
    }

    public String resolveTagName(String tableName, String columnName) {
        // 列名就是DICOM标签名，对于特殊列，需要特殊处�?        String specialMark = getColumns(tableName).stream()
                .filter(col -> col.getName().equals(columnName))
                .findFirst()
                .map(TableColumn::getTagId)
                .orElse("");

        // 对于特殊列，返回列名作为标签�?        if (SPECIAL_TAG_ID.equals(specialMark)) {
            return columnName;
        }

        // 直接返回列名作为标签�?        return columnName;
    }

    public void reload() {
        tableConfigs.clear();
        initializeAllTables();
    }

    /**
     * 验证表格列配�?     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();
        Set<String> tableNames = new HashSet<>(Arrays.asList(TABLE_EXAM, TABLE_SERIES, TABLE_IMAGE));

        for (String tableName : tableNames) {
            List<TableColumn> columns = getColumns(tableName);
            if (columns.isEmpty()) {
                result.addError("表格配置", ConfigMessages.TABLE_CONFIG_EMPTY.format(tableName));
                LOG.warning("表格 " + tableName + " 没有列配�?);
                continue;
            }

            Set<String> columnNames = new HashSet<>();
            Set<String> tagIds = new HashSet<>();
            columns.forEach(column -> validateColumn(tableName, column, result, columnNames, tagIds));
        }

        return result;
    }

    private void validateColumn(String tableName, TableColumn column,
            ValidationResult result, Set<String> columnNames, Set<String> tagIds) {
        String columnName = column.getName();
        if (isEmpty(columnName)) {
            result.addError("列名", ConfigMessages.COLUMN_NAME_EMPTY);
            return;
        }

        if (columnNames.contains(columnName)) {
            result.addError("列名重复", ConfigMessages.CONFIG_INVALID.format(columnName));
            LOG.warning("表格 " + tableName + " 中列�?" + columnName + " 重复");
        } else {
            columnNames.add(columnName);
        }

        if (!isSpecialColumn(columnName) && !SPECIAL_TAG_ID.equals(column.getTagId())) {
            validateTagMapping(tableName, columnName, column, result, tagIds);
        }

        if (isEmpty(column.getDisplayName())) {
            result.addError("显示名称", ConfigMessages.COLUMN_NAME_EMPTY.format(columnName));
            LOG.warning("�?" + columnName + " 的显示名称为�?);
        }

        if (column.getWidth() <= 0) {
            result.addError("列宽�?, ConfigMessages.CONFIG_INVALID.format(columnName));
            LOG.warning("�?" + columnName + " 的宽度无�? " + column.getWidth());
        }
    }

    private void validateTagMapping(String tableName, String columnName,
            TableColumn column, ValidationResult result, Set<String> tagIds) {
        try {
            String tagName = resolveTagName(tableName, columnName);
            String tagId = column.getTagId();

            if (isEmpty(tagName) && isEmpty(tagId)) {
                result.addError("标签映射", ConfigMessages.COLUMN_TAG_MISSING.format(columnName));
                LOG.warning("�?" + columnName + " 缺少标签映射");
                return;
            }

            if (!isEmpty(tagName) && !DicomTagConstants.containsTagName(tagName)) {
                result.addError("标签�?, ConfigMessages.CONFIG_INVALID.format(tagName));
                LOG.warning("无效的标签名: " + tagName);
            }

            if (!isEmpty(tagId)) {
                if (!SPECIAL_TAG_ID.equals(tagId) && !DicomTagConstants.isValidTag(tagId)) {
                    result.addError("标签ID", ConfigMessages.CONFIG_INVALID.format(tagId));
                    LOG.warning("无效的标签ID: " + tagId);
                }

                if (!SPECIAL_TAG_ID.equals(tagId) && tagIds.contains(tagId)) {
                    result.addError("标签ID重复", ConfigMessages.CONFIG_INVALID.format(tagId));
                    LOG.warning("表格 " + tableName + " 中标签ID " + tagId + " 重复");
                } else {
                    tagIds.add(tagId);
                }
            }

            if (!isEmpty(tagName) && !isEmpty(tagId) && !SPECIAL_TAG_ID.equals(tagId)) {
                String expectedTagId = DicomTagConstants.getTagIdByName(tagName);
                if (!isEmpty(expectedTagId) && !expectedTagId.equals(tagId)) {
                    result.addError("标签映射", ConfigMessages.CONFIG_INVALID.format(columnName));
                    LOG.warning("表格 " + tableName + " 中标签名和标签ID不一�?);
                }
            }
        } catch (Exception e) {
            result.addError("标签映射", ConfigMessages.CONFIG_INVALID.format(columnName));
            LOG.warning("验证标签映射时发生异�? " + e.getMessage());
        }
    }

    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    private boolean isSpecialColumn(String column) {
        return specialColumns.contains(column);
    }

    /**
     * 验证配置并抛出异�?     * @throws QAToolException 验证失败时抛出异�?     */
    @HandleException(errorCode = ErrorCode.CONFIG)
    public void validateOrThrow() throws QAToolException {
        ValidationResult result = validate();
        result.throwIfInvalid();
    }

    /**
     * 静态方法：验证表格配置并抛出异�?     * @param config 要验证的表格列管理器
     * @throws QAToolException 验证失败时抛出异�?     */
    @HandleException(errorCode = ErrorCode.CONFIG)
    public static void validateTableConfigOrThrow(TableColumnManager config) throws QAToolException {
        if (config == null) {
            throw ExceptionFactory.createConfigValidationException(
                    ErrorCode.CONFIG,
                    ConfigMessages.CONFIG_NULL);
        }

        config.validateOrThrow();
    }

    public static class ValidationResult {
        private static final Logger LOG = Logger.getLogger(ValidationResult.class.getName());
        private final List<ValidationError> errors = new ArrayList<>();
        private boolean valid = true;

        public void addError(String field, String message) {
            errors.add(new ValidationError(field, message));
            valid = false;
            LOG.warning("验证错误: [" + field + "] " + message);
        }

        public void addError(String field, Message message) {
            addError(field, message == null ? "未知错误" : message.getDefaultMessage());
        }

        public boolean isValid() { return valid; }
        public List<ValidationError> getErrors() { return new ArrayList<>(errors); }

        public String getErrorMessage() {
            return errors.isEmpty() ? "" : errors.stream()
                .map(error -> error.getField() + ": " + error.getMessage())
                .collect(Collectors.joining("; "));
        }

        public void throwIfInvalid() throws QAToolException {
            if (!valid) {
                LOG.severe("验证失败: " + getErrorMessage());
                ValidationError firstError = errors.get(0);
                throw ExceptionFactory.createConfigValidationException(
                    ErrorCode.CONFIG,
                    ConfigMessages.VALIDATION_FAILED,
                    firstError.getField() + ": " + firstError.getMessage() +
                    (errors.size() > 1 ? " (及其�? + (errors.size() - 1) + "个错�?" : "")
                );
            }
        }
    }

    public static class ValidationError {
        private final String field;
        private final String message;

        public ValidationError(String field, String message) {
            this.field = field;
            this.message = message;
        }

        public String getField() { return field; }
        public String getMessage() { return message; }

        @Override
        public String toString() { return field + ": " + message; }
    }
}
