package com.ge.med.ct.dcm;

import com.ge.med.ct.dcm.core.*;
import com.ge.med.ct.dcm.model.*;
import com.ge.med.ct.dcm.table.TableDataEngine;
import com.ge.med.ct.dcm.storage.DicomExporter;

import java.util.List;
import java.util.Vector;
import java.util.logging.Logger;

/**
 * DICOM 使用示例
 * 演示如何使用重构后的DICOM功能
 */
public class DicomExample {
    private static final Logger LOG = Logger.getLogger(DicomExample.class.getName());
    
    public static void main(String[] args) {
        DicomExample example = new DicomExample();
        
        try {
            // 演示文件系统方案
            example.demonstrateFileSystemUsage();
            
            // 演示PESI方案
            example.demonstratePesiUsage();
            
            // 演示表格转换
            example.demonstrateTableConversion();
            
            // 演示数据导出
            example.demonstrateDataExport();
            
        } catch (Exception e) {
            LOG.severe("示例执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示文件系统方案使用
     */
    public void demonstrateFileSystemUsage() {
        LOG.info("=== 演示文件系统方案 ===");
        
        try {
            // 1. 创建文件系统数据源
            FileSystemDataSource fileSource = new FileSystemDataSource.Builder()
                    .rootDirectory("C:/temp/dicom")  // 示例路径
                    .recursive(true)
                    .enableMonitoring(false)
                    .fileExtensions(".dcm", ".dicom", "")
                    .build();
            
            LOG.info("文件系统数据源: " + fileSource.getDescription());
            
            // 2. 创建数据服务
            DicomDataService service = DicomDataServiceImpl.getInstance();
            
            // 3. 加载数据
            service.loadDicomData(fileSource, new DicomDataService.ProgressCallback() {
                @Override
                public void onProgress(DicomDataService.ProgressInfo info) {
                    LOG.info("进度: " + info.getMessage() + " (" + info.getPercentage() + "%)");
                }
                
                @Override
                public void onError(String error) {
                    LOG.warning("错误: " + error);
                }
                
                @Override
                public void onComplete() {
                    LOG.info("文件系统数据加载完成");
                }
            });
            
            // 4. 使用数据
            if (service.isInitialized()) {
                DicomDataProvider provider = service.getDataProvider();
                
                // 获取统计信息
                DicomDataStats stats = provider.getDataStats();
                LOG.info("数据统计: " + stats.toString());
                
                // 获取检查列表
                List<DicomExam> exams = provider.getAllExams();
                LOG.info("找到 " + exams.size() + " 个检查");
                
                // 遍历检查
                for (DicomExam exam : exams) {
                    LOG.info("检查: " + exam.toString());
                    
                    // 获取序列
                    List<DicomSeries> seriesList = provider.getSeriesForExam(exam.getId());
                    for (DicomSeries series : seriesList) {
                        LOG.info("  序列: " + series.toString());
                        
                        // 获取图像
                        List<DicomImage> images = provider.getImagesForSeries(series.getId());
                        LOG.info("    图像数量: " + images.size());
                        
                        // 获取文件路径
                        for (DicomImage image : images) {
                            String filePath = provider.getImageFilePath(image.getId());
                            LOG.info("    图像路径: " + filePath);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            LOG.warning("文件系统方案演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示PESI方案使用
     */
    public void demonstratePesiUsage() {
        LOG.info("=== 演示PESI方案 ===");
        
        try {
            // 1. 创建PESI数据源
            PesiDataSource pesiSource = new PesiDataSource.Builder()
                    .executeQueryScript("/usr/local/bin/executequery.sh")
                    .imagePoolPath("/usr/g/sdc_image_pool/images")
                    .queryTimeout(30)
                    .build();
            
            LOG.info("PESI数据源: " + pesiSource.getDescription());
            
            // 2. 创建数据服务
            DicomDataService service = DicomDataServiceImpl.getInstance();
            
            // 3. 加载数据
            service.loadDicomData(pesiSource, new DicomDataService.ProgressCallback() {
                @Override
                public void onProgress(DicomDataService.ProgressInfo info) {
                    LOG.info("PESI进度: " + info.getMessage() + " (" + info.getPercentage() + "%)");
                }
                
                @Override
                public void onError(String error) {
                    LOG.warning("PESI错误: " + error);
                }
                
                @Override
                public void onComplete() {
                    LOG.info("PESI数据加载完成");
                }
            });
            
            // 4. 使用PESI数据
            if (service.isInitialized()) {
                DicomDataProvider provider = service.getDataProvider();
                
                // 搜索特定患者
                DicomSearchCriteria criteria = new DicomSearchCriteria.Builder()
                        .patientName("RT_exam_MR")
                        .seriesDescription("FIESTA MULTICOUPES")
                        .build();
                
                List<DicomExam> searchResults = provider.searchExams(criteria);
                LOG.info("搜索结果: " + searchResults.size() + " 个检查");
                
                // 获取PESI路径
                for (DicomExam exam : searchResults) {
                    for (DicomSeries series : provider.getSeriesForExam(exam.getId())) {
                        for (DicomImage image : provider.getImagesForSeries(series.getId())) {
                            String pesiPath = provider.getImageFilePath(image.getId());
                            LOG.info("PESI路径: " + pesiPath);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            LOG.warning("PESI方案演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示表格转换功能
     */
    public void demonstrateTableConversion() {
        LOG.info("=== 演示表格转换功能 ===");
        
        try {
            DicomDataService service = DicomDataServiceImpl.getInstance();
            
            if (!service.isInitialized()) {
                LOG.warning("数据服务未初始化，跳过表格转换演示");
                return;
            }
            
            DicomDataProvider provider = service.getDataProvider();
            TableDataEngine tableEngine = new TableDataEngine();
            
            // 1. 转换检查数据
            List<DicomExam> exams = provider.getAllExams();
            Vector<Vector<String>> examTableData = tableEngine.convertExamData(exams);
            String[] examColumns = tableEngine.getTableColumnDisplayNames(TableDataEngine.TableType.EXAM);
            
            LOG.info("检查表格数据: " + examTableData.size() + " 行 x " + examColumns.length + " 列");
            LOG.info("检查表格列: " + String.join(", ", examColumns));
            
            // 2. 转换序列数据
            if (!exams.isEmpty()) {
                List<DicomSeries> seriesList = provider.getSeriesForExam(exams.get(0).getId());
                Vector<Vector<String>> seriesTableData = tableEngine.convertSeriesData(seriesList);
                String[] seriesColumns = tableEngine.getTableColumnDisplayNames(TableDataEngine.TableType.SERIES);
                
                LOG.info("序列表格数据: " + seriesTableData.size() + " 行 x " + seriesColumns.length + " 列");
                LOG.info("序列表格列: " + String.join(", ", seriesColumns));
            }
            
            // 3. 转换图像数据
            if (!exams.isEmpty()) {
                List<DicomSeries> seriesList = provider.getSeriesForExam(exams.get(0).getId());
                if (!seriesList.isEmpty()) {
                    List<DicomImage> images = provider.getImagesForSeries(seriesList.get(0).getId());
                    Vector<Vector<String>> imageTableData = tableEngine.convertImageData(images);
                    String[] imageColumns = tableEngine.getTableColumnDisplayNames(TableDataEngine.TableType.IMAGE);
                    
                    LOG.info("图像表格数据: " + imageTableData.size() + " 行 x " + imageColumns.length + " 列");
                    LOG.info("图像表格列: " + String.join(", ", imageColumns));
                }
            }
            
        } catch (Exception e) {
            LOG.warning("表格转换演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示数据导出功能
     */
    public void demonstrateDataExport() {
        LOG.info("=== 演示数据导出功能 ===");
        
        try {
            DicomDataService service = DicomDataServiceImpl.getInstance();
            
            if (!service.isInitialized()) {
                LOG.warning("数据服务未初始化，跳过数据导出演示");
                return;
            }
            
            DicomDataProvider provider = service.getDataProvider();
            DicomExporter exporter = new DicomExporter();
            
            // 1. 导出为JSON
            String jsonFile = exporter.exportToJson(provider, "dicom_export_example.json");
            LOG.info("JSON导出完成: " + jsonFile);
            
            // 2. 导出检查数据为CSV
            String examCsvFile = exporter.exportToCsv(provider, "dicom_exams_example.csv", 
                    DicomExporter.ExportLevel.EXAM);
            LOG.info("检查CSV导出完成: " + examCsvFile);
            
            // 3. 导出序列数据为CSV
            String seriesCsvFile = exporter.exportToCsv(provider, "dicom_series_example.csv", 
                    DicomExporter.ExportLevel.SERIES);
            LOG.info("序列CSV导出完成: " + seriesCsvFile);
            
            // 4. 导出统计信息
            String statsFile = exporter.exportStatistics(provider, "dicom_stats_example.json");
            LOG.info("统计信息导出完成: " + statsFile);
            
        } catch (Exception e) {
            LOG.warning("数据导出演示失败: " + e.getMessage());
        }
    }
}
