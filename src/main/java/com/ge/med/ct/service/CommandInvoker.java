package com.ge.med.ct.service;

import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.CommonMessages;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

@HandleException(errorCode = ErrorCode.SYS_EXTERNAL)
public class CommandInvoker {
    private static final Logger LOG = Logger.getLogger(CommandInvoker.class.getName());

    public static final String IAUI_SCRIPT = "scripts/ia2commander";
    private static final String PERL_PATH = "perl";
    private static final String SCRIPTS_DIR = "scripts";

    /**
     * 执行指定脚本并返回输出
     *
     * @param script 脚本路径
     * @param command 命令参数
     * @return 脚本执行的输出
     * @throws QAToolException 如果执行失败
     */
    public static String execute(String script, String command) throws QAToolException {
        StringBuilder output = new StringBuilder();

        // 找到脚本文件的绝对路径
        File scriptFile = findScriptFile(script);
        LOG.info("执行脚本: " + scriptFile.getAbsolutePath());
        LOG.info("命令参数: " + command);

        // 检查并准备环境
        checkScriptPermissions(scriptFile);

        try {
            // 解析命令并构建命令参数列表
            List<String> cmdList = buildCommandList(scriptFile, command);

            // 创建ProcessBuilder
            ProcessBuilder pBuilder = new ProcessBuilder(cmdList);

            // 配置工作目录
            configureWorkingDirectory(pBuilder, scriptFile);

            // 合并错误流和标准输出流
            pBuilder.redirectErrorStream(true);

            LOG.info("执行命令: " + String.join(" ", cmdList));

            // 启动进程并收集输出
            Process process = pBuilder.start();
            collectProcessOutput(process, output);

            // 等待进程结束并检查退出码
            int exitCode = process.waitFor();
            LOG.info("脚本执行结束，退出码: " + exitCode);

            // 检查退出码
            if (exitCode != 0) {
                String errorMsg = "执行外部命令失败，退出码: " + exitCode;
                LOG.severe(errorMsg);
                LOG.severe("脚本输出: " + output.toString());
                throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.SYSTEM_ERROR.format(errorMsg));
            }

            return output.toString();
        } catch (IOException e) {
            String errorMsg = "执行IO异常: " + e.getMessage();
            LOG.severe(errorMsg);
            throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.IO_ERROR, e, errorMsg);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            String errorMsg = "线程中断: " + e.getMessage();
            LOG.severe(errorMsg);
            throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.THREAD_INTERRUPTED, e, errorMsg);
        }
    }

    /**
     * 查找脚本文件，支持相对路径和绝对路径
     */
    private static File findScriptFile(String script) throws QAToolException {
        File scriptFile = new File(script);

        if (!scriptFile.isAbsolute()) {
            // 尝试从当前目录查找
            String currentDir = System.getProperty("user.dir");
            scriptFile = new File(currentDir, script);

            // 如果仍然找不到，尝试从resources/scripts目录查找
            if (!IOOperator.fileExists(scriptFile.getPath())) {
                File resourcesDir = new File(currentDir, "src/main/resources");
                if (IOOperator.fileExists(resourcesDir.getPath())) {
                    scriptFile = new File(resourcesDir, script);
                }

                // 直接尝试scripts目录
                if (!IOOperator.fileExists(scriptFile.getPath()) && script.startsWith(SCRIPTS_DIR)) {
                    scriptFile = new File(resourcesDir, script);
                } else if (!IOOperator.fileExists(scriptFile.getPath())) {
                    scriptFile = new File(resourcesDir, SCRIPTS_DIR + File.separator + new File(script).getName());
                }
            }
        }

        // 检查脚本是否存在
        if (!IOOperator.fileExists(scriptFile.getPath())) {
            String errorMsg = "脚本文件不存在: " + scriptFile.getAbsolutePath();
            LOG.severe(errorMsg);
            throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.RESOURCE_NOT_FOUND, "脚本", errorMsg);
        }

        return scriptFile;
    }

    /**
     * 检查脚本文件的执行权限，如果在Linux环境下尝试设置执行权限
     */
    private static boolean checkScriptPermissions(File scriptFile) throws QAToolException {
        if (!IOOperator.fileExists(scriptFile.getPath())) {
            String errorMsg = "脚本文件不存在: " + scriptFile.getAbsolutePath();
            LOG.severe(errorMsg);
            throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.RESOURCE_NOT_FOUND, "脚本", errorMsg);
        }

        if (!scriptFile.canExecute()) {
            LOG.warning("脚本不可执行，尝试设置执行权限: " + scriptFile.getAbsolutePath());

            // 如果是Linux环境，使用chmod命令设置权限
            if (PlatformUtils.isLinux()) {
                try {
                    // 先尝试使用Java API
                    boolean success = scriptFile.setExecutable(true);
                    if (!success) {
                        // 如果Java API失败，尝试使用chmod命令
                        ProcessBuilder chmodBuilder = new ProcessBuilder("chmod", "755", scriptFile.getAbsolutePath());
                        Process process = chmodBuilder.start();
                        int exitCode = process.waitFor();

                        if (exitCode != 0 || !scriptFile.canExecute()) {
                            String errorMsg = "无法设置脚本执行权限: " + scriptFile.getAbsolutePath();
                            LOG.severe(errorMsg);
                            throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.PERMISSION_DENIED, errorMsg);
                        }
                    }
                } catch (Exception e) {
                    String errorMsg = "设置执行权限失败: " + e.getMessage();
                    LOG.severe(errorMsg);
                    throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.PERMISSION_DENIED, e, errorMsg);
                }
            } else {
                // 在非Linux环境中使用Java API
                boolean success = scriptFile.setExecutable(true);
                if (!success) {
                    String errorMsg = "无法设置脚本执行权限: " + scriptFile.getAbsolutePath();
                    LOG.severe(errorMsg);
                    throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.PERMISSION_DENIED, errorMsg);
                }
            }

            // 再次检查权限是否生效
            if (!scriptFile.canExecute()) {
                String errorMsg = "无法设置脚本执行权限，即使命令执行成功";
                LOG.severe(errorMsg);
                throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.PERMISSION_DENIED, errorMsg);
            }

            LOG.info("成功设置脚本执行权限: " + scriptFile.getAbsolutePath());
        }

        return true;
    }

    /**
     * 构建命令参数列表，正确处理命令行参数
     */
    private static List<String> buildCommandList(File scriptFile, String command) {
        List<String> cmdList = new ArrayList<>();

        // 根据平台选择合适的解释器
        if (scriptFile.getName().endsWith(".pl") || scriptFile.getName().contains("ia2commander")) {
            // Perl脚本
            cmdList.add(PERL_PATH);
            cmdList.add(scriptFile.getAbsolutePath());
        } else if (PlatformUtils.isWindows() && scriptFile.getName().endsWith(".bat")) {
            // Windows批处理文件
            cmdList.add("cmd.exe");
            cmdList.add("/c");
            cmdList.add(scriptFile.getAbsolutePath());
        } else if (PlatformUtils.isLinux() && (scriptFile.getName().endsWith(".sh") || !scriptFile.getName().contains("."))) {
            // Linux shell脚本
            cmdList.add("/bin/bash");
            cmdList.add(scriptFile.getAbsolutePath());
        } else {
            // 其他脚本类型，直接执行
            cmdList.add(scriptFile.getAbsolutePath());
        }

        // 解析命令参数，处理带引号的参数
        parseCommandArguments(cmdList, command);

        return cmdList;
    }

    /**
     * 解析命令行参数，处理引号和转义字符
     */
    private static void parseCommandArguments(List<String> cmdList, String command) {
        if (command == null || command.trim().isEmpty()) {
            return;
        }

        StringBuilder argBuilder = new StringBuilder();
        boolean inQuotes = false;
        char quoteChar = 0;
        boolean escapeNext = false;

        for (int i = 0; i < command.length(); i++) {
            char c = command.charAt(i);

            if (escapeNext) {
                argBuilder.append(c);
                escapeNext = false;
            } else if (c == '\\') {
                escapeNext = true;
            } else if ((c == '"' || c == '\'')) {
                if (!inQuotes) {
                    inQuotes = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    inQuotes = false;
                } else {
                    argBuilder.append(c);
                }
            } else if (Character.isWhitespace(c) && !inQuotes) {
                if (argBuilder.length() > 0) {
                    cmdList.add(argBuilder.toString());
                    argBuilder.setLength(0);
                }
            } else {
                argBuilder.append(c);
            }
        }

        if (argBuilder.length() > 0) {
            cmdList.add(argBuilder.toString());
        }
    }

    /**
     * 配置进程工作目录
     */
    private static void configureWorkingDirectory(ProcessBuilder pBuilder, File scriptFile) {
        File workDir = scriptFile.getParentFile();
        if (workDir != null && IOOperator.isDirectory(workDir.getPath())) {
            pBuilder.directory(workDir);
        }
    }

    /**
     * 收集进程输出
     */
    private static void collectProcessOutput(Process process, StringBuilder output) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append(PlatformUtils.getLineSeparator());
            }
        }
    }
}