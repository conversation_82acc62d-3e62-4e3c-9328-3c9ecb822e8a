package com.ge.med.ct.service;

import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.imageio.ImageIO;
import javax.swing.Icon;
import javax.swing.ImageIcon;

import com.ge.med.ct.ui.utils.UIConstants;

/**
 * 图标加载工具类，负责加载和缓存应用程序图标
 */
public class IconLoader {
    private static final Logger LOGGER = Logger.getLogger(IconLoader.class.getName());
    private static final Map<String, Icon> ICON_CACHE = new HashMap<>();
    private static final String ICON_PATH = "/assets/icons/";
    
    // 私有构造函数，防止实例化
    private IconLoader() {}
    
    /**
     * 加载指定名称的图标
     * 
     * @param iconName 图标名称，不包含路径和扩展名
     * @return 图标对象，如果加载失败则返回null
     */
    public static Icon getIcon(String iconName) {
        return getIcon(iconName, UIConstants.ICON_MEDIUM);
    }
    
    /**
     * 加载指定名称和尺寸的图标
     * 
     * @param iconName 图标名称，不包含路径和扩展名
     * @param size 图标尺寸
     * @return 图标对象，如果加载失败则返回null
     */
    public static Icon getIcon(String iconName, int size) {
        // 生成缓存键
        String cacheKey = iconName + "_" + size;
        
        // 检查缓存
        if (ICON_CACHE.containsKey(cacheKey)) {
            return ICON_CACHE.get(cacheKey);
        }
        
        // 尝试加载图标
        Icon icon = loadIcon(iconName, size);
        if (icon != null) {
            ICON_CACHE.put(cacheKey, icon);
        }
        
        return icon;
    }
    
    /**
     * 加载图标资源
     * 
     * @param iconName 图标名称
     * @param size 图标尺寸
     * @return 图标对象，如果加载失败则返回null
     */
    private static Icon loadIcon(String iconName, int size) {
        try {
            // 尝试不同的扩展名
            String[] extensions = {".png", ".jpg", ".gif", ".svg"};
            URL iconUrl = null;
            
            for (String ext : extensions) {
                String resourcePath = ICON_PATH + iconName + ext;
                iconUrl = IconLoader.class.getResource(resourcePath);
                if (iconUrl != null) {
                    break;
                }
            }
            
            if (iconUrl == null) {
                LOGGER.warning("Icon not found: " + iconName);
                return null;
            }
            
            // 加载图像
            BufferedImage img = ImageIO.read(iconUrl);
            
            // 调整大小
            if (img.getWidth() != size || img.getHeight() != size) {
                Image scaledImg = img.getScaledInstance(size, size, Image.SCALE_SMOOTH);
                ImageIcon icon = new ImageIcon(scaledImg);
                return icon;
            } else {
                return new ImageIcon(img);
            }
        } catch (IOException e) {
            LOGGER.log(Level.WARNING, "Failed to load icon: " + iconName, e);
            return null;
        }
    }
    
    /**
     * 清除图标缓存
     */
    public static void clearCache() {
        ICON_CACHE.clear();
    }
} 