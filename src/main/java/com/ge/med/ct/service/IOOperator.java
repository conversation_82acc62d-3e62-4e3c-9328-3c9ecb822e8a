package com.ge.med.ct.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.logging.Logger;

/**
 * 文件系统工具类
 * 提供文件操作相关的辅助方法
 */
public class IOOperator {
    private static final Logger LOG = LogManager.getInstance().getLogger(IOOperator.class);

    /**
     * 创建目录
     */
    public static void createDirectories(String directoryPath) {
        try {
            Path path = Paths.get(directoryPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                LOG.info("创建目录: " + path);
            }
        } catch (IOException e) {
            LOG.severe("创建目录失败: " + directoryPath + ", 原因: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean fileExists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }

        try {
            return Files.exists(Paths.get(filePath));
        } catch (Exception e) {
            LOG.warning("检查文件存在性时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查路径是否为目录
     *
     * @param directoryPath 目录路径
     * @return 是否为目录
     */
    public static boolean isDirectory(String directoryPath) {
        if (directoryPath == null || directoryPath.trim().isEmpty()) {
            return false;
        }

        try {
            return Files.isDirectory(Paths.get(directoryPath));
        } catch (Exception e) {
            LOG.warning("检查目录时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建目录，如果不存在
     *
     * @param directoryPath 目录路径
     * @return 是否成功创建或已存在
     */
    public static boolean createDirectoryIfNotExists(String directoryPath) {
        if (directoryPath == null || directoryPath.trim().isEmpty()) {
            return false;
        }

        try {
            Path path = Paths.get(directoryPath);
            if (Files.exists(path)) {
                return Files.isDirectory(path);
            }

            Files.createDirectories(path);
            return true;
        } catch (IOException e) {
            LOG.warning("创建目录时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取文件大小
     *
     * @param filePath 文件路径
     * @return 文件大小（字节），-1表示错误
     */
    public static long getFileSize(String filePath) {
        if (!fileExists(filePath)) {
            return -1;
        }

        try {
            return Files.size(Paths.get(filePath));
        } catch (IOException e) {
            LOG.warning("获取文件大小时出错: " + e.getMessage());
            return -1;
        }
    }

    /**
     * 检查文件是否有效
     *
     * @param filePath 文件路径
     * @param minSize  最小文件大小
     * @return 是否有效
     */
    public static boolean isFileValid(String filePath, long minSize) {
        if (!fileExists(filePath)) {
            return false;
        }

        // 如果是检查目录是否有效，忽略大小检查
        if (isDirectory(filePath)) {
            return minSize == 0;
        }

        // 检查文件大小
        long size = getFileSize(filePath);
        return size >= minSize;
    }

    /**
     * 获取文件名（不含路径）
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    public static String getFileName(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }

        try {
            return Paths.get(filePath).getFileName().toString();
        } catch (Exception e) {
            LOG.warning("获取文件名时出错: " + e.getMessage());
            return "";
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filePath 文件路径
     * @return 扩展名
     */
    public static String getFileExtension(String filePath) {
        String fileName = getFileName(filePath);
        int dotIndex = fileName.lastIndexOf('.');

        if (dotIndex < 0 || dotIndex == fileName.length() - 1) {
            return "";
        }

        return fileName.substring(dotIndex + 1);
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return 是否成功删除
     */
    public static boolean deleteFile(String filePath) {
        if (!fileExists(filePath)) {
            return false;
        }

        try {
            return Files.deleteIfExists(Paths.get(filePath));
        } catch (IOException e) {
            LOG.warning("删除文件时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 移动文件
     *
     * @param sourcePath 源路径
     * @param targetPath 目标路径
     * @return 是否成功移动
     */
    public static boolean moveFile(String sourcePath, String targetPath) {
        if (!fileExists(sourcePath)) {
            return false;
        }

        try {
            Files.move(Paths.get(sourcePath), Paths.get(targetPath));
            return true;
        } catch (IOException e) {
            LOG.warning("移动文件时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 复制文件
     *
     * @param sourcePath 源路径
     * @param targetPath 目标路径
     * @return 是否成功复制
     */
    public static boolean copyFile(String sourcePath, String targetPath) {
        if (!fileExists(sourcePath)) {
            return false;
        }

        try {
            Files.copy(Paths.get(sourcePath), Paths.get(targetPath));
            return true;
        } catch (IOException e) {
            LOG.warning("复制文件时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 确保日志文件具有正确的权限并清理旧日志
     * 
     * @param logDir 日志目录路径
     */
    public static void ensureLogPermissions(String logDir) {
        if (!PlatformUtils.isWindows()) {
            try {
                // 确保目录存在
                File dir = new File(logDir);
                if (!dir.exists()) {
                    if (dir.mkdirs()) {
                        System.out.println("Created log directory: " + logDir);
                    } else {
                        System.out.println("Failed to create log directory: " + logDir);
                        return;
                    }
                }
                
                // 设置目录权限
                ProcessBuilder chmodDirBuilder = new ProcessBuilder("chmod", "755", logDir);
                chmodDirBuilder.start().waitFor();
                
                // 清理旧日志文件（超过7天）
                final long RETENTION_PERIOD = 7 * 24 * 60 * 60 * 1000L; // 7天的毫秒数
                final long now = System.currentTimeMillis();
                final String LOG_PREFIX = "qatool";
                
                File[] logFiles = dir.listFiles((d, name) -> 
                    name.startsWith(LOG_PREFIX) && name.endsWith(".log"));
                    
                if (logFiles != null) {
                    for (File logFile : logFiles) {
                        if ((now - logFile.lastModified()) > RETENTION_PERIOD) {
                            if (logFile.delete()) {
                                System.out.println("Deleted old log file: " + logFile.getName());
                            }
                        } else {
                            // 为保留的日志文件设置正确权限
                            ProcessBuilder chmodBuilder = new ProcessBuilder(
                                "chmod", "644", logFile.getAbsolutePath());
                            chmodBuilder.start().waitFor();
                        }
                    }
                }
            } catch (Exception e) {
                System.err.println("Error managing logs: " + e.getMessage());
            }
        }
    }

}