package com.ge.med.ct.service;

import javax.swing.*;

import com.ge.med.ct.analysis.model.AnalysisState;

import java.util.logging.Logger;

public class StatusIconProvider {
    private static final Logger logger = LogManager.getInstance().getLogger(StatusIconProvider.class);
    private static final String ICON_PATH = "/assets/icons/";

    public static ImageIcon getStatusIcon(AnalysisState status) {
        String iconName = getIconName(status);
        try {
            return new ImageIcon(StatusIconProvider.class.getResource(ICON_PATH + iconName));
        } catch (Exception e) {
            logger.warning("Failed to load icon: " + iconName);
            return null;
        }
    }

    private static String getIconName(AnalysisState status) {
        switch (status) {
            case PASS:
                return "pass.png";
            case FAIL:
                return "fail.png";
            case IN_PROGRESS:
                return "process.png";
            case WAITING:
                return "wait.png";
            default:
                return "wait.png";
        }
    }
}