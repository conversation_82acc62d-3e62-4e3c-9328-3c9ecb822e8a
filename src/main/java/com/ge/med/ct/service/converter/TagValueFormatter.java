package com.ge.med.ct.service.converter;

import com.ge.med.ct.exception.message.DicomMessages;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import static com.ge.med.ct.dicom2.tag.DicomTagConstants.CT.*;
import static com.ge.med.ct.dicom2.tag.DicomTagConstants.Image.SLICE_THICKNESS;

/**
 * 标签值格式化工具
 * 负责将DICOM标签值转换为适合显示的格式
 */
public class TagValueFormatter {
    private static final Logger LOG = Logger.getLogger(TagValueFormatter.class.getName());
    private final Map<String, String> formatPatterns;

    public TagValueFormatter() {
        this.formatPatterns = initFormatPatterns();
    }

    private Map<String, String> initFormatPatterns() {
        Map<String, String> patterns = new HashMap<>();

        // 数值格式化模式
        patterns.put(SLICE_THICKNESS, "%.3f");
        patterns.put(GANTRY_TILT, "%.1f");
        patterns.put(FIELD_OF_VIEW, "%.1f");
        patterns.put(RECONSTRUCTION_DIAMETER, "%.1f");

        return patterns;
    }

    /**
     * 格式化标签值
     *
     * @param tagId 标签ID
     * @param value 标签值
     * @return 格式化后的值
     */
    public String format(String tagId, String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }

        String pattern = formatPatterns.get(tagId);
        if (pattern != null) {
            return formatFloat(value, pattern);
        }

        return value;
    }

    /**
     * 格式化日期
     *
     * @param value DICOM日期字符串
     * @return 格式化后的日期字符串
     */
    public String formatDate(String value) {
        if (value == null || value.length() < 8) {
            return value;
        }
        String year = value.substring(0, 4);
        String month = value.substring(4, 6);
        String day = value.substring(6, 8);
        return String.format("%s %s %s", getMonthAbbrev(month), day, year.substring(2));
    }

    private String formatFloat(String value, String pattern) {
        if (value == null)
            return "";
        try {
            float floatValue = Float.parseFloat(value);
            return String.format(pattern, floatValue);
        } catch (NumberFormatException e) {
            LOG.warning(DicomMessages.VALIDATION_ERROR.format("数值转换失败", value) + ": " + e.getMessage());
            return value;
        }
    }

    private String getMonthAbbrev(String month) {
        switch (month) {
            case "01":
                return "Jan";
            case "02":
                return "Feb";
            case "03":
                return "Mar";
            case "04":
                return "Apr";
            case "05":
                return "May";
            case "06":
                return "Jun";
            case "07":
                return "Jul";
            case "08":
                return "Aug";
            case "09":
                return "Sep";
            case "10":
                return "Oct";
            case "11":
                return "Nov";
            case "12":
                return "Dec";
            default:
                return month;
        }
    }
}