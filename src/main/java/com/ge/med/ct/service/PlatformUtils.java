package com.ge.med.ct.service;

/**
 * 平台工具类
 * 提供检测操作系统类型和处理平台相关特性的工具方法
 */
public final class PlatformUtils {
    
    private static final String OS_NAME = System.getProperty("os.name").toLowerCase();
    private static final boolean IS_WINDOWS = OS_NAME.contains("win");
    private static final boolean IS_LINUX = OS_NAME.contains("linux") || OS_NAME.contains("unix");
    private static final boolean IS_MAC = OS_NAME.contains("mac");
    
    /**
     * 私有构造函数，防止实例化
     */
    private PlatformUtils() {
        throw new AssertionError("工具类不应被实例化");
    }
    
    /**
     * 检查当前操作系统是否为Windows
     * 
     * @return 如果是Windows系统返回true，否则返回false
     */
    public static boolean isWindows() {
        return IS_WINDOWS;
    }
    
    /**
     * 检查当前操作系统是否为Linux或Unix
     * 
     * @return 如果是Linux或Unix系统返回true，否则返回false
     */
    public static boolean isLinux() {
        return IS_LINUX;
    }
    
    /**
     * 检查当前操作系统是否为Mac
     * 
     * @return 如果是Mac系统返回true，否则返回false
     */
    public static boolean isMac() {
        return IS_MAC;
    }
    
    /**
     * 获取平台特定的路径分隔符
     * 
     * @return 路径分隔符
     */
    public static String getPathSeparator() {
        return System.getProperty("file.separator");
    }
    
    /**
     * 获取平台特定的行分隔符
     * 
     * @return 行分隔符
     */
    public static String getLineSeparator() {
        return System.getProperty("line.separator");
    }
    
    /**
     * 根据当前平台，从Windows和Linux两个选项中选择适当的值
     * 
     * @param windowsValue Windows平台下的值
     * @param linuxValue Linux/Unix平台下的值
     * @return 适合当前平台的值
     */
    public static String getPlatformSpecificValue(String windowsValue, String linuxValue) {
        if (IS_WINDOWS) {
            return windowsValue;
        } else if (IS_LINUX) {
            return linuxValue;
        } else if (IS_MAC) {
            return linuxValue; // 对Mac使用与Linux相同的值，如有必要可以增加Mac特定的参数
        } else {
            return linuxValue; // 默认返回Linux值
        }
    }
} 