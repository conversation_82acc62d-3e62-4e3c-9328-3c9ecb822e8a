package com.ge.med.ct.service;

import javax.swing.*;
import javax.swing.table.TableCellRenderer;
import javax.swing.table.TableColumn;
import javax.swing.table.TableColumnModel;
import java.awt.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import com.ge.med.ct.laf2.theming.ThemeConstants;

/**
 * 表格操作工具类
 * 提供表格列宽自动调整功能和列编号功能
 */
public class TableOperations {
    private static final int MIN_COLUMN_WIDTH = 50;
    private static final int PADDING = 20;
    private static final int MAX_COLUMN_WIDTH = 300; // 添加最大列宽限制
    private static final AtomicBoolean isShutdown = new AtomicBoolean(false);
    private static final ExecutorService executor = Executors.newSingleThreadExecutor(r -> {
        Thread thread = new Thread(r, "TableAdjustThread");
        thread.setDaemon(true); // 设置为守护线程，避免阻止JVM退出
        return thread;
    });

    private TableOperations() {
        throw new AssertionError("Utility class should not be instantiated");
    }

    /**
     * 关闭线程池
     * 应在应用程序退出时调用
     */
    public static void shutdown() {
        if (isShutdown.compareAndSet(false, true)) {
            executor.shutdown();
        }
    }

    /**
     * 异步调整表格所有列的宽度
     * @param table 要调整的表格
     */
    public static void adjustColumnWidthsAsync(JTable table) {
        if (table == null || table.getColumnCount() == 0 || table.getModel() == null || isShutdown.get()) {
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                // 先应用列编号
                applyColumnNumbers(table);

                // 然后调整列宽
                for (int i = 0; i < table.getColumnCount(); i++) {
                    if (isShutdown.get()) {
                        return; // 如果已经关闭，则立即返回
                    }
                    final int column = i;
                    SwingUtilities.invokeLater(() -> adjustColumnWidth(table, column));
                }
            } catch (Exception e) {
                // 捕获并记录异常，避免异步任务失败
                System.err.println("Error adjusting column widths: " + e.getMessage());
            }
        }, executor);
    }

    /**
     * 应用列编号
     * 在表格头部显示列编号
     * @param table 表格
     */
    private static void applyColumnNumbers(JTable table) {
        if (table == null || table.getColumnCount() == 0 || isShutdown.get()) {
            return;
        }

        try {
            SwingUtilities.invokeLater(() -> {
                try {
                    if (isShutdown.get() || table.getColumnCount() == 0) {
                        return;
                    }

                    TableColumnModel columnModel = table.getColumnModel();
                    for (int i = 0; i < columnModel.getColumnCount(); i++) {
                        TableColumn column = columnModel.getColumn(i);
                        Object headerValue = column.getHeaderValue();
                        if (headerValue != null) {
                            String headerText = headerValue.toString();
                            // 如果列标题不以数字开头，添加列编号
                            if (!headerText.matches("^\\d+\\..*")) {
                                column.setHeaderValue((i + 1) + ". " + headerText);
                            }
                        }
                    }

                    // 刷新表头
                    if (table.getTableHeader() != null) {
                        table.getTableHeader().repaint();
                    }
                } catch (Exception e) {
                    System.err.println("Error applying column numbers: " + e.getMessage());
                }
            });
        } catch (Exception e) {
            System.err.println("Error scheduling column numbers update: " + e.getMessage());
        }
    }

    /**
     * 调整指定列的宽度
     * @param table 表格
     * @param column 列索引
     */
    public static void adjustColumnWidth(JTable table, int column) {
        if (table == null || column < 0 || column >= table.getColumnCount() || isShutdown.get()) {
            return;
        }

        try {
            // 获取列对象
            TableColumn tableColumn = table.getColumnModel().getColumn(column);

            // 计算列标题宽度
            int headerWidth = calculateHeaderWidth(table, column);

            // 计算列内容最大宽度
            int contentWidth = calculateContentWidth(table, column);

            // 取最大值作为列宽，并限制最大宽度
            int preferredWidth = Math.min(
                Math.max(Math.max(headerWidth, contentWidth) + PADDING, MIN_COLUMN_WIDTH),
                MAX_COLUMN_WIDTH
            );

            // 设置列宽
            tableColumn.setPreferredWidth(preferredWidth);
            tableColumn.setWidth(preferredWidth);

            // 更新UI
            updateTableUI(table);
        } catch (Exception e) {
            System.err.println("Error adjusting column width for column " + column + ": " + e.getMessage());
        }
    }

    /**
     * 计算列标题宽度
     */
    private static int calculateHeaderWidth(JTable table, int column) {
        if (table.getTableHeader() == null || isShutdown.get()) {
            return MIN_COLUMN_WIDTH;
        }

        try {
            TableCellRenderer headerRenderer = table.getTableHeader().getDefaultRenderer();
            if (headerRenderer == null) {
                return MIN_COLUMN_WIDTH;
            }

            Object headerValue = table.getColumnModel().getColumn(column).getHeaderValue();
            if (headerValue == null) {
                return MIN_COLUMN_WIDTH;
            }

            Component headerComp = headerRenderer.getTableCellRendererComponent(
                    table, headerValue, false, false, 0, column);
            if (headerComp == null) {
                return MIN_COLUMN_WIDTH;
            }

            return headerComp.getPreferredSize().width;
        } catch (Exception e) {
            System.err.println("Error calculating header width: " + e.getMessage());
            return MIN_COLUMN_WIDTH;
        }
    }

    /**
     * 计算列内容最大宽度
     */
    private static int calculateContentWidth(JTable table, int column) {
        if (isShutdown.get()) {
            return MIN_COLUMN_WIDTH;
        }

        int rowCount = table.getRowCount();
        if (rowCount == 0) {
            return MIN_COLUMN_WIDTH;
        }

        try {
            // 获取列渲染器
            TableCellRenderer renderer = table.getColumnModel().getColumn(column).getCellRenderer();
            if (renderer == null) {
                renderer = table.getDefaultRenderer(table.getColumnClass(column));
                if (renderer == null) {
                    return MIN_COLUMN_WIDTH;
                }
            }

            // 获取字体度量
            Font font = ThemeConstants.Fonts.getTableContent();
            if (font == null) {
                font = table.getFont();
                if (font == null) {
                    return MIN_COLUMN_WIDTH;
                }
            }
            FontMetrics fontMetrics = table.getFontMetrics(font);

            // 计算最大宽度
            int maxWidth = 0;
            // 限制检查的行数，避免大表格导致性能问题
            int maxRowsToCheck = Math.min(rowCount, 100);
            for (int row = 0; row < maxRowsToCheck; row++) {
                if (isShutdown.get()) {
                    return MIN_COLUMN_WIDTH;
                }

                try {
                    Object value = table.getValueAt(row, column);
                    if (value != null) {
                        // 计算文本宽度
                        String text = value.toString();
                        int textWidth = fontMetrics.stringWidth(text);

                        // 计算组件宽度
                        Component comp = renderer.getTableCellRendererComponent(
                                table, value, false, false, row, column);
                        int componentWidth = comp != null ? comp.getPreferredSize().width : 0;

                        // 取最大值
                        maxWidth = Math.max(maxWidth, Math.max(textWidth, componentWidth));
                    }
                } catch (Exception e) {
                    // 忽略单行处理错误，继续处理其他行
                    System.err.println("Error processing row " + row + ": " + e.getMessage());
                }
            }

            return maxWidth;
        } catch (Exception e) {
            System.err.println("Error calculating content width: " + e.getMessage());
            return MIN_COLUMN_WIDTH;
        }
    }

    /**
     * 更新表格UI
     */
    private static void updateTableUI(JTable table) {
        if (isShutdown.get()) {
            return;
        }

        try {
            if (table.getParent() instanceof JViewport) {
                JViewport viewport = (JViewport) table.getParent();
                if (viewport.getParent() instanceof JScrollPane) {
                    JScrollPane scrollPane = (JScrollPane) viewport.getParent();
                    scrollPane.revalidate();
                }
            }
            table.revalidate();
            table.repaint();
        } catch (Exception e) {
            System.err.println("Error updating table UI: " + e.getMessage());
        }
    }

    /**
     * 检查线程池是否已关闭
     * @return 如果线程池已关闭则返回true
     */
    public static boolean isShutdown() {
        return isShutdown.get();
    }
}