package com.ge.med.ct.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

public class ImgLSInvoker {
    private static final String SCRIPT_RESOURCE_PATH = "/scripts/imgLSDAT.pl";
    private static final String PERL_PATH = System.getProperty("perl.path", "perl");
    
    private static String getScriptPath() throws IOException {
        // Get the script from resources and copy to a temporary file
        URL resourceUrl = ImgLSInvoker.class.getResource(SCRIPT_RESOURCE_PATH);
        if (resourceUrl == null) {
            throw new IOException("Script resource not found: " + SCRIPT_RESOURCE_PATH);
        }
        
        Path tempScript = Files.createTempFile("imgLSDAT", ".pl");
        try (InputStream in = resourceUrl.openStream()) {
            Files.copy(in, tempScript, StandardCopyOption.REPLACE_EXISTING);
        }
        
        // Make the temporary script executable on Unix-like systems
        File scriptFile = tempScript.toFile();
        scriptFile.setExecutable(true);
        
        return tempScript.toString();
    }
    
    public static String runAndGetResult(String command) {
        StringBuilder output = new StringBuilder();
        
        try {
            String scriptPath = getScriptPath();
            
            String[] cmdArgs = {
                    PERL_PATH,
                    scriptPath,
                    command
            };
            
            ProcessBuilder processBuilder = new ProcessBuilder(cmdArgs);
            
            Process process = processBuilder.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            while ((line = errorReader.readLine()) != null) {
                output.append("ERROR: ").append(line).append("\n");
            }
            
            int exitCode = process.waitFor();
            output.append("Exit code: ").append(exitCode);
            
            return output.toString();
        } catch (IOException | InterruptedException e) {
            return "Error executing command: " + e.getMessage();
        }
    }
    
    public static String run(String command) {
        String result = runAndGetResult(command);
        System.out.println(result);
        return result;
    }
}