package com.ge.med.ct.service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 服务管理器，用于统一管理应用中的各类服务
 */
public class ServiceManager {
    private static final Logger LOGGER = Logger.getLogger(ServiceManager.class.getName());
    private static final ServiceManager INSTANCE = new ServiceManager();
    
    // 存储注册的服务
    private final Map<Class<?>, Object> services = new HashMap<>();
    
    private ServiceManager() {
        // 私有构造函数防止外部实例化
    }
    
    /**
     * 获取ServiceManager实例
     * 
     * @return ServiceManager单例实例
     */
    public static ServiceManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 注册服务
     * 
     * @param <T> 服务类型
     * @param serviceClass 服务类的Class对象
     * @param serviceInstance 服务实例
     * @return 当前ServiceManager实例，用于链式调用
     */
    public <T> ServiceManager registerService(Class<T> serviceClass, T serviceInstance) {
        if (serviceClass == null || serviceInstance == null) {
            throw new IllegalArgumentException("Service class and instance cannot be null");
        }
        
        if (services.containsKey(serviceClass)) {
            LOGGER.warning("Service " + serviceClass.getName() + " is already registered, replacing");
        }
        
        services.put(serviceClass, serviceInstance);
        LOGGER.info("Registered service: " + serviceClass.getName());
        return this;
    }
    
    /**
     * 获取已注册的服务
     * 
     * @param <T> 服务类型
     * @param serviceClass 服务类的Class对象
     * @return 服务实例，如果服务未注册则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getService(Class<T> serviceClass) {
        if (serviceClass == null) {
            throw new IllegalArgumentException("Service class cannot be null");
        }
        
        Object service = services.get(serviceClass);
        if (service == null) {
            LOGGER.warning("Service not found: " + serviceClass.getName());
            return null;
        }
        
        return (T) service;
    }
    
    /**
     * 检查服务是否已注册
     * 
     * @param serviceClass 服务类的Class对象
     * @return 如果服务已注册则返回true
     */
    public boolean hasService(Class<?> serviceClass) {
        return services.containsKey(serviceClass);
    }
    
    /**
     * 移除已注册的服务
     * 
     * @param serviceClass 服务类的Class对象
     * @return 如果服务已被移除则返回true
     */
    public boolean removeService(Class<?> serviceClass) {
        if (services.remove(serviceClass) != null) {
            LOGGER.info("Removed service: " + serviceClass.getName());
            return true;
        }
        return false;
    }
    
    /**
     * 清除所有注册的服务
     */
    public void clearServices() {
        services.clear();
        LOGGER.info("Cleared all services");
    }
    
    /**
     * 获取已注册服务的数量
     * 
     * @return 已注册服务的数量
     */
    public int getServiceCount() {
        return services.size();
    }
} 