package com.ge.med.ct.service.converter;

import com.ge.med.ct.dcm_se.core.cfg.table.TableColumnManager;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.exception.core.QAToolException;

import static com.ge.med.ct.dicom2.tag.DicomTagConstants.Patient.PATIENT_ID;

/**
 * 检查数据转换器
 * 负责将DicomExam对象的数据转换为表格显示所需的字符串格式
 */
public class ExamDataConverter implements DataConverter<DicomExam> {
    private final TagValueFormatter tagValueFormatter;
    private final TableColumnManager tableColumnManager;

    public ExamDataConverter(TagValueFormatter tagValueFormatter, TableColumnManager tableColumnManager) {
        this.tagValueFormatter = tagValueFormatter;
        this.tableColumnManager = tableColumnManager;
    }

    @Override
    public String getTagValue(DicomExam exam, String columnName, String tableType) throws QAToolException {
        if (exam == null)
            return "";

        // 获取标签ID
        String tagId = tableColumnManager.resolveTagId(tableType, columnName);

        // 处理特殊列
        if ("special".equals(tagId)) {
            // 根据列名判断具体的特殊列类型
            return "";
        }

        if (tagId.isEmpty())
            return "";

        String tagValue = exam.getTagValue(tagId);
        if (tagValue == null || tagValue.isEmpty())
            return "";

        // 特殊列处理
        if ("StudyDate".equals(columnName)) {
            return tagValueFormatter.formatDate(tagValue);
        } else if ("PatientID".equals(columnName)) {
            return getPatientId(exam);
        } else {
            return tagValueFormatter.format(tagId, tagValue);
        }
    }

    private String getPatientId(DicomExam exam) {
        String patientId = exam.getTagValue(PATIENT_ID);
        return patientId != null && !patientId.isEmpty() ? patientId : exam.getPatientID();
    }
}