package com.ge.med.ct.service;

import com.ge.med.ct.dcm_se.core.cfg.ConfigManager;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;

import java.io.File;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.*;

/**
 * 日志管理器，负责创建和管理日志实例
 * 使用Java标准日志（java.util.logging）
 */
@HandleException(errorCode = ErrorCode.INIT)
public class LogManager {
    private static volatile LogManager instance;
    private final Map<String, Logger> loggers = new HashMap<>();
    private FileHandler fileHandler;
    private Level logLevel = Level.INFO;
    private String logFilePath;
    
    // 日志常量
    private static final String LOG_PREFIX = "qatool";
    private static final String LOG_EXT = ".log";
    private static final int LOG_RETENTION_DAYS = 7;  // 与Perl脚本一致
    
    // 防止重复初始化
    private static boolean initialized = false;

    private LogManager() {
        if (!initialized) {
            initLogManager();
            initialized = true;
        }
    }

    @HandleException(errorCode = ErrorCode.INIT)
    private void initLogManager() {
        // 尝试从配置中读取日志级别
        ConfigManager config = ConfigManager.getInstance();
        String configLevel = config.getLogLevel();
        if (configLevel != null && !configLevel.isEmpty()) {
            try {
                logLevel = Level.parse(configLevel.toUpperCase());
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid log level: " + configLevel + ", using default: INFO");
            }
        }

        // 尝试从配置中读取日志目录
        String configDir = config.getString("log.dir.path", null);
        String logDir;
        
        if (configDir != null) {
            logDir = configDir;
        } else if (PlatformUtils.isWindows()) {
            logDir = "./logs";
        } else {
            // Linux环境下使用与runqat脚本相同的日志目录
            logDir = "/usr/g/bin/qatdemo/logs";
        }
        
        // 使用日期命名日志文件，与Perl脚本保持一致
        String today = new SimpleDateFormat("yyyyMMdd").format(new Date());
        // 添加JVM进程名称作为唯一标识符，避免并发冲突
        String processId = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
        String logFileName = LOG_PREFIX + "_" + today + "_" + processId + LOG_EXT;
        
        // 构建完整的日志文件路径
        logFilePath = logDir + File.separator + logFileName;
        
        System.out.println("Log file path: " + logFilePath);
        
        // 确保日志目录存在
        File logDirFile = new File(logDir);
        if (!logDirFile.exists()) {
            logDirFile.mkdirs();
        }

        // 清理旧日志并确保权限
        cleanupOldLogs(logDir);
        IOOperator.ensureLogPermissions(logDir);

        // 配置 FileHandler - 使用append=true确保不覆盖现有内容
        try {
            // 关闭已有的FileHandler
            if (fileHandler != null) {
                fileHandler.close();
            }
            
            // 创建不会自动增加后缀的FileHandler
            fileHandler = new FileHandler(logFilePath, 10_000_000, 1, true);
            fileHandler.setFormatter(new SimpleFormatter() {
                @Override
                public String format(LogRecord record) {
                    // 自定义日志格式，添加时间戳和日志级别
                    SimpleDateFormat logTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                    String timestamp = logTime.format(new Date(record.getMillis()));
                    return timestamp + " [" + record.getLevel() + "] " 
                           + record.getLoggerName() + ": " 
                           + record.getMessage() + "\n";
                }
            });
            fileHandler.setLevel(logLevel);

            // 配置根日志记录器
            Logger rootLogger = Logger.getLogger("");
            rootLogger.setLevel(logLevel);

            // 移除默认处理器
            for (Handler handler : rootLogger.getHandlers()) {
                rootLogger.removeHandler(handler);
            }

            // 添加控制台处理器和文件处理器
            ConsoleHandler consoleHandler = new ConsoleHandler();
            consoleHandler.setLevel(logLevel);
            rootLogger.addHandler(consoleHandler);
            rootLogger.addHandler(fileHandler);

            // 记录初始化成功
            Logger initLogger = getLogger(LogManager.class);
            initLogger.info("日志系统初始化成功，日志级别: " + logLevel + ", 日志文件: " + logFilePath);
        } catch (IOException e) {
            // 在日志系统初始化失败时，只能使用标准错误输出
            System.err.println("Failed to initialize logger handler: " + e.getMessage());
            throw new RuntimeException("日志系统初始化失败", e);
        }
    }

    /**
     * 清理旧的日志文件
     * 
     * @param logDir 日志目录
     */
    private void cleanupOldLogs(String logDir) {
        try {
            File dir = new File(logDir);
            if (dir.exists() && dir.isDirectory()) {
                // 当前时间减去保留天数
                long cutoffTime = System.currentTimeMillis() - (LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000L);
                
                File[] oldLogs = dir.listFiles((d, name) -> 
                    name.startsWith(LOG_PREFIX) && name.endsWith(LOG_EXT));
                
                if (oldLogs != null) {
                    for (File logFile : oldLogs) {
                        if (logFile.lastModified() < cutoffTime) {
                            if (logFile.delete()) {
                                System.out.println("已删除旧日志: " + logFile.getName());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("清理旧日志失败: " + e.getMessage());
        }
    }

    public static LogManager getInstance() {
        if (instance == null) {
            synchronized (LogManager.class) {
                if (instance == null) {
                    instance = new LogManager();
                }
            }
        }
        return instance;
    }

    /**
     * 获取指定类的Logger实例
     *
     * @param clazz 类
     * @return Logger实例
     */
    public Logger getLogger(Class<?> clazz) {
        return getLogger(clazz.getName());
    }

    /**
     * 获取指定名称的Logger实例
     *
     * @param name 日志名称
     * @return Logger实例
     */
    public Logger getLogger(String name) {
        if (!loggers.containsKey(name)) {
            Logger logger = Logger.getLogger(name);
            logger.setLevel(logLevel);
            loggers.put(name, logger);
        }
        return loggers.get(name);
    }

    /**
     * 设置日志级别
     *
     * @param level 日志级别
     */
    public void setLogLevel(Level level) {
        this.logLevel = level;
        fileHandler.setLevel(level);

        // 更新所有已创建的日志记录器
        for (Logger logger : loggers.values()) {
            logger.setLevel(level);
        }

        // 更新根日志记录器
        Logger.getLogger("").setLevel(level);
    }

    /**
     * 获取当前日志级别
     *
     * @return 日志级别
     */
    public Level getLogLevel() {
        return logLevel;
    }

    /**
     * 获取日志文件路径
     *
     * @return 日志文件路径
     */
    public String getLogFilePath() {
        return logFilePath;
    }
}