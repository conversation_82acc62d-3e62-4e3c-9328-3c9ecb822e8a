package com.ge.med.ct.service.converter;

import com.ge.med.ct.dcm_se.core.cfg.table.TableColumnManager;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.exception.core.QAToolException;

/**
 * 数据转换器接口
 * 负责将DICOM数据转换为表格显示所需的字符串格式
 */
public interface DataConverter<T> {

    /**
     * 获取指定列的标签值
     *
     * @param data       数据对象
     * @param columnName 列名
     * @param tableType  表格类型
     * @return 格式化后的标签值
     * @throws QAToolException 转换失败时抛出异常
     */
    String getTagValue(T data, String columnName, String tableType) throws QAToolException;

    /**
     * 创建检查数据转换器
     */
    static DataConverter<DicomExam> createExamConverter(TableColumnManager tableColumnManager) {
        TagValueFormatter tagValueFormatter = new TagValueFormatter();
        return new ExamDataConverter(tagValueFormatter, tableColumnManager);
    }

    /**
     * 创建序列数据转换器
     */
    static DataConverter<DicomSeries> createSeriesConverter(TableColumnManager tableColumnManager) {
        TagValueFormatter tagValueFormatter = new TagValueFormatter();
        return new SeriesDataConverter(tagValueFormatter, tableColumnManager);
    }

    /**
     * 创建图像数据转换器
     */
    static DataConverter<DicomImage> createImageConverter(TableColumnManager tableColumnManager) {
        TagValueFormatter tagValueFormatter = new TagValueFormatter();
        return new ImageDataConverter(tagValueFormatter, tableColumnManager);
    }
}