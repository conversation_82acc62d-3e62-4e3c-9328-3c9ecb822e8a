package com.ge.med.ct.service;

import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.CommonMessages;

import java.util.concurrent.Callable;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 重试策略
 * 提供重试机制，用于处理可能暂时失败的操作
 */
@HandleException(errorCode = ErrorCode.OPERATION)
public final class RetryStrategy {
    private static final Logger LOG = LogManager.getInstance().getLogger(RetryStrategy.class);

    private RetryStrategy() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 使用默认参数执行重试操作
     *
     * @param <T>           返回值类型
     * @param operation     要执行的操作
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T retry(Callable<T> operation, String operationName) throws Exception {
        return retry(operation, operationName, 3, 1000, e -> true);
    }

    /**
     * 执行重试操作
     *
     * @param <T>           返回值类型
     * @param operation     要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param maxAttempts   最大尝试次数
     * @param delayMs       重试间隔（毫秒）
     * @param retryOn       决定哪些异常应该重试的谓词
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T retry(
            Callable<T> operation,
            String operationName,
            int maxAttempts,
            long delayMs,
            Predicate<Exception> retryOn) throws Exception {

        int attempts = 0;
        Exception lastException = null;

        while (attempts < maxAttempts) {
            try {
                attempts++;
                return operation.call();
            } catch (InterruptedException e) {
                // 对于中断异常，不重试，直接抛出
                Thread.currentThread().interrupt();
                throw e;
            } catch (Exception e) {
                lastException = e;

                if (!retryOn.test(e)) {
                    LOG.log(Level.WARNING, "操作 {0} 失败，异常不符合重试条件，直接抛出", operationName);
                    throw e;
                }

                if (attempts >= maxAttempts) {
                    LOG.log(Level.WARNING, "操作 {0} 在 {1} 次尝试后失败", new Object[] { operationName, maxAttempts });
                    throw e;
                }

                LOG.log(Level.INFO, "操作 {0} 失败，将在 {1}ms 后进行第 {2}/{3} 次重试: {4}",
                        new Object[] { operationName, delayMs, attempts + 1, maxAttempts, e.getMessage() });

                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw ExceptionFactory.createException(ErrorCode.OPERATION,
                            CommonMessages.THREAD_INTERRUPTED, ie, "重试等待被中断");
                }
            }
        }

        // 这里通常不会到达，因为最后一次失败会在循环中抛出异常
        throw ExceptionFactory.createException(ErrorCode.OPERATION,
                CommonMessages.PROCESSING_ERROR, lastException, "所有重试尝试都失败: " + operationName);
    }

    /**
     * 执行不返回值的重试操作
     *
     * @param operation     要执行的操作
     * @param operationName 操作名称（用于日志）
     * @throws Exception 如果所有重试都失败
     */
    public static void retryVoid(RunnableWithException operation, String operationName) throws Exception {
        retry(() -> {
            operation.run();
            return null;
        }, operationName);
    }

    /**
     * 可能抛出异常的Runnable接口
     */
    @FunctionalInterface
    public interface RunnableWithException {
        void run() throws Exception;
    }
}
