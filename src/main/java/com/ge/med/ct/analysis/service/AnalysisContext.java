package com.ge.med.ct.analysis.service;

import com.ge.med.ct.analysis.model.AnalysisParams;
import com.ge.med.ct.analysis.model.AnalysisResult;
import com.ge.med.ct.analysis.model.AnalysisState;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 分析上下文类
 * 用于管理分析过程中的状态和数据
 */
public class AnalysisContext {
    private static final Logger LOGGER = Logger.getLogger(AnalysisContext.class.getName());
    
    // 核心属性
    private final AnalysisParams params;
    private volatile AnalysisState state;
    private volatile AnalysisResult result;
    private final Executor executor;
    
    // 执行状态
    private Date startTime;
    private Date endTime;
    private String commandOutput;
    private boolean outputFileExists;
    
    // 钩子函数
    private final List<Consumer<AnalysisContext>> preProcessHooks = new ArrayList<>();
    private final List<Consumer<AnalysisContext>> postProcessHooks = new ArrayList<>();
    
    // 分析指标
    private final Map<String, Object> metrics = new HashMap<>();
    
    /**
     * 创建分析上下文
     * @param params 分析参数
     * @param initialState 初始状态
     * @param executor 执行器
     */
    public AnalysisContext(AnalysisParams params, AnalysisState initialState, Executor executor) {
        this.params = params;
        this.state = initialState;
        this.executor = executor;
    }
    
    /**
     * 获取分析参数
     * @return 分析参数
     */
    public AnalysisParams getParams() {
        return params;
    }
    
    /**
     * 获取分析状态
     * @return 分析状态
     */
    public AnalysisState getState() {
        return state;
    }
    
    /**
     * 设置分析状态
     * @param state 新状态
     */
    public void setState(AnalysisState state) {
        this.state = state;
    }
    
    /**
     * 获取分析结果
     * @return 分析结果
     */
    public AnalysisResult getResult() {
        return result;
    }
    
    /**
     * 设置分析结果
     * @param result 分析结果
     */
    public void setResult(AnalysisResult result) {
        this.result = result;
    }
    
    /**
     * 获取执行器
     * @return 执行器
     */
    public Executor getExecutor() {
        return executor;
    }
    
    /**
     * 获取开始时间
     * @return 开始时间
     */
    public Date getStartTime() {
        return startTime;
    }
    
    /**
     * 设置开始时间
     * @param startTime 开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    /**
     * 获取结束时间
     * @return 结束时间
     */
    public Date getEndTime() {
        return endTime;
    }
    
    /**
     * 设置结束时间
     * @param endTime 结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    /**
     * 获取命令输出
     * @return 命令输出
     */
    public String getCommandOutput() {
        return commandOutput;
    }
    
    /**
     * 设置命令输出
     * @param commandOutput 命令输出
     */
    public void setCommandOutput(String commandOutput) {
        this.commandOutput = commandOutput;
    }
    
    /**
     * 输出文件是否存在
     * @return 如果输出文件存在返回true
     */
    public boolean isOutputFileExists() {
        return outputFileExists;
    }
    
    /**
     * 设置输出文件存在状态
     * @param outputFileExists 输出文件是否存在
     */
    public void setOutputFileExists(boolean outputFileExists) {
        this.outputFileExists = outputFileExists;
    }
    
    /**
     * 添加前置处理钩子
     * @param hook 钩子函数
     */
    public void addPreProcessHook(Consumer<AnalysisContext> hook) {
        if (hook != null) {
            preProcessHooks.add(hook);
        }
    }
    
    /**
     * 添加后置处理钩子
     * @param hook 钩子函数
     */
    public void addPostProcessHook(Consumer<AnalysisContext> hook) {
        if (hook != null) {
            postProcessHooks.add(hook);
        }
    }
    
    /**
     * 执行所有前置处理钩子
     */
    public void executePreProcessHooks() {
        executeHooks(preProcessHooks, "前置处理");
    }
    
    /**
     * 执行所有后置处理钩子
     */
    public void executePostProcessHooks() {
        executeHooks(postProcessHooks, "后置处理");
    }
    
    /**
     * 执行钩子集合
     */
    private void executeHooks(List<Consumer<AnalysisContext>> hooks, String hookType) {
        if (hooks.isEmpty()) {
            return;
        }
        
        for (Consumer<AnalysisContext> hook : hooks) {
            try {
                hook.accept(this);
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, hookType + "钩子执行异常", e);
            }
        }
    }
    
    /**
     * 获取指标
     * @return 指标映射的副本
     */
    public Map<String, Object> getMetrics() {
        return new HashMap<>(metrics);
    }
    
    /**
     * 设置指标
     * @param key 指标名
     * @param value 指标值
     */
    public void setMetric(String key, Object value) {
        if (key != null) {
            metrics.put(key, value);
        }
    }
    
    /**
     * 添加持续时间指标
     */
    public void recordDuration() {
        if (startTime != null && endTime != null) {
            long duration = endTime.getTime() - startTime.getTime();
            setMetric("duration", duration);
        }
    }
} 