package com.ge.med.ct.analysis.model;

/**
 * 分析状态枚举
 * 定义了分析过程中可能的状态
 */
public enum AnalysisState {
    /**
     * 分析通过
     */
    PASS("通过"),
    
    /**
     * 分析失败
     */
    FAIL("失败"),
    
    /**
     * 分析进行中
     */
    IN_PROGRESS("进行中"),
    
    /**
     * 等待执行
     */
    WAITING("等待中");

    private final String description;

    AnalysisState(String description) {
        this.description = description;
    }

    /**
     * 获取状态描述
     * @return 状态的文字描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 从字符串转换为状态枚举
     * @param status 状态字符串
     * @return 状态枚举，如果转换失败则返回FAIL
     */
    public static AnalysisState fromString(String status) {
        if (status == null || status.trim().isEmpty()) {
            return FAIL;
        }
        
        try {
            return valueOf(status.toUpperCase());
        } catch (IllegalArgumentException e) {
            return FAIL;
        }
    }
} 