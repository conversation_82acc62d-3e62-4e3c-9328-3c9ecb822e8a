package com.ge.med.ct.analysis.service;

import com.ge.med.ct.analysis.model.AnalysisResult;
import com.ge.med.ct.analysis.model.AnalysisState;

/**
 * 分析状态回调接口
 * 用于接收分析进度和状态的更新
 */
public interface AnalysisStatusCallback {
    /**
     * 记录日志信息
     * @param level 日志级别
     * @param message 日志消息
     */
    default void onLog(LogLevel level, String message) {
        // 默认将日志转发到状态更新
        switch (level) {
            case ERROR:
                updateStatus("错误: " + message, -1);
                break;
            case WARNING:
                updateStatus("警告: " + message, -1);
                break;
            default:
                onStatus(message);
        }
    }
    
    /**
     * 更新进度
     * @param percentage 进度百分比 (0-100)
     * @param status 当前状态描述
     */
    default void onProgress(int percentage, String status) {
        updateStatus(status, percentage);
    }
    
    /**
     * 状态变更通知
     * @param newState 新的分析状态
     */
    default void onStateChange(AnalysisState newState) {
        updateStatus("状态变更: " + newState.getDescription(), -1);
    }
    
    /**
     * 状态消息回调
     * @param message 状态消息
     */
    void onStatus(String message);
    
    /**
     * 更新状态和进度
     * @param message 状态消息
     * @param progress 进度值(0-100)，-1表示不适用
     */
    void updateStatus(String message, int progress);
    
    /**
     * 分析完成通知
     * @param result 分析结果
     */
    void onComplete(AnalysisResult result);
    
    /**
     * 错误通知
     * @param message 错误消息
     * @param error 错误对象，可能为null
     */
    default void onError(String message, Throwable error) {
        String errorMessage = message;
        if (error != null && error.getMessage() != null) {
            errorMessage += ": " + error.getMessage();
        }
        onLog(LogLevel.ERROR, errorMessage);
    }
    
    /**
     * 创建一个简单的回调实现，只输出日志
     * @param logger 日志输出对象
     * @return 回调实现
     */
    static AnalysisStatusCallback createSimpleLogger(java.util.logging.Logger logger) {
        return new AnalysisStatusCallback() {
            @Override
            public void onStatus(String message) {
                logger.info(message);
            }
            
            @Override
            public void updateStatus(String message, int progress) {
                if (progress >= 0) {
                    logger.info(String.format("[%d%%] %s", progress, message));
                } else {
                    logger.info(message);
                }
            }
            
            @Override
            public void onComplete(AnalysisResult result) {
                logger.info("分析完成: " + result);
            }
        };
    }
    
    /**
     * 日志级别枚举
     */
    enum LogLevel {
        DEBUG, INFO, WARNING, ERROR
    }
} 