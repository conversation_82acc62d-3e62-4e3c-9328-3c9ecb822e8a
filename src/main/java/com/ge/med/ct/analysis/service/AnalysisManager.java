package com.ge.med.ct.analysis.service;

import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.analysis.model.AnalysisParams;
import com.ge.med.ct.analysis.model.AnalysisState;
import com.ge.med.ct.analysis.model.AnalysisResult;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.logging.Logger;

/**
 * 分析管理器
 * 用于创建、管理和监控多个分析任务
 */
public class AnalysisManager {
    private static final Logger LOGGER = Logger.getLogger(AnalysisManager.class.getName());
    private static AnalysisManager instance;
    
    // 存储正在进行的分析
    private final ConcurrentMap<String, Analysis> activeAnalyses = new ConcurrentHashMap<>();
    // 存储已完成的分析结果
    private final List<AnalysisResult> completedResults = new ArrayList<>();
    
    private AnalysisManager() {
        // 私有构造方法，单例模式
    }
    
    /**
     * 获取单例实例
     * @return AnalysisManager单例
     */
    public static synchronized AnalysisManager getInstance() {
        if (instance == null) {
            instance = new AnalysisManager();
        }
        return instance;
    }
    
    /**
     * 创建并启动一个新的分析任务
     * @param params 分析参数
     * @param callback 状态回调
     * @return 分析任务ID
     */
    public String startAnalysis(AnalysisParams params, AnalysisStatusCallback callback) {
        if (params == null) {
            throw new IllegalArgumentException("分析参数不能为空");
        }
        
        // 创建分析ID
        String analysisId = generateAnalysisId(params);
        
        // 检查是否有相同ID的分析正在进行
        if (activeAnalyses.containsKey(analysisId)) {
            if (callback != null) {
                callback.onError("已有相同参数的分析任务正在进行中", null);
            }
            return analysisId;
        }
        
        // 创建分析对象
        Analysis analysis = new Analysis(params);
        activeAnalyses.put(analysisId, analysis);
        
        // 使用增强回调以处理完成和错误
        AnalysisStatusCallback enhancedCallback = callback != null ? 
            enhanceCallback(callback, analysisId) : 
            AnalysisStatusCallback.createSimpleLogger(LOGGER);
        
        // 异步执行分析
        analysis.executeAsync(enhancedCallback)
            .exceptionally(ex -> {
                LOGGER.severe("分析执行异常: " + ex.getMessage());
                activeAnalyses.remove(analysisId);
                return null;
            });
        
        return analysisId;
    }
    
    /**
     * 增强回调以处理结果记录和清理
     */
    private AnalysisStatusCallback enhanceCallback(AnalysisStatusCallback originalCallback, String analysisId) {
        return new AnalysisStatusCallback() {
            @Override
            public void onStatus(String message) {
                originalCallback.onStatus(message);
            }
            
            @Override
            public void updateStatus(String message, int progress) {
                originalCallback.updateStatus(message, progress);
            }
            
            @Override
            public void onComplete(AnalysisResult result) {
                // 记录结果并从活动分析中移除
                addCompletedResult(result);
                activeAnalyses.remove(analysisId);
                
                // 调用原始回调
                originalCallback.onComplete(result);
            }
            
            @Override
            public void onError(String message, Throwable error) {
                activeAnalyses.remove(analysisId);
                originalCallback.onError(message, error);
            }
            
            @Override
            public void onLog(LogLevel level, String message) {
                originalCallback.onLog(level, message);
            }
            
            @Override
            public void onProgress(int percentage, String status) {
                originalCallback.onProgress(percentage, status);
            }
            
            @Override
            public void onStateChange(AnalysisState newState) {
                originalCallback.onStateChange(newState);
            }
        };
    }
    
    /**
     * 添加已完成的分析结果
     */
    private synchronized void addCompletedResult(AnalysisResult result) {
        if (result != null) {
            // 限制已完成结果数量，防止内存泄漏
            while (completedResults.size() >= 100) {
                completedResults.remove(0);
            }
            completedResults.add(result);
        }
    }
    
    /**
     * 获取活动分析的状态
     * @param analysisId 分析ID
     * @return 分析状态，如果分析不存在则返回null
     */
    public AnalysisState getAnalysisState(String analysisId) {
        Analysis analysis = activeAnalyses.get(analysisId);
        return analysis != null ? analysis.getState() : null;
    }
    
    /**
     * 获取已完成的分析结果列表
     * @return 结果列表的副本
     */
    public synchronized List<AnalysisResult> getCompletedResults() {
        return new ArrayList<>(completedResults);
    }
    
    /**
     * 生成分析ID
     * @param params 分析参数
     * @return 唯一标识分析任务的ID
     */
    private String generateAnalysisId(AnalysisParams params) {
        return String.format("%s_%s_%d", 
            params.getProtocolType(), 
            params.isTextMode() ? "text" : "gui",
            System.currentTimeMillis()
        );
    }
    
    /**
     * 创建一个简单的日志记录回调
     * @param target 日志记录目标
     * @return 状态回调对象
     */
    public static AnalysisStatusCallback createLoggingCallback(LoggingTarget target) {
        return new AnalysisStatusCallback() {
            @Override
            public void onStatus(String message) {
                target.log(message);
            }
            
            @Override
            public void updateStatus(String message, int progress) {
                if (progress == 100) {
                    target.log(MessageType.INFO, "完成: " + message);
                } else if (progress < 0) {
                    target.log(MessageType.ERROR, message);
                } else {
                    target.log(MessageType.INFO, String.format("[%d%%] %s", progress, message));
                }
            }
            
            @Override
            public void onComplete(AnalysisResult result) {
                if (result.isSuccess()) {
                    target.log(MessageType.INFO, "分析成功: " + result.getReportFile());
                } else {
                    target.log(MessageType.ERROR, "分析失败: " + result.getMessage());
                }
            }
        };
    }
    
    /**
     * 日志记录目标接口
     */
    public interface LoggingTarget {
        void log(String message);
        void log(MessageType type, String message);
    }
} 