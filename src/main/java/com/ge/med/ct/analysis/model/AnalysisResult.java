package com.ge.med.ct.analysis.model;

import java.util.List;
import java.util.Objects;

/**
 * 分析结果类
 * 封装分析完成后的状态、结果数据和相关信息
 */
public class AnalysisResult {
    private final AnalysisParams params; // 原始分析参数
    private final AnalysisState status;  // 分析状态
    private final String reportFile;     // 报告文件路径
    private final List<String> imagePaths; // 相关图像路径
    private final String message;        // 分析结果信息

    /**
     * 创建分析结果
     * @param params 原始分析参数
     * @param status 分析状态
     * @param message 结果信息
     * @param imagePaths 相关图像路径列表
     */
    public AnalysisResult(AnalysisParams params, String status, String message, List<String> imagePaths) {
        this.params = Objects.requireNonNull(params, "分析参数不能为空");
        this.status = AnalysisState.valueOf(status);
        this.message = message;
        this.reportFile = params.getOutputPath(); // 使用参数中的输出路径作为报告文件路径
        this.imagePaths = imagePaths;
    }

    public AnalysisResult(AnalysisParams params, AnalysisState status, String message, List<String> imagePaths) {
        this.params = Objects.requireNonNull(params, "分析参数不能为空");
        this.status = status;
        this.message = message;
        this.reportFile = params.getOutputPath(); // 使用参数中的输出路径作为报告文件路径
        this.imagePaths = imagePaths;
    }
    /**
     * 获取原始分析参数
     * @return 分析参数对象，可能为null（当使用旧构造方法时）
     */
    public AnalysisParams getParams() {
        return params;
    }

    /**
     * 获取分析状态
     * @return 分析状态枚举值
     */
    public AnalysisState getStatus() {
        return status;
    }

    /**
     * 获取分析结果消息
     * @return 结果描述信息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取相关图像路径列表
     * @return 图像路径列表，可能为null
     */
    public List<String> getImagePaths() {
        return imagePaths;
    }

    /**
     * 获取报告文件路径
     * @return 报告文件路径
     */
    public String getReportFile() {
        return reportFile;
    }

    /**
     * 检查分析是否成功
     * @return 如果状态为PASS则返回true，否则返回false
     */
    public boolean isSuccess() {
        return status == AnalysisState.PASS;
    }
    
    /**
     * 获取协议类型
     * @return 如果有参数则返回协议类型，否则返回null
     */
    public String getProtocolType() {
        return params != null ? params.getProtocolType() : null;
    }
    
    /**
     * 获取协议名称
     * @return 如果有参数则返回协议名称，否则返回null
     */
    public String getProtocolName() {
        return params != null ? params.getProtocolName() : null;
    }
    
    /**
     * 获取协议ID
     * @return 如果有参数则返回协议ID，否则返回null
     */
    public String getProtocolId() {
        return params != null ? params.getProtocolId() : null;
    }

    @Override
    public String toString() {
        return String.format("分析结果[状态=%s, 成功=%s, 消息=%s]", 
                status, isSuccess(), message != null ? message : "");
    }
} 