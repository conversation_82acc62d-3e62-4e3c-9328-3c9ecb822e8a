package com.ge.med.ct.config;

import java.util.*;
import java.util.logging.Logger;
import com.ge.med.ct.dicom.tag.DicomTag;

/**
 * 表格配置管理器
 * 负责管理检查列表、序列列表和图像列表的列配置
 */
public class TableConfigManager {
    private static final Logger LOG = Logger.getLogger(TableConfigManager.class.getName());
    private static volatile TableConfigManager instance;
    
    private final Map<String, List<TableColumnConfig>> tableConfigs;
    private final AppConfig appConfig;
    
    private TableConfigManager() {
        this.tableConfigs = new HashMap<>();
        this.appConfig = AppConfig.getInstance();
        loadConfigFromProperties();
    }
    
    public static TableConfigManager getInstance() {
        if (instance == null) {
            synchronized (TableConfigManager.class) {
                if (instance == null) {
                    instance = new TableConfigManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 从配置文件加载表格配置
     */
    private void loadConfigFromProperties() {
        loadTableConfig("exam");
        loadTableConfig("series");
        loadTableConfig("image");
    }
    
    /**
     * 加载指定表格类型的配置
     * @param tableType 表格类型
     */
    private void loadTableConfig(String tableType) {
        String columnsKey = "table." + tableType + ".columns";
        String columnsValue = appConfig.getString(columnsKey, "");
        
        if (columnsValue.isEmpty()) {
            LOG.warning("No columns configured for table type: " + tableType);
            initializeDefaultConfig(tableType);
            return;
        }
        
        List<TableColumnConfig> columns = new ArrayList<>();
        String[] columnNames = columnsValue.split(",");
        
        for (String columnName : columnNames) {
            columnName = columnName.trim();
            
            // 获取列配置
            String tagIdKey = "table." + tableType + ".column." + columnName + ".tagId";
            String displayNameKey = "table.column.names." + columnName;
            String widthKey = "table.column.width." + columnName;
            
            String tagId = appConfig.getString(tagIdKey, "");
            String displayName = appConfig.getString(displayNameKey, columnName);
            int width = appConfig.getInt(widthKey, 100);
            
            if (tagId.isEmpty()) {
                LOG.warning("No tag ID configured for column: " + columnName);
                continue;
            }
            
            TableColumnConfig config = new TableColumnConfig(tagId, columnName, displayName);
            config.setWidth(width);
            columns.add(config);
        }
        
        if (columns.isEmpty()) {
            LOG.warning("No valid columns loaded for table type: " + tableType);
            initializeDefaultConfig(tableType);
        } else {
            tableConfigs.put(tableType, columns);
            LOG.info(String.format("Loaded %d columns for table type: %s", columns.size(), tableType));
        }
    }
    
    /**
     * 初始化默认配置
     * @param tableType 表格类型
     */
    private void initializeDefaultConfig(String tableType) {
        List<TableColumnConfig> columns = new ArrayList<>();
        
        switch (tableType) {
            case "exam":
                columns.add(new TableColumnConfig("00200010", "Exam", "检查号"));
                columns.add(new TableColumnConfig("00081010", "StationName", "设备名称"));
                columns.add(new TableColumnConfig("00100010", "Name", "患者姓名"));
                columns.add(new TableColumnConfig("00080020", "Date", "日期"));
                columns.add(new TableColumnConfig("00081030", "Description", "描述"));
                columns.add(new TableColumnConfig("00080060", "Modality", "设备类型"));
                columns.add(new TableColumnConfig("00400252", "MPPS", "MPPS"));
                columns.add(new TableColumnConfig("00080056", "Archived", "已归档"));
                columns.add(new TableColumnConfig("00080058", "Transferred", "已传输"));
                break;
                
            case "series":
                columns.add(new TableColumnConfig("00200011", "Series", "序列号"));
                columns.add(new TableColumnConfig("00400009", "Type", "类型"));
                columns.add(new TableColumnConfig("00201209", "Images", "图像数"));
                columns.add(new TableColumnConfig("0008103E", "Description", "描述"));
                columns.add(new TableColumnConfig("00080060", "Modality", "设备类型"));
                columns.add(new TableColumnConfig("00080070", "Manufacturer", "制造商"));
                columns.add(new TableColumnConfig("00400252", "MPPS", "MPPS"));
                columns.add(new TableColumnConfig("00080056", "Archived", "已归档"));
                columns.add(new TableColumnConfig("00080058", "Transferred", "已传输"));
                break;
                
            case "image":
                columns.add(new TableColumnConfig("00200013", "Image", "图像号"));
                columns.add(new TableColumnConfig("00080022", "Date", "日期"));
                columns.add(new TableColumnConfig("00280010", "ImageSize", "图像尺寸"));
                break;
        }
        
        if (!columns.isEmpty()) {
            tableConfigs.put(tableType, columns);
            LOG.info(String.format("Initialized default config with %d columns for table type: %s", 
                columns.size(), tableType));
        }
    }
    
    /**
     * 获取指定表格的列配置
     * @param tableType 表格类型（exam/series/image）
     * @return 列配置列表
     */
    public List<TableColumnConfig> getTableColumns(String tableType) {
        return tableConfigs.getOrDefault(tableType, new ArrayList<>());
    }
    
    /**
     * 获取指定表格列的DICOM标签ID
     * @param tableType 表格类型
     * @param columnName 列名
     * @return DICOM标签ID
     */
    public String getTagId(String tableType, String columnName) {
        List<TableColumnConfig> columns = getTableColumns(tableType);
        for (TableColumnConfig column : columns) {
            if (column.getColumnName().equals(columnName)) {
                return column.getTagId();
            }
        }
        return null;
    }
    
    /**
     * 获取指定表格列的显示名称
     * @param tableType 表格类型
     * @param columnName 列名
     * @return 显示名称
     */
    public String getDisplayName(String tableType, String columnName) {
        List<TableColumnConfig> columns = getTableColumns(tableType);
        for (TableColumnConfig column : columns) {
            if (column.getColumnName().equals(columnName)) {
                return column.getDisplayName();
            }
        }
        return columnName;
    }
    
    /**
     * 更新列配置
     * @param tableType 表格类型
     * @param columnName 列名
     * @param config 新的配置
     */
    public void updateColumnConfig(String tableType, String columnName, TableColumnConfig config) {
        List<TableColumnConfig> columns = getTableColumns(tableType);
        for (int i = 0; i < columns.size(); i++) {
            if (columns.get(i).getColumnName().equals(columnName)) {
                columns.set(i, config);
                break;
            }
        }
    }
    
    /**
     * 添加新的列配置
     * @param tableType 表格类型
     * @param config 列配置
     */
    public void addColumnConfig(String tableType, TableColumnConfig config) {
        List<TableColumnConfig> columns = tableConfigs.computeIfAbsent(tableType, k -> new ArrayList<>());
        columns.add(config);
    }
    
    /**
     * 移除列配置
     * @param tableType 表格类型
     * @param columnName 列名
     */
    public void removeColumnConfig(String tableType, String columnName) {
        List<TableColumnConfig> columns = getTableColumns(tableType);
        columns.removeIf(config -> config.getColumnName().equals(columnName));
    }
    
    /**
     * 获取列的值是否为空的原因
     * @param tableType 表格类型
     * @param columnName 列名
     * @param value 值
     * @return 描述信息
     */
    public String getEmptyValueReason(String tableType, String columnName, String value) {
        if (value != null && !value.trim().isEmpty()) {
            return null;
        }
        
        String tagId = getTagId(tableType, columnName);
        if (tagId == null) {
            return "未找到对应的DICOM标签";
        }
        
        return String.format("DICOM标签[%s]值为空", tagId);
    }
} 