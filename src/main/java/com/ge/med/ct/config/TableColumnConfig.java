package com.ge.med.ct.config;

/**
 * 表格列配置类
 * 用于管理表格列与DICOM标签的映射关系
 */
public class TableColumnConfig {
    private String tagId;          // DICOM标签ID
    private String columnName;     // 列名（程序内部使用）
    private String displayName;    // 显示名称（界面显示）
    private String defaultValue;   // 默认值
    private boolean visible;       // 是否可见
    private int width;            // 列宽
    private int order;            // 显示顺序

    public TableColumnConfig(String tagId, String columnName, String displayName) {
        this.tagId = tagId;
        this.columnName = columnName;
        this.displayName = displayName;
        this.visible = true;
        this.width = 100;
        this.defaultValue = "";
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    @Override
    public String toString() {
        return String.format("TableColumnConfig[tagId=%s, columnName=%s, displayName=%s]", 
            tagId, columnName, displayName);
    }
} 