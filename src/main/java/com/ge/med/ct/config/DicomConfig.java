package com.ge.med.ct.config;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * DICOM配置类
 * 管理DICOM相关的配置参数
 * 使用不可变模式和构建器模式
 */
public final class DicomConfig {
    private static final Logger logger = Logger.getLogger(DicomConfig.class.getName());
    
    // 文件类型常量
    public static final String CT_IMAGE_TYPE = "CTDC";
    public static final String SR_IMAGE_TYPE = "SRDC";
    
    // 文件扩展名
    public static final String DICOM_EXTENSION = ".dcm";
    public static final String CT_EXTENSION = ".ctdc";
    public static final String SR_EXTENSION = ".srdc";
    
    // 路径分隔符
    public static final String PATH_SEPARATOR = "/";
    
    // 默认配置值
    private static final String DEFAULT_ROOT_DIR = System.getProperty("java.io.tmpdir") + File.separator + "dicom";
    private static final int DEFAULT_CACHE_SIZE = 1000;
    private static final long DEFAULT_CACHE_EXPIRE_TIME = 1;
    private static final TimeUnit DEFAULT_CACHE_EXPIRE_UNIT = TimeUnit.HOURS;
    
    // 文件类型映射
    private static final Map<String, String> FILE_TYPE_MAP = new HashMap<>();
    static {
        FILE_TYPE_MAP.put(CT_IMAGE_TYPE, CT_EXTENSION);
        FILE_TYPE_MAP.put(SR_IMAGE_TYPE, SR_EXTENSION);
    }
    
    // 配置属性
    private final String rootDirectory;
    private final int cacheSize;
    private final long cacheExpireTime;
    private final TimeUnit cacheExpireUnit;
    private final Properties additionalProperties;
    
    private DicomConfig(Builder builder) {
        this.rootDirectory = builder.rootDirectory;
        this.cacheSize = builder.cacheSize;
        this.cacheExpireTime = builder.cacheExpireTime;
        this.cacheExpireUnit = builder.cacheExpireUnit;
        this.additionalProperties = new Properties();
        this.additionalProperties.putAll(builder.additionalProperties);
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private String rootDirectory = DEFAULT_ROOT_DIR;
        private int cacheSize = DEFAULT_CACHE_SIZE;
        private long cacheExpireTime = DEFAULT_CACHE_EXPIRE_TIME;
        private TimeUnit cacheExpireUnit = DEFAULT_CACHE_EXPIRE_UNIT;
        private final Properties additionalProperties = new Properties();
        
        public Builder() {}
        
        public Builder(Properties properties) {
            if (properties != null) {
                this.rootDirectory = properties.getProperty("dicom.rootDirectory", DEFAULT_ROOT_DIR);
                
                try {
                    this.cacheSize = Integer.parseInt(properties.getProperty("dicom.cacheSize", String.valueOf(DEFAULT_CACHE_SIZE)));
                } catch (NumberFormatException e) {
                    logger.log(Level.WARNING, "Invalid cache size, using default", e);
                }
                
                try {
                    this.cacheExpireTime = Long.parseLong(properties.getProperty("dicom.cacheExpireTime", String.valueOf(DEFAULT_CACHE_EXPIRE_TIME)));
                } catch (NumberFormatException e) {
                    logger.log(Level.WARNING, "Invalid cache expire time, using default", e);
                }
                
                // 复制其他属性
                for (String key : properties.stringPropertyNames()) {
                    if (!key.startsWith("dicom.")) {
                        additionalProperties.setProperty(key, properties.getProperty(key));
                    }
                }
            }
        }
        
        public Builder(DicomConfig config) {
            this.rootDirectory = config.rootDirectory;
            this.cacheSize = config.cacheSize;
            this.cacheExpireTime = config.cacheExpireTime;
            this.cacheExpireUnit = config.cacheExpireUnit;
            this.additionalProperties.putAll(config.additionalProperties);
        }
        
        public Builder rootDirectory(String rootDirectory) {
            if (rootDirectory == null || rootDirectory.trim().isEmpty()) {
                throw new IllegalArgumentException("Root directory cannot be null or empty");
            }
            this.rootDirectory = rootDirectory;
            return this;
        }
        
        public Builder cacheSize(int cacheSize) {
            if (cacheSize <= 0) {
                throw new IllegalArgumentException("Cache size must be positive");
            }
            this.cacheSize = cacheSize;
            return this;
        }
        
        public Builder cacheExpireTime(long cacheExpireTime) {
            if (cacheExpireTime <= 0) {
                throw new IllegalArgumentException("Cache expire time must be positive");
            }
            this.cacheExpireTime = cacheExpireTime;
            return this;
        }
        
        public Builder cacheExpireUnit(TimeUnit cacheExpireUnit) {
            if (cacheExpireUnit == null) {
                throw new IllegalArgumentException("Cache expire unit cannot be null");
            }
            this.cacheExpireUnit = cacheExpireUnit;
            return this;
        }
        
        public Builder additionalProperty(String key, String value) {
            if (key == null || key.trim().isEmpty()) {
                throw new IllegalArgumentException("Property key cannot be null or empty");
            }
            this.additionalProperties.setProperty(key, value);
            return this;
        }
        
        public DicomConfig build() {
            return new DicomConfig(this);
        }
    }
    
    // Getter methods
    public String getRootDirectory() { return rootDirectory; }
    public int getCacheSize() { return cacheSize; }
    public long getCacheExpireTime() { return cacheExpireTime; }
    public TimeUnit getCacheExpireUnit() { return cacheExpireUnit; }
    public Properties getAdditionalProperties() { return (Properties) additionalProperties.clone(); }
    public String getAdditionalProperty(String key) { return additionalProperties.getProperty(key); }
    public String getAdditionalProperty(String key, String defaultValue) { return additionalProperties.getProperty(key, defaultValue); }
    
    /**
     * 获取文件类型对应的扩展名
     */
    public static String getExtensionForType(String fileType) {
        return FILE_TYPE_MAP.getOrDefault(fileType, DICOM_EXTENSION);
    }
    
    /**
     * 获取所有支持的文件扩展名
     */
    public static String[] getAllExtensions() {
        return new String[] {
            DICOM_EXTENSION,
            CT_EXTENSION,
            SR_EXTENSION,
            CT_EXTENSION + ".1",
            SR_EXTENSION + ".1"
        };
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DicomConfig that = (DicomConfig) o;
        return cacheSize == that.cacheSize &&
               cacheExpireTime == that.cacheExpireTime &&
               Objects.equals(rootDirectory, that.rootDirectory) &&
               Objects.equals(additionalProperties, that.additionalProperties);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(rootDirectory, cacheSize, cacheExpireTime, additionalProperties);
    }
    
    @Override
    public String toString() {
        return "DicomConfig{" +
               "rootDirectory='" + rootDirectory + '\'' +
               ", cacheSize=" + cacheSize +
               ", cacheExpireTime=" + cacheExpireTime +
               ", cacheExpireUnit=" + cacheExpireUnit +
               ", additionalProperties=" + additionalProperties +
               '}';
    }
} 