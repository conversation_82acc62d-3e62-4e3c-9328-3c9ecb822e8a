package com.ge.med.ct.config;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 应用程序配置管理类，负责加载和管理系统配置
 * 使用单例模式实现
 */
public class AppConfig {
    private static final Logger LOGGER = Logger.getLogger(AppConfig.class.getName());
    private static final AppConfig INSTANCE = new AppConfig();
    
    private static final String DEFAULT_CONFIG_PATH = "/config/application.properties";
    private static final String USER_CONFIG_PATH = "/config/application.properties";
    
    private final Properties properties;
    
    private AppConfig() {
        properties = new Properties();
        loadConfig();
    }
    
    /**
     * 获取ApplicationConfig单例实例
     * 
     * @return ApplicationConfig实例
     */
    public static AppConfig getInstance() {
        return INSTANCE;
    }

    private void loadConfig() {
        try (InputStream is = AppConfig.class.getResourceAsStream(DEFAULT_CONFIG_PATH)) {
            if (is != null) {
                properties.load(is);
                LOGGER.info("Loaded default configuration");
            } else {
                LOGGER.warning("Default configuration not found: " + DEFAULT_CONFIG_PATH);
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "Failed to load default configuration", e);
        }
    }

    /**
     * 获取字符串属性值
     * 
     * @param key 属性键
     * @return 属性值，如果不存在则返回null
     */
    public String getString(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 获取字符串属性值，如果不存在则返回默认值
     * 
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值或默认值
     */
    public String getString(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 获取整数属性值
     * 
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值或默认值
     */
    public int getInt(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            LOGGER.warning("Invalid integer value for property: " + key);
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔属性值
     * 
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值或默认值
     */
    public boolean getBoolean(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }
    
    /**
     * 设置属性值
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void setProperty(String key, String value) {
        properties.setProperty(key, value);
        LOGGER.info("Property updated: " + key + " = " + value);
    }
    
    /**
     * 保存配置到用户配置文件
     * 
     * @throws IOException 如果保存失败
     */
    public void saveConfig() throws IOException {
        Path userConfigDir = Paths.get("config");
        if (!Files.exists(userConfigDir)) {
            Files.createDirectories(userConfigDir);
        }
        
        Path userConfigPath = Paths.get(USER_CONFIG_PATH);
        try (java.io.OutputStream os = Files.newOutputStream(userConfigPath)) {
            properties.store(os, "CT Quality Assurance Tool Configuration");
            LOGGER.info("Configuration saved to: " + userConfigPath);
        }
    }

    /**
     * 获取所有配置属性
     * 
     * @return 配置属性副本
     */
    public Properties getAllProperties() {
        return new Properties(properties);
    }

    /**
     * 检查配置项是否存在
     * 
     * @param key 属性键
     * @return 是否存在
     */
    public boolean containsKey(String key) {
        return properties.containsKey(key);
    }

    /**
     * 移除配置项
     * 
     * @param key 属性键
     */
    public void removeProperty(String key) {
        properties.remove(key);
        LOGGER.info("Property removed: " + key);
    }

    /**
     * 清除所有配置
     */
    public void clear() {
        properties.clear();
        LOGGER.info("All properties cleared");
    }
} 