package com.ge.med.ct.dicom.model;

import java.util.logging.Logger;
import java.util.Map;
import java.util.HashMap;

import org.dcm4che3.data.VR;

import com.ge.med.ct.dicom.tag.DicomTag;
import com.ge.med.ct.dicom.util.DicomException;

/**
 * DICOM图像类
 * 管理DICOM图像属性和关系
 */
public class DicomImage {
    private static final Logger LOG = Logger.getLogger(DicomImage.class.getName());
    
    private String id;
    private Map<String, DicomTag> tags;
    private DicomSeries series;
    
    public DicomImage(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException("ID cannot be null or empty");
        }
        this.id = id;
        this.tags = new HashMap<>();
    }
    
    public String getId() {
        return id;
    }
    
    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }
    
    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            LOG.fine("Added tag " + tagId + " to image " + id);
        }
    }
    
    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValue() : null;
    }
    
    // 基本图像标识符
    public String getSopInstanceUID() {
        return getTagValue(DicomTag.SOPInstanceUID.getTagId());
    }
    
    public void setSopInstanceUID(String uid) throws DicomException {
        setTagValue(DicomTag.SOPInstanceUID.getTagId(), uid);
    }
    
    public String getInstanceNumber() {
        return getTagValue(DicomTag.InstanceNumber.getTagId());
    }
    
    public void setInstanceNumber(String number) throws DicomException {
        setTagValue(DicomTag.InstanceNumber.getTagId(), number);
    }
    
    // 图像类型和基本属性
    public String getImageType() {
        return getTagValue(DicomTag.ImageType.getTagId());
    }
    
    public void setImageType(String type) throws DicomException {
        setTagValue(DicomTag.ImageType.getTagId(), type);
    }
    
    public String getImageDate() {
        return getTagValue(DicomTag.ImageDate.getTagId());
    }
    
    public void setImageDate(String date) throws DicomException {
        setTagValue(DicomTag.ImageDate.getTagId(), date);
    }
    
    public String getImageTime() {
        return getTagValue(DicomTag.ImageTime.getTagId());
    }
    
    public void setImageTime(String time) throws DicomException {
        setTagValue(DicomTag.ImageTime.getTagId(), time);
    }
    
    // 图像尺寸和像素属性
    public String getRows() {
        return getTagValue(DicomTag.Rows.getTagId());
    }
    
    public void setRows(String rows) throws DicomException {
        setTagValue(DicomTag.Rows.getTagId(), rows);
    }
    
    public String getColumns() {
        return getTagValue(DicomTag.Columns.getTagId());
    }
    
    public void setColumns(String columns) throws DicomException {
        setTagValue(DicomTag.Columns.getTagId(), columns);
    }
    
    public String getBitsAllocated() {
        return getTagValue(DicomTag.BitsAllocated.getTagId());
    }
    
    public void setBitsAllocated(String bits) throws DicomException {
        setTagValue(DicomTag.BitsAllocated.getTagId(), bits);
    }
    
    public String getBitsStored() {
        return getTagValue(DicomTag.BitsStored.getTagId());
    }
    
    public void setBitsStored(String bits) throws DicomException {
        setTagValue(DicomTag.BitsStored.getTagId(), bits);
    }
    
    public String getHighBit() {
        return getTagValue(DicomTag.HighBit.getTagId());
    }
    
    public void setHighBit(String bit) throws DicomException {
        setTagValue(DicomTag.HighBit.getTagId(), bit);
    }
    
    // 像素数据属性
    public String getPixelRepresentation() {
        return getTagValue(DicomTag.PixelRepresentation.getTagId());
    }
    
    public void setPixelRepresentation(String repr) throws DicomException {
        setTagValue(DicomTag.PixelRepresentation.getTagId(), repr);
    }
    
    public String getSamplesPerPixel() {
        return getTagValue(DicomTag.SamplesPerPixel.getTagId());
    }
    
    public void setSamplesPerPixel(String samples) throws DicomException {
        setTagValue(DicomTag.SamplesPerPixel.getTagId(), samples);
    }
    
    public String getPlanarConfiguration() {
        return getTagValue(DicomTag.PlanarConfiguration.getTagId());
    }
    
    public void setPlanarConfiguration(String config) throws DicomException {
        setTagValue(DicomTag.PlanarConfiguration.getTagId(), config);
    }
    
    // 窗位窗宽
    public String getWindowCenter() {
        return getTagValue(DicomTag.WindowCenter.getTagId());
    }
    
    public void setWindowCenter(String center) throws DicomException {
        setTagValue(DicomTag.WindowCenter.getTagId(), center);
    }
    
    public String getWindowWidth() {
        return getTagValue(DicomTag.WindowWidth.getTagId());
    }
    
    public void setWindowWidth(String width) throws DicomException {
        setTagValue(DicomTag.WindowWidth.getTagId(), width);
    }
    
    // 图像重建参数
    public String getRescaleIntercept() {
        return getTagValue(DicomTag.RescaleIntercept.getTagId());
    }
    
    public void setRescaleIntercept(String intercept) throws DicomException {
        setTagValue(DicomTag.RescaleIntercept.getTagId(), intercept);
    }
    
    public String getRescaleSlope() {
        return getTagValue(DicomTag.RescaleSlope.getTagId());
    }
    
    public void setRescaleSlope(String slope) throws DicomException {
        setTagValue(DicomTag.RescaleSlope.getTagId(), slope);
    }
    
    public String getRescaleType() {
        return getTagValue(DicomTag.RescaleType.getTagId());
    }
    
    public void setRescaleType(String type) throws DicomException {
        setTagValue(DicomTag.RescaleType.getTagId(), type);
    }
    
    // 图像位置和方向
    public String getImagePositionPatient() {
        return getTagValue(DicomTag.ImagePositionPatient.getTagId());
    }
    
    public void setImagePositionPatient(String position) throws DicomException {
        setTagValue(DicomTag.ImagePositionPatient.getTagId(), position);
    }
    
    public String getImageOrientationPatient() {
        return getTagValue(DicomTag.ImageOrientationPatient.getTagId());
    }
    
    public void setImageOrientationPatient(String orientation) throws DicomException {
        setTagValue(DicomTag.ImageOrientationPatient.getTagId(), orientation);
    }
    
    public String getSliceLocation() {
        return getTagValue(DicomTag.SliceLocation.getTagId());
    }
    
    public void setSliceLocation(String location) throws DicomException {
        setTagValue(DicomTag.SliceLocation.getTagId(), location);
    }
    
    public String getImageIndex() {
        return getTagValue(DicomTag.ImageIndex.getTagId());
    }
    
    public void setImageIndex(String index) throws DicomException {
        setTagValue(DicomTag.ImageIndex.getTagId(), index);
    }
    
    public String getImageComments() {
        return getTagValue(DicomTag.ImageComments.getTagId());
    }
    
    public void setImageComments(String comments) throws DicomException {
        setTagValue(DicomTag.ImageComments.getTagId(), comments);
    }
    
    private void setTagValue(String tagId, String value) throws DicomException {
        DicomTag tag = tags.get(tagId);
        if (tag == null) {
            tag = DicomTag.createNewTag(tagId, "Tag-" + tagId, value, VR.UN, "Dynamic tag " + tagId, false);
            if (tag == null) {
                throw new DicomException("Failed to create tag: " + tagId);
            }
            tags.put(tagId, tag);
        } else {
            tag.setValue(value);
        }
        LOG.fine("Set tag " + tagId + " value to " + value + " for image " + id);
    }
    
    public DicomSeries getSeries() {
        return series;
    }
    
    public void setSeries(DicomSeries series) {
        if (this.series != series) {
            DicomSeries oldSeries = this.series;
            this.series = series;
            
            if (oldSeries != null) {
                oldSeries.removeImage(this);
            }
            
            if (series != null && !series.getImages().contains(this)) {
                series.addImage(this);
            }
            
            LOG.fine("Set series " + (series != null ? series.getId() : "null") + " for image " + id);
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s[id=%s, sopInstanceUID=%s, instanceNumber=%s, imageType=%s, imageDate=%s, imageTime=%s, rows=%s, columns=%s, series=%s]",
            getClass().getSimpleName(), id, getSopInstanceUID(), getInstanceNumber(), getImageType(),
            getImageDate(), getImageTime(), getRows(), getColumns(),
            series != null ? series.getId() : "null");
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DicomImage other = (DicomImage) obj;
        return id != null && id.equals(other.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
} 