package com.ge.med.ct.dicom.io;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.io.IOException;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ge.med.ct.dicom.tag.DicomTagRegistry;
import com.ge.med.ct.dicom.model.DicomFileModel;

/**
 * DICOM文件扫描器
 * 提供扫描目录和查找DICOM文件的功能
 */
public class DicomFileScanner implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(DicomFileScanner.class);
    
    private final ExecutorService executor;
    private final DicomFileReader fileReader;

    private final List<String> foundFiles;
    private final List<Consumer<List<String>>> progressListeners;
    private final List<Consumer<List<String>>> completionListeners;

    private volatile boolean isScanning;
    private volatile boolean shouldStop;
    
    /**
     * 创建一个新的DICOM文件扫描器
     * 使用固定线程池
     */
    public DicomFileScanner() {
        this.executor = Executors.newFixedThreadPool(
            Math.max(2, Runtime.getRuntime().availableProcessors() - 1)
        );
        this.foundFiles = new ArrayList<>();
        this.progressListeners = new ArrayList<>();
        this.completionListeners = new ArrayList<>();
        DicomTagRegistry.getInstance();
        this.isScanning = false;
        this.shouldStop = false;
        this.fileReader = new DicomFileReader();
    }
    
    /**
     * 添加进度监听器
     * 
     * @param listener 监听器，接收到目前为止找到的文件列表
     */
    public void addProgressListener(Consumer<List<String>> listener) {
        if (listener != null) {
            progressListeners.add(listener);
        }
    }
    
    /**
     * 添加完成监听器
     * 
     * @param listener 监听器，接收最终的文件列表
     */
    public void addCompletionListener(Consumer<List<String>> listener) {
        if (listener != null) {
            completionListeners.add(listener);
        }
    }
    
    /**
     * 异步扫描目录
     * 
     * @param directory 要扫描的目录
     * @return 表示扫描任务的future
     */
    public CompletableFuture<List<String>> scanDirectoryAsync(String directory) {
        if (isScanning) {
            logger.warn("Scan already in progress");
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        isScanning = true;
        shouldStop = false;
        foundFiles.clear();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<String> result = scanDirectory(directory);
                if (!shouldStop) {
                    for (Consumer<List<String>> listener : completionListeners) {
                        try {
                            listener.accept(result);
                        } catch (Exception e) {
                            logger.error("Completion listener error", e);
                        }
                    }
                }
                return result;
            } catch (Exception e) {
                logger.error("Directory scan error", e);
                return new ArrayList<>();
            } finally {
                isScanning = false;
            }
        }, executor);
    }
    
    /**
     * 同步扫描目录
     * 
     * @param directory 要扫描的目录
     * @return 找到的DICOM文件列表
     */
    public List<String> scanDirectory(String directory) {
        long startTime = System.currentTimeMillis();
        
        // 验证目录
        if (!FileSystemUtil.isFileValid(directory, 0)) {
            logger.warn("Invalid directory: {}", directory);
            return new ArrayList<>();
        }
        
        try {
            Path directoryPath = Paths.get(directory).normalize();
            logger.info("Scanning directory: {}", directory);
            
            // 使用Files.walk并设置最大深度为10
            List<Path> allFiles = Files.walk(directoryPath, 10)
                .filter(path -> Files.isRegularFile(path))
                .map(Path::normalize)
                .distinct()
                .collect(Collectors.toList());
            
            logger.info("Found {} potential DICOM files in directory", allFiles.size());
            
            // 处理每个文件
            int processedFiles = 0;
            for (Path path : allFiles) {
                if (shouldStop) {
                    logger.info("Scan stopped by user request after processing {} files", processedFiles);
                    break;
                }
                
                processedFiles++;
                if (processedFiles % 100 == 0) {
                    logger.info("Processed {}/{} files", processedFiles, allFiles.size());
                }
                
                try {
                    String filePath = path.toString();
                    if (FileSystemUtil.isFileValid(filePath, 132) && fileReader.isDicomFile(filePath)) {
                        if (!foundFiles.contains(filePath)) {
                            foundFiles.add(filePath);
                            
                            if (foundFiles.size() % 100 == 0) {
                                logger.info("Found {} DICOM files so far", foundFiles.size());
                                notifyProgressListeners();
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("File processing error: {}", path, e);
                }
            }
            
            long endTime = System.currentTimeMillis();
            logger.info("Scan completed in {} ms. Found {} DICOM files", 
                (endTime - startTime), foundFiles.size());
            
            return new ArrayList<>(foundFiles);
        } catch (Exception e) {
            logger.error("Directory scan error", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 停止当前扫描
     */
    public void stopScan() {
        if (!isScanning) {
            logger.warn("No scan in progress");
            return;
        }
        shouldStop = true;
        logger.info("Scan stop requested");
    }
    
    /**
     * 通知所有进度监听器
     */
    private void notifyProgressListeners() {
        List<String> snapshot = new ArrayList<>(foundFiles);
        for (Consumer<List<String>> listener : progressListeners) {
            try {
                listener.accept(snapshot);
            } catch (Exception e) {
                logger.error("Progress listener error", e);
            }
        }
    }
    
    /**
     * 异步过滤DICOM文件
     * 
     * @param files 文件列表
     * @return 过滤后的DICOM文件列表
     */
    public CompletableFuture<List<String>> filterDicomFilesAsync(List<String> files) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return files.stream()
                    .filter(FileSystemUtil::fileExists)
                    .filter(fileReader::isDicomFile)
                    .collect(Collectors.toList());
            } catch (Exception e) {
                logger.error("DICOM file filtering error", e);
                return new ArrayList<>();
            }
        }, executor);
    }

    /**
     * 扫描目录中的DICOM文件
     * @param directoryPath 目录路径
     * @param recursive 是否递归扫描子目录
     * @return DICOM文件列表
     */
    public List<DicomFileModel> scanDirectory(String directoryPath, boolean recursive) {
        List<DicomFileModel> dicomFiles = new ArrayList<>();
        
        if (!FileSystemUtil.isFileValid(directoryPath, 0)) {
            logger.warn("Invalid directory: {}", directoryPath);
            return dicomFiles;
        }
        
        try {
            Stream<Path> pathStream = recursive ? 
                Files.walk(Paths.get(directoryPath)) : 
                Files.list(Paths.get(directoryPath));
            
            pathStream.filter(Files::isRegularFile)
                     .map(Path::toString)
                     .filter(fileReader::isDicomFile)
                     .forEach(filePath -> {
                         try {
                             DicomFileModel model = fileReader.readDicomFile(filePath);
                             if (model != null) {
                                 dicomFiles.add(model);
                             }
                         } catch (Exception e) {
                             logger.error("DICOM file read error: {}", filePath, e);
                         }
                     });
            
            pathStream.close();
        } catch (IOException e) {
            logger.error("Directory scan error: {}", directoryPath, e);
        }
        
        return dicomFiles;
    }
    
    /**
     * 获取目录中的DICOM文件数量
     * @param directoryPath 目录路径
     * @param recursive 是否递归扫描子目录
     * @return DICOM文件数量
     */
    public long countDicomFiles(String directoryPath, boolean recursive) {
        if (!FileSystemUtil.isFileValid(directoryPath, 0)) {
            logger.warn("Invalid directory: {}", directoryPath);
            return 0;
        }
        
        try {
            Stream<Path> pathStream = recursive ? 
                Files.walk(Paths.get(directoryPath)) : 
                Files.list(Paths.get(directoryPath));
            
            long count = pathStream.filter(Files::isRegularFile)
                                 .map(Path::toString)
                                 .filter(fileReader::isDicomFile)
                                 .count();
            
            pathStream.close();
            return count;
        } catch (IOException e) {
            logger.error("DICOM file count error: {}", directoryPath, e);
            return 0;
        }
    }
    
    /**
     * 检查目录是否包含DICOM文件
     * @param directoryPath 目录路径
     * @param recursive 是否递归扫描子目录
     * @return 是否包含DICOM文件
     */
    public boolean hasDicomFiles(String directoryPath, boolean recursive) {
        return countDicomFiles(directoryPath, recursive) > 0;
    }
    
    /**
     * 关闭扫描器并释放资源
     */
    @Override
    public void close() {
        stopScan();
        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
} 