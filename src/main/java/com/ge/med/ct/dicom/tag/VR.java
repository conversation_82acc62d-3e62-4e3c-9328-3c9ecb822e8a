package com.ge.med.ct.dicom.tag;

/**
 * DICOM Value Representation (VR) 枚举
 * 定义DICOM标准中支持的值表示类型
 */
public enum VR {
    AE("Application Entity"),
    AS("Age String"),
    AT("Attribute Tag"),
    CS("Code String"),
    DA("Date"),
    DS("Decimal String"),
    DT("Date Time"),
    FL("Floating Point Single"),
    FD("Floating Point Double"),
    IS("Integer String"),
    LO("Long String"),
    LT("Long Text"),
    OB("Other Byte"),
    OD("Other Double"),
    OF("Other Float"),
    OL("Other Long"),
    OW("Other Word"),
    PN("Person Name"),
    SH("Short String"),
    SL("Signed Long"),
    SS("Signed Short"),
    ST("Short Text"),
    TM("Time"),
    UI("Unique Identifier"),
    UL("Unsigned Long"),
    UN("Unknown"),
    US("Unsigned Short"),
    UT("Unlimited Text");
    
    private final String description;
    
    private VR(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return name() + " - " + description;
    }
} 