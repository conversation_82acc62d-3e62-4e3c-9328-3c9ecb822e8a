package com.ge.med.ct.dicom;

import java.io.IOException;
import java.util.List;
import java.util.logging.Logger;

import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.data.VR;

import com.ge.med.ct.dicom.model.DicomFileModel;
import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.tag.DicomTag;
import com.ge.med.ct.dicom.tag.DicomTagRegistry;
import com.ge.med.ct.dicom.util.DicomException;

/**
 * DICOM标签解析器
 * 负责DICOM标签的解析和验证
 */
public class DicomTagParser {
    private static final Logger LOG = Logger.getLogger(DicomTagParser.class.getName());
    private final DicomTagRegistry tagRegistry;
    
    public DicomTagParser() {
        this.tagRegistry = DicomTagRegistry.getInstance();
    }
    
    /**
     * 解析DICOM标签
     * @param model DICOM文件模型
     * @param attributes DICOM属性
     * @return 解析后的DICOM文件模型
     * @throws DicomException 如果解析失败
     */
    public DicomFileModel parseDicomTags(DicomFileModel model, Attributes attributes) throws DicomException {
        if (model == null || attributes == null) {
            throw new DicomException("Model or attributes is null");
        }
        
        try {
            // 解析常用标签
            parseCommonTags(model, attributes);
            
            // 按类别解析标签
            parseTagsByCategory(model, attributes, "Patient");
            parseTagsByCategory(model, attributes, "Study");
            parseTagsByCategory(model, attributes, "Series");
            parseTagsByCategory(model, attributes, "Image");
            
            // 解析图像相关标签
            parseImageTags(model, attributes);
            
            return model;
        } catch (Exception e) {
            throw new DicomException("Failed to parse DICOM tags: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析常用标签
     * @param model DICOM文件模型
     * @param attributes DICOM属性
     * @throws DicomException 如果解析失败
     */
    private void parseCommonTags(DicomFileModel model, Attributes attributes) throws DicomException {
        // 患者信息
        addTagIfExists(model, attributes, Tag.PatientID);
        addTagIfExists(model, attributes, Tag.PatientName);
        addTagIfExists(model, attributes, Tag.PatientBirthDate);
        addTagIfExists(model, attributes, Tag.PatientSex);
        addTagIfExists(model, attributes, Tag.PatientAge);
        addTagIfExists(model, attributes, Tag.PatientSize);
        addTagIfExists(model, attributes, Tag.PatientWeight);
        addTagIfExists(model, attributes, Tag.PatientState);
        addTagIfExists(model, attributes, Tag.PregnancyStatus);
        addTagIfExists(model, attributes, Tag.MedicalAlerts);
        addTagIfExists(model, attributes, Tag.Allergies);
        addTagIfExists(model, attributes, Tag.SpecialNeeds);
        addTagIfExists(model, attributes, Tag.PatientComments);
        
        // 检查信息
        addTagIfExists(model, attributes, Tag.StudyInstanceUID);
        addTagIfExists(model, attributes, Tag.StudyID);
        addTagIfExists(model, attributes, Tag.StudyDate);
        addTagIfExists(model, attributes, Tag.StudyTime);
        addTagIfExists(model, attributes, Tag.StudyDescription);
        addTagIfExists(model, attributes, Tag.AccessionNumber);
        addTagIfExists(model, attributes, Tag.Modality);
        addTagIfExists(model, attributes, Tag.ReferringPhysicianName);
        addTagIfExists(model, attributes, Tag.StudyComments);
        addTagIfExists(model, attributes, Tag.StudyCompletionDate);
        addTagIfExists(model, attributes, Tag.StudyCompletionTime);
        addTagIfExists(model, attributes, Tag.StudyVerifiedDate);
        addTagIfExists(model, attributes, Tag.StudyVerifiedTime);
        addTagIfExists(model, attributes, Tag.NumberOfStudyRelatedSeries);
        addTagIfExists(model, attributes, Tag.NumberOfStudyRelatedInstances);
        
        // 序列信息
        addTagIfExists(model, attributes, Tag.SeriesInstanceUID);
        addTagIfExists(model, attributes, Tag.SeriesNumber);
        addTagIfExists(model, attributes, Tag.SeriesDate);
        addTagIfExists(model, attributes, Tag.SeriesTime);
        addTagIfExists(model, attributes, Tag.SeriesDescription);
        addTagIfExists(model, attributes, Tag.BodyPartExamined);
        addTagIfExists(model, attributes, Tag.SeriesType);
        addTagIfExists(model, attributes, Tag.ProtocolName);
        addTagIfExists(model, attributes, Tag.NumberOfSeriesRelatedInstances);
        addTagIfExists(model, attributes, Tag.PerformedProcedureStepStartDate);
        addTagIfExists(model, attributes, Tag.PerformedProcedureStepStartTime);
    }
    
    /**
     * 解析图像相关标签
     * @param model DICOM文件模型
     * @param attributes DICOM属性
     * @throws DicomException 如果解析失败
     */
    private void parseImageTags(DicomFileModel model, Attributes attributes) throws DicomException {
        DicomImage image = model.getImage();
        if (image == null) {
            image = new DicomImage(model.getId());
            model.setImage(image);
        }
        
        // 基本图像属性
        if (attributes.contains(Tag.Rows)) {
            String rows = String.valueOf(attributes.getInt(Tag.Rows, 0));
            image.setRows(rows);
            addTagIfExists(model, attributes, Tag.Rows);
        }
        if (attributes.contains(Tag.Columns)) {
            String columns = String.valueOf(attributes.getInt(Tag.Columns, 0));
            image.setColumns(columns);
            addTagIfExists(model, attributes, Tag.Columns);
        }
        
        // 图像相关标签
        addTagIfExists(model, attributes, Tag.SOPInstanceUID);
        addTagIfExists(model, attributes, Tag.SOPClassUID);
        addTagIfExists(model, attributes, Tag.InstanceNumber);
        addTagIfExists(model, attributes, Tag.ImageType);
        addTagIfExists(model, attributes, Tag.AcquisitionDate);
        addTagIfExists(model, attributes, Tag.AcquisitionTime);
        addTagIfExists(model, attributes, Tag.AcquisitionNumber);
        addTagIfExists(model, attributes, Tag.ImagePositionPatient);
        addTagIfExists(model, attributes, Tag.ImageOrientationPatient);
        addTagIfExists(model, attributes, Tag.SliceLocation);
        addTagIfExists(model, attributes, Tag.SliceThickness);
        addTagIfExists(model, attributes, Tag.ImageIndex);
        addTagIfExists(model, attributes, Tag.ImageComments);
        addTagIfExists(model, attributes, Tag.WindowCenter);
        addTagIfExists(model, attributes, Tag.WindowWidth);
        addTagIfExists(model, attributes, Tag.RescaleIntercept);
        addTagIfExists(model, attributes, Tag.RescaleSlope);
        addTagIfExists(model, attributes, Tag.RescaleType);
        addTagIfExists(model, attributes, Tag.PixelSpacing);
        addTagIfExists(model, attributes, Tag.ImagerPixelSpacing);
        addTagIfExists(model, attributes, Tag.NominalScannedPixelSpacing);
        
        // 像素数据相关
        addTagIfExists(model, attributes, Tag.BitsAllocated);
        addTagIfExists(model, attributes, Tag.BitsStored);
        addTagIfExists(model, attributes, Tag.HighBit);
        addTagIfExists(model, attributes, Tag.PixelRepresentation);
        addTagIfExists(model, attributes, Tag.SamplesPerPixel);
        addTagIfExists(model, attributes, Tag.PlanarConfiguration);
        addTagIfExists(model, attributes, Tag.PhotometricInterpretation);
        addTagIfExists(model, attributes, Tag.PixelAspectRatio);
        addTagIfExists(model, attributes, Tag.SmallestImagePixelValue);
        addTagIfExists(model, attributes, Tag.LargestImagePixelValue);
        
        // 像素数据
        if (attributes.contains(Tag.PixelData)) {
            try {
                byte[] pixelData = attributes.getBytes(Tag.PixelData);
                if (pixelData != null) {
                    model.addTag(String.valueOf(Tag.PixelData), pixelData);
                }
            } catch (IOException e) {
                throw new DicomException("Failed to read pixel data: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 如果标签存在则添加
     * @param model DICOM文件模型
     * @param attributes DICOM属性
     * @param tagId 标签ID
     * @throws DicomException 如果添加失败
     */
    private void addTagIfExists(DicomFileModel model, Attributes attributes, int tagId) throws DicomException {
        if (attributes.contains(tagId)) {
            String value = getStringValue(attributes, tagId);
            if (value != null && !value.trim().isEmpty()) {
                VR vr = attributes.getVR(tagId);
                if (vr == null) {
                    vr = VR.UN;
                }
                DicomTag tag = new DicomTag(String.format("%08X", tagId), value, vr);
                model.addTag(String.valueOf(tagId), tag);
            }
        }
    }
    
    /**
     * 按类别解析标签
     * @param model DICOM文件模型
     * @param attributes DICOM属性
     * @param category 标签类别
     * @throws DicomException 如果解析失败
     */
    private void parseTagsByCategory(DicomFileModel model, Attributes attributes, String category) throws DicomException {
        List<DicomTag> tags = tagRegistry.getTagsByCategory(category);
        if (tags == null || tags.isEmpty()) {
            return;
        }
        
        for (DicomTag tag : tags) {
            try {
                String tagId = tag.getTagId();
                int tagIdInt = Integer.parseInt(tagId, 16);
                if (attributes.contains(tagIdInt)) {
                    String value = getStringValue(attributes, tagIdInt);
                    if (value != null && !value.trim().isEmpty()) {
                        DicomTag newTag = new DicomTag(tagId, value, tag.getVr());
                        model.addTag(tagId, newTag);
                    }
                }
            } catch (Exception e) {
                LOG.warning("Failed to parse tag: " + tag.getName() + ", Error: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取字符串值
     * @param attributes DICOM属性
     * @param tagId 标签ID
     * @return 字符串值
     */
    private String getStringValue(Attributes attributes, int tagId) {
        if (attributes == null || !attributes.contains(tagId)) {
            return null;
        }
        
        try {
            return attributes.getString(tagId);
        } catch (Exception e) {
            LOG.warning("Failed to get string value for tag " + tagId + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证标签
     * @param model DICOM文件模型
     * @param tagId 标签ID
     * @param expectedVR 期望的值表示
     * @return 是否验证通过
     */
    public boolean validateTag(DicomFileModel model, String tagId, VR expectedVR) {
        if (model == null || tagId == null || expectedVR == null) {
            return false;
        }
        return model.validateTag(tagId, expectedVR);
    }
} 