package com.ge.med.ct.dicom.model;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.logging.Logger;

import org.dcm4che3.data.VR;

import com.ge.med.ct.dicom.util.DicomException;
import com.ge.med.ct.dicom.tag.DicomTag;

/**
 * DICOM序列类
 * 管理DICOM序列属性和图像
 */
public class DicomSeries {
    private static final Logger LOG = Logger.getLogger(DicomSeries.class.getName());
    
    private String id;
    private Map<String, DicomTag> tags;
    private List<DicomImage> images;
    private DicomExam exam;
    
    public DicomSeries(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException("ID cannot be null or empty");
        }
        this.id = id;
        this.tags = new HashMap<>();
        this.images = new ArrayList<>();
    }
    
    public String getId() {
        return id;
    }
    
    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }
    
    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            LOG.fine("Added tag " + tagId + " to series " + id);
        }
    }
    
    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValue() : null;
    }
    
    // 序列标识符
    public String getSeriesInstanceUID() {
        return getTagValue(DicomTag.SeriesInstanceUID.getTagId());
    }
    
    public void setSeriesInstanceUID(String uid) throws DicomException {
        setTagValue(DicomTag.SeriesInstanceUID.getTagId(), uid);
    }
    
    public String getSeriesNumber() {
        return getTagValue(DicomTag.SeriesNumber.getTagId());
    }
    
    public void setSeriesNumber(String number) throws DicomException {
        setTagValue(DicomTag.SeriesNumber.getTagId(), number);
    }
    
    // 序列描述信息
    public String getSeriesDescription() {
        return getTagValue(DicomTag.SeriesDescription.getTagId());
    }
    
    public void setSeriesDescription(String description) throws DicomException {
        setTagValue(DicomTag.SeriesDescription.getTagId(), description);
    }
    
    public String getModality() {
        return getTagValue(DicomTag.Modality.getTagId());
    }
    
    public void setModality(String modality) throws DicomException {
        setTagValue(DicomTag.Modality.getTagId(), modality);
    }
    
    public String getBodyPartExamined() {
        return getTagValue(DicomTag.BodyPartExamined.getTagId());
    }
    
    public void setBodyPartExamined(String bodyPart) throws DicomException {
        setTagValue(DicomTag.BodyPartExamined.getTagId(), bodyPart);
    }
    
    // 序列日期时间
    public String getSeriesDate() {
        return getTagValue(DicomTag.SeriesDate.getTagId());
    }
    
    public void setSeriesDate(String date) throws DicomException {
        setTagValue(DicomTag.SeriesDate.getTagId(), date);
    }
    
    public String getSeriesTime() {
        return getTagValue(DicomTag.SeriesTime.getTagId());
    }
    
    public void setSeriesTime(String time) throws DicomException {
        setTagValue(DicomTag.SeriesTime.getTagId(), time);
    }
    
    // 序列状态和优先级
    public String getSeriesStatus() {
        return getTagValue(DicomTag.SeriesStatus.getTagId());
    }
    
    public void setSeriesStatus(String status) throws DicomException {
        setTagValue(DicomTag.SeriesStatus.getTagId(), status);
    }
    
    public String getSeriesPriority() {
        return getTagValue(DicomTag.SeriesPriority.getTagId());
    }
    
    public void setSeriesPriority(String priority) throws DicomException {
        setTagValue(DicomTag.SeriesPriority.getTagId(), priority);
    }
    
    // 序列完成和验证时间
    public String getSeriesCompletionDate() {
        return getTagValue(DicomTag.SeriesCompletionDate.getTagId());
    }
    
    public void setSeriesCompletionDate(String date) throws DicomException {
        setTagValue(DicomTag.SeriesCompletionDate.getTagId(), date);
    }
    
    public String getSeriesCompletionTime() {
        return getTagValue(DicomTag.SeriesCompletionTime.getTagId());
    }
    
    public void setSeriesCompletionTime(String time) throws DicomException {
        setTagValue(DicomTag.SeriesCompletionTime.getTagId(), time);
    }
    
    public String getSeriesVerifiedDate() {
        return getTagValue(DicomTag.SeriesVerifiedDate.getTagId());
    }
    
    public void setSeriesVerifiedDate(String date) throws DicomException {
        setTagValue(DicomTag.SeriesVerifiedDate.getTagId(), date);
    }
    
    public String getSeriesVerifiedTime() {
        return getTagValue(DicomTag.SeriesVerifiedTime.getTagId());
    }
    
    public void setSeriesVerifiedTime(String time) throws DicomException {
        setTagValue(DicomTag.SeriesVerifiedTime.getTagId(), time);
    }
    
    private void setTagValue(String tagId, String value) throws DicomException {
        DicomTag tag = tags.get(tagId);
        if (tag == null) {
            tag = DicomTag.createNewTag(tagId, "Tag-" + tagId, value, VR.UN, "Dynamic tag " + tagId, false);
            if (tag == null) {
                throw new DicomException("Failed to create tag: " + tagId);
            }
            tags.put(tagId, tag);
        } else {
            tag.setValue(value);
        }
        LOG.fine("Set tag " + tagId + " value to " + value + " for series " + id);
    }
    
    public List<DicomImage> getImages() {
        return Collections.unmodifiableList(images);
    }
    
    public void setImages(List<DicomImage> images) {
        if (images == null) {
            throw new IllegalArgumentException("Images list cannot be null");
        }
        
        // Remove all existing images
        for (DicomImage image : new ArrayList<>(this.images)) {
            removeImage(image);
        }
        
        // Add all new images
        for (DicomImage image : images) {
            addImage(image);
        }
    }
    
    public void addImage(DicomImage image) {
        if (image != null && !images.contains(image)) {
            images.add(image);
            if (image.getSeries() != this) {
                image.setSeries(this);
            }
            LOG.fine("Added image " + image.getId() + " to series " + id);
        }
    }
    
    public void removeImage(DicomImage image) {
        if (image != null && images.contains(image)) {
            images.remove(image);
            if (image.getSeries() == this) {
                image.setSeries(null);
            }
            LOG.fine("Removed image " + image.getId() + " from series " + id);
        }
    }
    
    public DicomExam getExam() {
        return exam;
    }
    
    public void setExam(DicomExam exam) {
        if (this.exam != exam) {
            DicomExam oldExam = this.exam;
            this.exam = exam;
            
            if (oldExam != null) {
                oldExam.removeSeries(this);
            }
            
            if (exam != null && !exam.getSeries().contains(this)) {
                exam.addSeries(this);
            }
            
            LOG.fine("Set exam " + (exam != null ? exam.getId() : "null") + " for series " + id);
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s[id=%s, seriesInstanceUID=%s, modality=%s, seriesNumber=%s, seriesDescription=%s, bodyPartExamined=%s, seriesDate=%s, seriesTime=%s, imageCount=%d, exam=%s]",
            getClass().getSimpleName(), id, getSeriesInstanceUID(), getModality(), getSeriesNumber(),
            getSeriesDescription(), getBodyPartExamined(), getSeriesDate(), getSeriesTime(),
            images.size(), exam != null ? exam.getId() : "null");
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DicomSeries other = (DicomSeries) obj;
        return id != null && id.equals(other.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}