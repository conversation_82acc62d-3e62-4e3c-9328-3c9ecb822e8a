package com.ge.med.ct.dicom;

import java.nio.file.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.concurrent.ConcurrentHashMap;

import com.ge.med.ct.dicom.model.DicomExam;
import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.model.DicomSeries;
import com.ge.med.ct.dicom.model.DicomFileModel;
import com.ge.med.ct.dicom.io.DicomFileReader;
import com.ge.med.ct.dicom.io.DicomFileScanner;
import com.ge.med.ct.dicom.io.FileSystemUtil;
import com.ge.med.ct.dicom.util.DicomDataProvider;
import com.ge.med.ct.dicom.util.StatusCallback;
import com.ge.med.ct.dicom.util.DicomException;
import com.ge.med.ct.dicom.tag.DicomTag;
import com.ge.med.ct.config.AppConfig;

/**
 * DICOM数据管理器
 * 负责DICOM文件和数据的管理
 */
public class DicomDataManager implements DicomDataProvider {
    
    private static final Logger LOG = Logger.getLogger(DicomDataManager.class.getName());
    private static volatile DicomDataManager instance;
    
    private final Map<String, DicomExam> exams;
    private final Map<String, DicomSeries> series;
    private final Map<String, DicomImage> images;
    private final Map<String, Set<String>> examToSeriesMap;
    private final Map<String, Set<String>> seriesToImageMap;

    private WatchService watchService;
    private final DicomFileScanner scanner;
    private final DicomFileReader reader;
    private boolean isMonitoring;
    private final String rootDirectory;

    private DicomDataManager() {
        this.rootDirectory = AppConfig.getInstance().getString("dicom.scan.directory", "usr/g/sdc_image_pool/images");
        this.isMonitoring = false;

        this.exams = new ConcurrentHashMap<>();
        this.series = new ConcurrentHashMap<>();
        this.images = new ConcurrentHashMap<>();

        this.examToSeriesMap = new HashMap<>();
        this.seriesToImageMap = new HashMap<>();
        
        this.scanner = new DicomFileScanner();
        this.reader = new DicomFileReader();
    }

    public static DicomDataManager getInstance() {
        if (instance == null) {
            synchronized (DicomDataManager.class) {
                if (instance == null) {
                    instance = new DicomDataManager();
                }
            }
        }
        return instance;
    }

    /**
     * 加载DICOM文件
     * @param filePath 文件路径
     * @return DicomFileModel实例
     * @throws DicomException 如果加载失败
     */
    public DicomFileModel loadDicomFile(String filePath) throws DicomException {
        if (!FileSystemUtil.fileExists(filePath)) {
            throw new DicomException("File not found: " + filePath);
        }
        
        DicomFileModel fileModel = reader.readDicomFile(filePath);
        if (fileModel == null) {
            throw new DicomException("Failed to read DICOM file: " + filePath);
        }
        
        processDicomData(fileModel);
        return fileModel;
    }

    /**
     * 扫描目录并加载DICOM数据
     * @throws DicomException 如果扫描或加载失败
     */
    public void scanAndLoadData() throws DicomException {
        LOG.info("Starting DICOM data scan and load");
        
        // 清除现有数据
        clearData();
        
        try {
            // 使用DicomFileScanner扫描目录
            List<DicomFileModel> dicomFiles = scanner.scanDirectory(rootDirectory, true);
            
            if (dicomFiles.isEmpty()) {
                LOG.info("No DICOM files found in directory: " + rootDirectory);
                return;
            }
            
            LOG.info("Found " + dicomFiles.size() + " DICOM files to process");
            int processedFiles = 0;
            int failedFiles = 0;
            
            // 处理每个DICOM文件
            for (DicomFileModel fileModel : dicomFiles) {
                try {
                    // 处理DICOM数据
                    processDicomData(fileModel);
                    processedFiles++;
                    
                    if (processedFiles % 100 == 0) {
                        LOG.info("Processed " + processedFiles + " files");
                    }
                } catch (Exception e) {
                    LOG.warning("Error processing DICOM file: " + fileModel.getFilePath() + ", Error: " + e.getMessage());
                    failedFiles++;
                }
            }
            
            String summary = String.format("DICOM data scan completed. Processed: %d, Failed: %d", 
                processedFiles, failedFiles);
            LOG.info(summary);
            
        } catch (Exception e) {
            throw new DicomException("Failed to scan and load DICOM data: " + e.getMessage(), e);
        }
    }

    /**
     * 处理DICOM数据
     * @param fileModel DICOM文件模型
     * @throws DicomException 如果处理失败
     */
    private void processDicomData(DicomFileModel fileModel) throws DicomException {
        try {
            DicomImage image = createOrUpdateImage(fileModel);
            DicomSeries series = createOrUpdateSeries(fileModel);
            DicomExam exam = createOrUpdateExam(fileModel);
            
            if (image != null && series != null && exam != null) {
                series.addImage(image);
                exam.addSeries(series);
                
                // 更新映射关系
                examToSeriesMap.computeIfAbsent(exam.getId(), k -> new HashSet<>())
                    .add(series.getId());
                seriesToImageMap.computeIfAbsent(series.getId(), k -> new HashSet<>())
                    .add(image.getId());
            }
        } catch (Exception e) {
            throw new DicomException("Failed to process DICOM data: " + e.getMessage(), e);
        }
    }

    /**
     * 创建或更新图像
     * @param fileModel DICOM文件模型
     * @return DicomImage实例
     * @throws DicomException 如果创建或更新失败
     */
    private DicomImage createOrUpdateImage(DicomFileModel fileModel) throws DicomException {
        String imageId = fileModel.getId();
        DicomImage image = images.get(imageId);
        
        if (image == null) {
            image = new DicomImage(imageId);
            images.put(imageId, image);
        }
        
        // 更新图像属性
        DicomImage fileImage = fileModel.getImage();
        if (fileImage != null) {
            image.setRows(fileImage.getRows());
            image.setColumns(fileImage.getColumns());
            
            String positionValue = fileModel.getTagValueAsString(DicomTag.ImagePositionPatient.getTagId());
            if (positionValue != null) {
                image.addTag(DicomTag.ImagePositionPatient.getTagId(), 
                    new DicomTag(DicomTag.ImagePositionPatient.getTagId(), positionValue, DicomTag.ImagePositionPatient.getVr()));
            }
            
            String orientationValue = fileModel.getTagValueAsString(DicomTag.ImageOrientationPatient.getTagId());
            if (orientationValue != null) {
                image.addTag(DicomTag.ImageOrientationPatient.getTagId(), 
                    new DicomTag(DicomTag.ImageOrientationPatient.getTagId(), orientationValue, DicomTag.ImageOrientationPatient.getVr()));
            }
            
            String pixelValue = fileModel.getTagValueAsString(DicomTag.PixelData.getTagId());
            if (pixelValue != null) {
                image.addTag(DicomTag.PixelData.getTagId(), 
                    new DicomTag(DicomTag.PixelData.getTagId(), pixelValue, DicomTag.PixelData.getVr()));
            }
        }
        
        return image;
    }

    /**
     * 创建或更新序列
     * @param fileModel DICOM文件模型
     * @return DicomSeries实例
     * @throws DicomException 如果创建或更新失败
     */
    private DicomSeries createOrUpdateSeries(DicomFileModel fileModel) throws DicomException {
        String seriesInstanceUID = fileModel.getSeriesInstanceUID();
        DicomSeries series = this.series.get(seriesInstanceUID);
        
        if (series == null) {
            series = new DicomSeries(seriesInstanceUID);
            String modalityValue = fileModel.getModality();
            if (modalityValue != null) {
                series.addTag(DicomTag.Modality.getTagId(), 
                    new DicomTag(DicomTag.Modality.getTagId(), modalityValue, DicomTag.Modality.getVr()));
            }
            this.series.put(seriesInstanceUID, series);
        }
        
        String descValue = fileModel.getSeriesDescription();
        if (descValue != null) {
            series.addTag(DicomTag.SeriesDescription.getTagId(), 
                new DicomTag(DicomTag.SeriesDescription.getTagId(), descValue, DicomTag.SeriesDescription.getVr()));
        }
        
        String seriesNumber = fileModel.getSeriesNumber();
        if (seriesNumber != null) {
            try {
                series.setSeriesNumber(seriesNumber);
            } catch (Exception e) {
                LOG.warning("Invalid series number: " + seriesNumber);
            }
        }
        
        return series;
    }

    /**
     * 创建或更新检查
     * @param fileModel DICOM文件模型
     * @return DicomExam实例
     * @throws DicomException 如果创建或更新失败
     */
    private DicomExam createOrUpdateExam(DicomFileModel fileModel) throws DicomException {
        String studyInstanceUID = fileModel.getStudyInstanceUID();
        DicomExam exam = exams.get(studyInstanceUID);
        
        if (exam == null) {
            exam = new DicomExam(studyInstanceUID);
            exams.put(studyInstanceUID, exam);
        }
        
        // 更新检查属性
        exam.setPatientID(fileModel.getPatientId());
        exam.setPatientName(fileModel.getPatientName());
        exam.setStudyDate(fileModel.getStudyDate());
        exam.setStudyTime(fileModel.getStudyTime());
        
        // 设置检查号（从StudyID或AccessionNumber获取）
        String examId = fileModel.getTagValueAsString(DicomTag.StudyID.getTagId());
        if (examId != null) {
            exam.addTag(DicomTag.StudyID.getTagId(), 
                new DicomTag(DicomTag.StudyID.getTagId(), examId, DicomTag.StudyID.getVr()));
        }
        
        // 设置设备名称
        String stationName = fileModel.getTagValueAsString(DicomTag.StationName.getTagId());
        if (stationName != null) {
            exam.addTag(DicomTag.StationName.getTagId(), 
                new DicomTag(DicomTag.StationName.getTagId(), stationName, DicomTag.StationName.getVr()));
        }
        
        // 设置检查描述
        String description = fileModel.getTagValueAsString(DicomTag.StudyDescription.getTagId());
        if (description != null) {
            exam.addTag(DicomTag.StudyDescription.getTagId(), 
                new DicomTag(DicomTag.StudyDescription.getTagId(), description, DicomTag.StudyDescription.getVr()));
        }
        
        // 设置设备类型
        String modality = fileModel.getTagValueAsString(DicomTag.Modality.getTagId());
        if (modality != null) {
            exam.addTag(DicomTag.Modality.getTagId(), 
                new DicomTag(DicomTag.Modality.getTagId(), modality, DicomTag.Modality.getVr()));
        }
        
        // 设置MPPS状态
        String mppsStatus = fileModel.getTagValueAsString(DicomTag.PerformedProcedureStepStatus.getTagId());
        if (mppsStatus != null) {
            exam.addTag(DicomTag.PerformedProcedureStepStatus.getTagId(), 
                new DicomTag(DicomTag.PerformedProcedureStepStatus.getTagId(), mppsStatus, DicomTag.PerformedProcedureStepStatus.getVr()));
        }
        
        // 设置归档状态（如果有）
        String archiveStatus = fileModel.getTagValueAsString(DicomTag.InstanceAvailability.getTagId());
        if (archiveStatus != null) {
            exam.addTag(DicomTag.InstanceAvailability.getTagId(), 
                new DicomTag(DicomTag.InstanceAvailability.getTagId(), archiveStatus, DicomTag.InstanceAvailability.getVr()));
        }
        
        return exam;
    }

    /**
     * 清除所有数据
     */
    public void clearData() {
        exams.clear();
        series.clear();
        images.clear();
        examToSeriesMap.clear();
        seriesToImageMap.clear();
        LOG.info("Cleared all DICOM data");
    }
    
    // 数据访问接口
    @Override
    public List<DicomExam> getAllExams() {
        return new ArrayList<>(exams.values());
    }
    
    @Override
    public DicomExam getExam(String studyInstanceUID) {
        return exams.get(studyInstanceUID);
    }
    
    @Override
    public List<DicomSeries> getAllSeries() {
        return new ArrayList<>(series.values());
    }
    
    @Override
    public DicomSeries getSeries(String seriesInstanceUID) {
        return series.get(seriesInstanceUID);
    }
    
    @Override
    public List<DicomImage> getAllImages() {
        return new ArrayList<>(images.values());
    }
    
    @Override
    public DicomImage getImage(String imageId) {
        return images.get(imageId);
    }
    
    // 搜索接口
    @Override
    public List<DicomExam> searchExams(String patientId, String studyId) {
        List<DicomExam> result = new ArrayList<>();
        for (DicomExam exam : exams.values()) {
            if ((patientId == null || patientId.equals(exam.getPatientID())) &&
                (studyId == null || studyId.equals(exam.getStudyInstanceUID()))) {
                result.add(exam);
            }
        }
        return result;
    }

    @Override
    public void setStatusCallback(StatusCallback callback) {
    }

    @Override
    public List<DicomExam> getExamList() {
        return new ArrayList<>(exams.values());
    }

    @Override
    public boolean addSeries(DicomSeries series) {
        if (series == null || series.getId() == null) {
            LOG.warning("Invalid series data");
            return false;
        }
        this.series.put(series.getId(), series);
        return true;
    }

    @Override
    public void addSeriesToExam(String examId, String seriesId) {
        DicomSeries series = this.series.get(seriesId);
        if (series != null) {
            DicomExam exam = exams.get(examId);
            if (exam != null) {
                exam.addSeries(series);
            }
        }
    }

    @Override
    public void addImageToSeries(String seriesId, String imageId) {
        DicomImage image = images.get(imageId);
        if (image != null) {
            DicomSeries series = this.series.get(seriesId);
            if (series != null) {
                series.addImage(image);
            }
        }
    }

    @Override
    public List<DicomImage> searchImages(String seriesId, String imageNumber) {
        List<DicomImage> result = new ArrayList<>();
        if (seriesId == null) {
            return result;
        }
        DicomSeries series = this.series.get(seriesId);
        if (series != null) {
            for (DicomImage image : series.getImages()) {
                if (imageNumber == null || imageNumber.equals(image.getInstanceNumber())) {
                    result.add(image);
                }
            }
        }
        return result;
    }

    @Override
    public List<DicomSeries> searchSeries(String examId, String seriesNumber) {
        List<DicomSeries> result = new ArrayList<>();
        if (examId == null) {
            return result;
        }
        DicomExam exam = exams.get(examId);
        if (exam != null) {
            for (DicomSeries series : exam.getSeries()) {
                if (seriesNumber == null || String.valueOf(series.getSeriesNumber()).equals(seriesNumber)) {
                    result.add(series);
                }
            }
        }
        return result;
    }

    @Override
    public List<DicomExam> searchExaminations(String patientName, String examId) {
        return exams.values().stream()
            .filter(exam -> (patientName == null || exam.getPatientName().contains(patientName)) &&
(examId == null || exam.getId().contains(examId)))
            .collect(Collectors.toList());
    }

    @Override
    public List<DicomExam> searchByPatientName(String patientName) {
        return exams.values().stream()
            .filter(exam -> exam.getPatientName().contains(patientName))
            .collect(Collectors.toList());
    }

    @Override
    public boolean addImage(DicomImage image) {
        if (image == null || image.getId() == null) {
            LOG.warning("Invalid image data");
            return false;
        }
        images.put(image.getId(), image);
        return true;
    }

    @Override
    public CompletableFuture<DicomExam> getExamAsync(String examId) {
        return CompletableFuture.completedFuture(getExam(examId));
    }

    @Override
    public CompletableFuture<Void> addExamAsync(DicomExam exam) {
        return CompletableFuture.runAsync(() -> addExam(exam));
    }

    @Override
    public <T> T executeInTransaction(Supplier<T> operation) {
        try {
            return operation.get();
        } catch (Exception e) {
            LOG.severe("Transaction failed: " + e.getMessage());
            throw new RuntimeException("Transaction failed", e);
        }
    }

    @Override
    public boolean addExam(DicomExam exam) {
        if (exam == null || exam.getId() == null) {
            LOG.warning("Invalid exam data");
            return false;
        }
        exams.put(exam.getId(), exam);
        examToSeriesMap.putIfAbsent(exam.getId(), new HashSet<>());
        return true;
    }

    public void startFileMonitoring() {
        if (isMonitoring) {
            return;
        }

        try {
            watchService = FileSystems.getDefault().newWatchService();
            Path dirPath = Paths.get(rootDirectory);
            dirPath.register(watchService, 
                StandardWatchEventKinds.ENTRY_CREATE,
                StandardWatchEventKinds.ENTRY_DELETE,
                StandardWatchEventKinds.ENTRY_MODIFY);

            isMonitoring = true;
            CompletableFuture.runAsync(this::monitorDirectory);
        } catch (IOException e) {
            LOG.severe("Failed to start file monitoring: " + e.getMessage());
        }
    }

    private void monitorDirectory() {
        while (isMonitoring) {
            try {
                WatchKey key = watchService.take();
                for (WatchEvent<?> event : key.pollEvents()) {
                    WatchEvent.Kind<?> kind = event.kind();
                    Path fileName = (Path) event.context();
                    Path fullPath = Paths.get(rootDirectory).resolve(fileName);

                    if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
                        handleFileCreated(fullPath.toString());
                    } else if (kind == StandardWatchEventKinds.ENTRY_DELETE) {
                        handleFileDeleted(fullPath.toString());
                    } else if (kind == StandardWatchEventKinds.ENTRY_MODIFY) {
                        handleFileModified(fullPath.toString());
                    }
                }
                key.reset();
            } catch (InterruptedException e) {
                LOG.warning("File monitoring interrupted: " + e.getMessage());
                break;
            }
        }
    }

    private void handleFileCreated(String filePath) {
        if (reader.isDicomFile(filePath)) {
            try {
                DicomFileModel fileModel = loadDicomFile(filePath);
                if (fileModel != null) {
                    DicomImage image = fileModel.getImage();
                    if (image != null) {
                        addImage(image);
                    }
                }
            } catch (DicomException e) {
                LOG.warning("Error handling new file: " + filePath + ", Error: " + e.getMessage());
            }
        }
    }

    private void handleFileDeleted(String filePath) {
        // Remove the file from all maps
        images.entrySet().removeIf(entry -> filePath.equals(entry.getValue().getId()));
        updateRelationships();
    }

    private void handleFileModified(String filePath) {
        handleFileDeleted(filePath);
        handleFileCreated(filePath);
    }

    public void stopFileMonitoring() {
        isMonitoring = false;
        if (watchService != null) {
            try {
                watchService.close();
            } catch (IOException e) {
                LOG.severe("Error closing watch service: " + e.getMessage());
            }
        }
    }

    private void updateRelationships() {
        examToSeriesMap.clear();
        seriesToImageMap.clear();

        // rebuild relationships
        for (DicomExam exam : exams.values()) {
            for (DicomSeries series : exam.getSeries()) {
                examToSeriesMap.computeIfAbsent(exam.getId(), k -> new HashSet<>()).add(series.getId());
                for (DicomImage image : series.getImages()) {
                    seriesToImageMap.computeIfAbsent(series.getId(), k -> new HashSet<>()).add(image.getId());
                }
            }
        }
    }

    /**
     * 通过患者姓名模糊查询检查列表
     * @param patientName 患者姓名（支持模糊匹配）
     * @return 匹配的检查列表
     */
    public List<DicomExam> searchExamsByPatientName(String patientName) {
        if (patientName == null || patientName.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        String searchPattern = patientName.toLowerCase().trim();
        return exams.values().stream()
            .filter(exam -> exam.getPatientName() != null && exam.getPatientName().toLowerCase().contains(searchPattern))
            .collect(Collectors.toList());
    }

    /**
     * 通过患者ID查询检查列表
     * @param patientId 患者ID（精确匹配）
     * @return 匹配的检查列表
     */
    public List<DicomExam> searchExamsByPatientId(String patientId) {
        if (patientId == null || patientId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return exams.values().stream()
            .filter(exam -> patientId.equals(exam.getPatientID()))
            .collect(Collectors.toList());
    }

    /**
     * 通过患者姓名和ID组合查询检查列表
     * @param patientName 患者姓名（支持模糊匹配）
     * @param patientId 患者ID（精确匹配）
     * @return 匹配的检查列表
     */
    public List<DicomExam> searchExamsByPatient(String patientName, String patientId) {
        if ((patientName == null || patientName.trim().isEmpty()) && 
            (patientId == null || patientId.trim().isEmpty())) {
            return new ArrayList<>();
        }
        
        return exams.values().stream()
            .filter(exam -> {
                boolean nameMatch = patientName == null || patientName.trim().isEmpty() ||
                    (exam.getPatientName() != null && exam.getPatientName().toLowerCase().contains(patientName.toLowerCase().trim()));
                boolean idMatch = patientId == null || patientId.trim().isEmpty() ||
                                patientId.equals(exam.getPatientID());
                return nameMatch && idMatch;
            })
            .collect(Collectors.toList());
    }

    /**
     * 获取所有患者列表
     * @return 患者列表（去重）
     */
    public Set<String> getAllPatientNames() {
        return exams.values().stream()
            .map(DicomExam::getPatientName)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
    }

    /**
     * 获取所有患者ID列表
     * @return 患者ID列表（去重）
     */
    public Set<String> getAllPatientIds() {
        return exams.values().stream()
            .map(DicomExam::getPatientID)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
    }

    /**
     * 获取指定检查的所有序列
     * @param examId 检查ID
     * @return 序列列表
     */
    public List<DicomSeries> getSeriesForExam(String examId) {
        if (examId == null || examId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return examToSeriesMap.getOrDefault(examId, new HashSet<>())
            .stream()
            .map(seriesId -> series.get(seriesId))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 获取指定序列的所有图像
     * @param seriesId 序列ID
     * @return 图像列表
     */
    public List<DicomImage> getImagesForSeries(String seriesId) {
        if (seriesId == null || seriesId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return seriesToImageMap.getOrDefault(seriesId, new HashSet<>())
            .stream()
            .map(imageId -> images.get(imageId))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    @Override
    public String getTagValue(String imageId, String tagId) {
        if (imageId == null || tagId == null) {
            LOG.warning("Invalid imageId or tagId");
            return null;
        }
        
        DicomImage image = images.get(imageId);
        if (image == null) {
            LOG.warning("Image not found: " + imageId);
            return null;
        }
        
        return image.getTagValue(tagId);
    }
}