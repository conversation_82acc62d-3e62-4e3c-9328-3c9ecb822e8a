package com.ge.med.ct.dicom.io;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.channels.SeekableByteChannel;
import java.util.logging.Logger;

import org.dcm4che3.io.DicomInputStream;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.data.VR;

import com.ge.med.ct.dicom.DicomTagParser;
import com.ge.med.ct.dicom.model.DicomFileModel;
import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.tag.DicomTag;
import com.ge.med.ct.dicom.util.DicomException;

/**
 * DICOM文件读取器
 * 负责读取和解析DICOM文件
 */
public class DicomFileReader {
    private static final Logger LOGGER = Logger.getLogger(DicomFileReader.class.getName());
    private final DicomTagParser tagParser;
    
    // DICOM文件标识常量
    private static final int DICOM_MAGIC_OFFSET = 128;  // DICOM文件前128字节为保留区
    private static final String DICOM_MAGIC_STRING = "DICM";  // DICOM文件标识
    private static final int DICOM_MAGIC_LENGTH = 4;  // DICOM标识长度
    
    public DicomFileReader() {
        this.tagParser = new DicomTagParser();
    }
    
    /**
     * 检查文件是否为DICOM格式
     * 
     * @param filePath 文件路径
     * @return 是否为DICOM文件
     */
    public boolean isDicomFile(String filePath) {
        if (!FileSystemUtil.fileExists(filePath)) {
            return false;
        }
        
        try {
            byte[] magicBytes = new byte[DICOM_MAGIC_LENGTH];
            Path path = Paths.get(filePath);
            
            // 读取DICOM标识位置的字节
            try (SeekableByteChannel channel = Files.newByteChannel(path)) {
                channel.position(DICOM_MAGIC_OFFSET);
                channel.read(java.nio.ByteBuffer.wrap(magicBytes));
            }
            
            // 检查是否包含DICOM标识
            String magic = new String(magicBytes);
            return DICOM_MAGIC_STRING.equals(magic);
        } catch (IOException e) {
            LOGGER.warning("检查DICOM文件格式时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 读取DICOM文件并创建DicomFileModel
     * 
     * @param filePath 文件路径
     * @return DicomFileModel实例
     * @throws DicomException 如果读取或解析失败
     */
    public DicomFileModel readDicomFile(String filePath) throws DicomException {
        if (!isDicomFile(filePath)) {
            throw new DicomException("不是有效的DICOM文件", null, "readDicomFile");
        }
        
        try {
            // 创建DicomFileModel实例
            File file = new File(filePath);
            DicomFileModel model = new DicomFileModel(file.getName());
            model.setFilePath(filePath);
            model.setFileName(file.getName());
            model.setFileSize(file.length());
            
            // 使用DicomInputStream读取DICOM文件
            try (DicomInputStream dis = new DicomInputStream(file)) {
                // 读取DICOM属性
                Attributes attributes = dis.readDataset();
                
                // 使用DicomTagParser解析标签
                model = tagParser.parseDicomTags(model, attributes);
                
                // 读取像素数据
                readPixelData(model, attributes);
            }
            
            return model;
        } catch (Exception e) {
            throw new DicomException("读取DICOM文件失败: " + e.getMessage(), null, "readDicomFile", null, e);
        }
    }
    
    /**
     * 读取DICOM文件的像素数据
     * 
     * @param model DICOM文件模型
     * @param attributes DICOM属性
     * @throws DicomException 如果读取失败
     */
    private void readPixelData(DicomFileModel model, Attributes attributes) throws DicomException {
        try {
            DicomImage image = model.getImage();
            if (image == null) {
                throw new DicomException("DicomImage object is null", null, "readPixelData");
            }
            
            // 获取必要的图像属性
            int rows = attributes.getInt(Tag.Rows, 0);
            int columns = attributes.getInt(Tag.Columns, 0);
            int bitsAllocated = attributes.getInt(Tag.BitsAllocated, 16);
            int samplesPerPixel = attributes.getInt(Tag.SamplesPerPixel, 1);
            int pixelRepresentation = attributes.getInt(Tag.PixelRepresentation, 0);
            String photometricInterpretation = attributes.getString(Tag.PhotometricInterpretation);
            
            // 验证必要的属性
            if (rows <= 0 || columns <= 0) {
                throw new DicomException("无效的图像尺寸", null, "readPixelData");
            }
            
            // 设置图像属性
            addTagToModel(model, DicomTag.Rows.getTagId(), String.valueOf(rows), VR.US);
            addTagToModel(model, DicomTag.Columns.getTagId(), String.valueOf(columns), VR.US);
            addTagToModel(model, DicomTag.BitsAllocated.getTagId(), String.valueOf(bitsAllocated), VR.US);
            addTagToModel(model, DicomTag.SamplesPerPixel.getTagId(), String.valueOf(samplesPerPixel), VR.US);
            addTagToModel(model, DicomTag.PixelRepresentation.getTagId(), String.valueOf(pixelRepresentation), VR.US);
            addTagToModel(model, DicomTag.PhotometricInterpretation.getTagId(), photometricInterpretation, VR.CS);
            
            // 读取像素数据
            byte[] pixelData = attributes.getBytes(Tag.PixelData);
            if (pixelData != null && pixelData.length > 0) {
                addTagToModel(model, DicomTag.PixelData.getTagId(), pixelData);
            }
            
            // 设置图像位置和方向
            float[] imagePosition = attributes.getFloats(Tag.ImagePositionPatient);
            if (imagePosition != null && imagePosition.length == 3) {
                addTagToModel(model, DicomTag.ImagePositionPatient.getTagId(), 
                    formatFloatArray(imagePosition), VR.DS);
            }
            
            float[] imageOrientation = attributes.getFloats(Tag.ImageOrientationPatient);
            if (imageOrientation != null && imageOrientation.length == 6) {
                addTagToModel(model, DicomTag.ImageOrientationPatient.getTagId(), 
                    formatFloatArray(imageOrientation), VR.DS);
            }
            
            // 设置像素间距
            float[] pixelSpacing = attributes.getFloats(Tag.PixelSpacing);
            if (pixelSpacing != null && pixelSpacing.length == 2) {
                addTagToModel(model, String.valueOf(Tag.PixelSpacing), 
                    formatFloatArray(pixelSpacing), VR.DS);
            }
            
            // 设置窗宽窗位
            float[] windowCenter = attributes.getFloats(Tag.WindowCenter);
            float[] windowWidth = attributes.getFloats(Tag.WindowWidth);
            if (windowCenter != null && windowWidth != null && 
                windowCenter.length > 0 && windowWidth.length > 0) {
                addTagToModel(model, DicomTag.WindowCenter.getTagId(), 
                    String.valueOf(windowCenter[0]), VR.DS);
                addTagToModel(model, DicomTag.WindowWidth.getTagId(), 
                    String.valueOf(windowWidth[0]), VR.DS);
            }
            
            // 设置重缩放参数
            float rescaleSlope = attributes.getFloat(Tag.RescaleSlope, 1.0f);
            float rescaleIntercept = attributes.getFloat(Tag.RescaleIntercept, 0.0f);
            addTagToModel(model, DicomTag.RescaleSlope.getTagId(), 
                String.valueOf(rescaleSlope), VR.DS);
            addTagToModel(model, DicomTag.RescaleIntercept.getTagId(), 
                String.valueOf(rescaleIntercept), VR.DS);
            
        } catch (Exception e) {
            throw new DicomException("读取像素数据失败: " + e.getMessage(), null, "readPixelData", null, e);
        }
    }
    
    /**
     * 将浮点数组格式化为字符串
     */
    private String formatFloatArray(float[] values) {
        if (values == null || values.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < values.length; i++) {
            if (i > 0) {
                sb.append("\\");
            }
            sb.append(String.format("%.6f", values[i]));
        }
        return sb.toString();
    }
    
    /**
     * 添加标签到模型
     */
    private void addTagToModel(DicomFileModel model, String tagId, String value, VR vr) throws DicomException {
        if (tagId != null && value != null) {
            DicomTag tag = new DicomTag(tagId, value, vr);
            model.addTag(tagId, tag);
        }
    }
    
    /**
     * 添加标签到模型（字节数组值）
     */
    private void addTagToModel(DicomFileModel model, String tagId, byte[] value) {
        if (tagId != null && value != null) {
            model.addTag(tagId, value);
        }
    }
    
    /**
     * 获取DICOM文件的元数据信息
     * 
     * @param filePath 文件路径
     * @return 包含元数据的DicomFileModel实例
     * @throws DicomException 如果读取失败
     */
    public DicomFileModel getMetadata(String filePath) throws DicomException {
        if (!isDicomFile(filePath)) {
            throw new DicomException("不是有效的DICOM文件", null, "getMetadata");
        }
        
        try {
            File file = new File(filePath);
            DicomFileModel model = new DicomFileModel(file.getName());
            
            // 使用DicomInputStream读取DICOM文件
            try (DicomInputStream dis = new DicomInputStream(file)) {
                // 读取DICOM属性
                Attributes attributes = dis.readDataset();
                
                // 使用DicomTagParser解析标签
                model = tagParser.parseDicomTags(model, attributes);
            }
            
            return model;
        } catch (Exception e) {
            throw new DicomException("读取元数据失败: " + e.getMessage(), null, "getMetadata", null, e);
        }
    }

    /**
     * 验证DICOM文件
     * 检查文件是否包含所有必要的DICOM标签
     * 
     * @param filePath 文件路径
     * @return 是否有效
     */
    public boolean validateDicomFile(String filePath) {
        try {
            DicomFileModel model = readDicomFile(filePath);
            if (model == null) {
                return false;
            }
            
            // 验证必要的标签
            String[] requiredTags = {
                DicomTag.SOPInstanceUID.getTagId(),
                DicomTag.Modality.getTagId(),
                DicomTag.PatientName.getTagId(),
                DicomTag.PatientID.getTagId(),
                DicomTag.StudyInstanceUID.getTagId(),
                DicomTag.SeriesInstanceUID.getTagId()
            };
            
            for (String tagId : requiredTags) {
                if (model.getTag(tagId) == null) {
                    LOGGER.warning("缺少必要的DICOM标签 " + tagId + ": " + filePath);
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            LOGGER.warning("验证DICOM文件失败: " + filePath + ", 错误: " + e.getMessage());
            return false;
        }
    }
} 