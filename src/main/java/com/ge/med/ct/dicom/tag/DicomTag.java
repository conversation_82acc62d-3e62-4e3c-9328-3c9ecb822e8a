package com.ge.med.ct.dicom.tag;

import java.util.logging.Logger;
import java.util.regex.Pattern;

import org.dcm4che3.data.VR;

import com.ge.med.ct.dicom.util.DicomException;

/**
 * DICOM标签类
 * 表示一个DICOM标签及其值
 */
public class DicomTag {
    
    private static final Logger LOG = Logger.getLogger(DicomTag.class.getName());
    
    private String tagId;
    private String name;
    private String value;
    private byte[] rawValue;
    private VR vr;
    private String description;
    private boolean required;
    private boolean retired;
    private String vm;
    private boolean privateTag;
    
    // 预定义标签 - Patient相关
    public static final DicomTag PatientID = createTag("00100020", "Patient ID", null, VR.LO, "Patient ID", true);
    public static final DicomTag PatientName = createTag("00100010", "Patient Name", null, VR.PN, "Patient Name", true);
    public static final DicomTag PatientSex = createTag("00100040", "Patient Sex", null, VR.CS, "Patient Sex", true);
    public static final DicomTag PatientBirthDate = createTag("00100030", "Patient Birth Date", null, VR.DA, "Patient Birth Date", false);
    public static final DicomTag PatientAge = createTag("00101010", "Patient Age", null, VR.AS, "Patient Age", false);
    public static final DicomTag PatientWeight = createTag("00101030", "Patient Weight", null, VR.DS, "Patient Weight", false);
    public static final DicomTag PatientSize = createTag("00101020", "Patient Size", null, VR.DS, "Patient Size", false);
    public static final DicomTag PatientState = createTag("00380500", "Patient State", null, VR.LO, "Patient State", false);
    public static final DicomTag PregnancyStatus = createTag("001021C0", "Pregnancy Status", null, VR.US, "Pregnancy Status", false);
    public static final DicomTag MedicalAlerts = createTag("00102000", "Medical Alerts", null, VR.LO, "Medical Alerts", false);
    public static final DicomTag Allergies = createTag("00102110", "Allergies", null, VR.LO, "Allergies", false);
    public static final DicomTag SpecialNeeds = createTag("00102110", "Special Needs", null, VR.LO, "Special Needs", false);
    public static final DicomTag PatientComments = createTag("00104000", "Patient Comments", null, VR.LT, "Patient Comments", false);

    // 预定义标签 - Study相关
    public static final DicomTag StudyInstanceUID = createTag("0020000D", "Study Instance UID", null, VR.UI, "Study Instance UID", true);
    public static final DicomTag StudyID = createTag("00200010", "Study ID", null, VR.SH, "Study ID", false);
    public static final DicomTag StudyDate = createTag("00080020", "Study Date", null, VR.DA, "Study Date", true);
    public static final DicomTag StudyTime = createTag("00080030", "Study Time", null, VR.TM, "Study Time", true);
    public static final DicomTag StudyDescription = createTag("00081030", "Study Description", null, VR.LO, "Study Description", false);
    public static final DicomTag AccessionNumber = createTag("00080050", "Accession Number", null, VR.SH, "Accession Number", false);
    public static final DicomTag ReferringPhysicianName = createTag("00080090", "Referring Physician Name", null, VR.PN, "Referring Physician Name", false);
    public static final DicomTag StudyComments = createTag("00324000", "Study Comments", null, VR.LT, "Study Comments", false);
    public static final DicomTag StudyStatus = createTag("0032000A", "Study Status", null, VR.CS, "Study Status", false);
    public static final DicomTag StudyPriority = createTag("0032000C", "Study Priority", null, VR.CS, "Study Priority", false);
    public static final DicomTag StudyCompletionDate = createTag("00321000", "Study Completion Date", null, VR.DA, "Study Completion Date", false);
    public static final DicomTag StudyCompletionTime = createTag("00321001", "Study Completion Time", null, VR.TM, "Study Completion Time", false);
    public static final DicomTag StudyVerifiedDate = createTag("00321002", "Study Verified Date", null, VR.DA, "Study Verified Date", false);
    public static final DicomTag StudyVerifiedTime = createTag("00321003", "Study Verified Time", null, VR.TM, "Study Verified Time", false);
    public static final DicomTag StudyReadDate = createTag("00321004", "Study Read Date", null, VR.DA, "Study Read Date", false);
    public static final DicomTag StudyReadTime = createTag("00321005", "Study Read Time", null, VR.TM, "Study Read Time", false);
    public static final DicomTag StudyType = createTag("00321006", "Study Type", null, VR.CS, "Study Type", false);
    public static final DicomTag StudyReason = createTag("00321007", "Study Reason", null, VR.CS, "Study Reason", false);

    // 预定义标签 - Series相关
    public static final DicomTag SeriesInstanceUID = createTag("0020000E", "Series Instance UID", null, VR.UI, "Series Instance UID", true);
    public static final DicomTag SeriesNumber = createTag("00200011", "Series Number", null, VR.IS, "Series Number", true);
    public static final DicomTag SeriesDescription = createTag("0008103E", "Series Description", null, VR.LO, "Series Description", false);
    public static final DicomTag SeriesDate = createTag("00080021", "Series Date", null, VR.DA, "Series Date", false);
    public static final DicomTag SeriesTime = createTag("00080031", "Series Time", null, VR.TM, "Series Time", false);
    public static final DicomTag SeriesType = createTag("00400009", "Series Type", null, VR.CS, "Series Type", false);
    public static final DicomTag SeriesUID = createTag("0020000E", "Series UID", null, VR.UI, "Series UID", true);
    public static final DicomTag SeriesStatus = createTag("00200062", "Series Status", null, VR.CS, "Series Status", false);
    public static final DicomTag SeriesPriority = createTag("00200063", "Series Priority", null, VR.CS, "Series Priority", false);
    public static final DicomTag SeriesCompletionDate = createTag("00200064", "Series Completion Date", null, VR.DA, "Series Completion Date", false);
    public static final DicomTag SeriesCompletionTime = createTag("00200065", "Series Completion Time", null, VR.TM, "Series Completion Time", false);
    public static final DicomTag SeriesVerifiedDate = createTag("00200066", "Series Verified Date", null, VR.DA, "Series Verified Date", false);
    public static final DicomTag SeriesVerifiedTime = createTag("00200067", "Series Verified Time", null, VR.TM, "Series Verified Time", false);
    public static final DicomTag SeriesReadDate = createTag("00200068", "Series Read Date", null, VR.DA, "Series Read Date", false);
    public static final DicomTag SeriesReadTime = createTag("00200069", "Series Read Time", null, VR.TM, "Series Read Time", false);
    public static final DicomTag BodyPartExamined = createTag("00180015", "Body Part Examined", null, VR.CS, "Body Part Examined", false);
    public static final DicomTag Modality = createTag("00080060", "Modality", null, VR.CS, "Modality", true);

    // 预定义标签 - Image相关
    public static final DicomTag SOPInstanceUID = createTag("00080018", "SOP Instance UID", null, VR.UI, "SOP Instance UID", true);
    public static final DicomTag InstanceNumber = createTag("00200013", "Instance Number", null, VR.IS, "Instance Number", true);
    public static final DicomTag ImageType = createTag("00080008", "Image Type", null, VR.CS, "Image Type", true);
    public static final DicomTag ImageDate = createTag("00080022", "Image Date", null, VR.DA, "Image Date", false);
    public static final DicomTag ImageTime = createTag("00080032", "Image Time", null, VR.TM, "Image Time", false);
    public static final DicomTag ImagePosition = createTag("00200032", "Image Position", null, VR.DS, "Image Position", false);
    public static final DicomTag ImageOrientation = createTag("00200037", "Image Orientation", null, VR.DS, "Image Orientation", false);
    public static final DicomTag ImagePositionPatient = createTag("00200032", "Image Position Patient", null, VR.DS, "Image Position Patient", false);
    public static final DicomTag ImageOrientationPatient = createTag("00200037", "Image Orientation Patient", null, VR.DS, "Image Orientation Patient", false);
    public static final DicomTag SliceLocation = createTag("00201041", "Slice Location", null, VR.DS, "Slice Location", false);
    public static final DicomTag ImageIndex = createTag("00540052", "Image Index", null, VR.US, "Image Index", false);
    public static final DicomTag ImageComments = createTag("00204000", "Image Comments", null, VR.LT, "Image Comments", false);
    public static final DicomTag Rows = createTag("00280010", "Rows", null, VR.US, "Number of rows in the image", true);
    public static final DicomTag Columns = createTag("00280011", "Columns", null, VR.US, "Number of columns in the image", true);
    public static final DicomTag PixelData = createTag("7FE00010", "Pixel Data", null, VR.OW, "Pixel Data", true);
    public static final DicomTag BitsAllocated = createTag("00280100", "Bits Allocated", null, VR.US, "Number of bits allocated for each pixel sample", true);
    public static final DicomTag BitsStored = createTag("00280101", "Bits Stored", null, VR.US, "Number of bits stored for each pixel sample", true);
    public static final DicomTag HighBit = createTag("00280102", "High Bit", null, VR.US, "Most significant bit position of pixel sample", true);
    public static final DicomTag PixelRepresentation = createTag("00280103", "Pixel Representation", null, VR.US, "Data representation of the pixel samples", true);
    public static final DicomTag SamplesPerPixel = createTag("00280002", "Samples per Pixel", null, VR.US, "Number of samples per pixel", true);
    public static final DicomTag PlanarConfiguration = createTag("00280006", "Planar Configuration", null, VR.US, "Indicates whether the pixel data are sent color-by-plane or color-by-pixel", true);
    public static final DicomTag PhotometricInterpretation = createTag("00280004", "Photometric Interpretation", null, VR.CS, "Photometric Interpretation", true);
    public static final DicomTag WindowCenter = createTag("00281050", "Window Center", null, VR.DS, "Center of the window for display", false);
    public static final DicomTag WindowWidth = createTag("00281051", "Window Width", null, VR.DS, "Width of the window for display", false);
    public static final DicomTag RescaleIntercept = createTag("00281052", "Rescale Intercept", null, VR.DS, "Value of b in the equation y = mx + b", false);
    public static final DicomTag RescaleSlope = createTag("00281053", "Rescale Slope", null, VR.DS, "Value of m in the equation y = mx + b", false);
    public static final DicomTag RescaleType = createTag("00281054", "Rescale Type", null, VR.LO, "Specifies the output units of the rescale operation", false);
    public static final DicomTag SliceThickness = createTag("00180050", "Slice Thickness", null, VR.DS, "Slice Thickness", false);
    public static final DicomTag PixelSpacing = createTag("00280030", "Pixel Spacing", null, VR.DS, "Physical distance between the center of each pixel", true);

    // 预定义标签 - Equipment相关
    public static final DicomTag InstitutionAddress = createTag("00080081", "Institution Address", null, VR.ST, "Institution Address", false);
    public static final DicomTag InstitutionDepartmentName = createTag("00081040", "Institution Department Name", null, VR.LO, "Institution Department Name", false);
    public static final DicomTag DateOfLastCalibration = createTag("00181201", "Date of Last Calibration", null, VR.DA, "Date of Last Calibration", false);
    public static final DicomTag TimeOfLastCalibration = createTag("00181202", "Time of Last Calibration", null, VR.TM, "Time of Last Calibration", false);
    public static final DicomTag StationName = createTag("00081010", "Station Name", null, VR.SH, "Station Name", false);
    public static final DicomTag PerformedProcedureStepStatus = createTag("00400252", "Performed Procedure Step Status", null, VR.CS, "Performed Procedure Step Status", false);
    public static final DicomTag InstanceAvailability = createTag("00080056", "Instance Availability", null, VR.CS, "Instance Availability", false);

    // 预定义标签 - Protocol相关
    public static final DicomTag ProtocolContextSequence = createTag("00400200", "Protocol Context Sequence", null, VR.SQ, "Protocol Context Sequence", false);

    // 预定义标签 - Acquisition相关
    public static final DicomTag AcquisitionNumber = createTag("00200012", "Acquisition Number", null, VR.IS, "Acquisition Number", false);
    public static final DicomTag AcquisitionDeviceProcessingDescription = createTag("00181401", "Acquisition Device Processing Description", null, VR.LO, "Acquisition Device Processing Description", false);
    public static final DicomTag AcquisitionDeviceProcessingCode = createTag("00181402", "Acquisition Device Processing Code", null, VR.CS, "Acquisition Device Processing Code", false);
    public static final DicomTag AcquisitionStartCondition = createTag("00181403", "Acquisition Start Condition", null, VR.CS, "Acquisition Start Condition", false);
    public static final DicomTag AcquisitionStartConditionData = createTag("00181404", "Acquisition Start Condition Data", null, VR.US, "Acquisition Start Condition Data", false);
    public static final DicomTag AcquisitionTerminationCondition = createTag("00181405", "Acquisition Termination Condition", null, VR.CS, "Acquisition Termination Condition", false);
    public static final DicomTag AcquisitionTerminationConditionData = createTag("00181406", "Acquisition Termination Condition Data", null, VR.US, "Acquisition Termination Condition Data", false);
    public static final DicomTag AcquisitionContextSequence = createTag("00400256", "Acquisition Context Sequence", null, VR.SQ, "Acquisition Context Sequence", false);
    public static final DicomTag AcquisitionProtocolElementSequence = createTag("00400260", "Acquisition Protocol Element Sequence", null, VR.SQ, "Acquisition Protocol Element Sequence", false);
    public static final DicomTag AcquisitionDuration = createTag("00180012", "Acquisition Duration", null, VR.DS, "Acquisition Duration", false);

    // 预定义标签 - Content相关
    public static final DicomTag ContentDate = createTag("00080023", "Content Date", null, VR.DA, "Content Date", false);
    public static final DicomTag ContentTime = createTag("00080033", "Content Time", null, VR.TM, "Content Time", false);

    /**
     * 创建DICOM标签
     */
    public DicomTag(String tagId, String value, VR vr) throws DicomException {
        if (tagId == null || tagId.trim().isEmpty()) {
            throw new DicomException("Tag ID cannot be null or empty");
        }
        this.tagId = DicomTagUtil.normalizeTagId(tagId);
        this.value = value;
        this.vr = vr != null ? vr : VR.UN;
        this.rawValue = value != null ? value.getBytes() : null;
    }
    
    /**
     * 创建DICOM标签（带名称和描述）
     */
    public DicomTag(String tagId, String name, String value, VR vr, String description) throws DicomException {
        this(tagId, value, vr);
        this.name = name;
        this.description = description;
    }
    
    public String getTagId() {
        return tagId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
        this.rawValue = value != null ? value.getBytes() : null;
        LOG.fine("Set value for tag " + tagId + ": " + value);
    }
    
    public byte[] getRawValue() {
        return rawValue;
    }
    
    public void setRawValue(byte[] rawValue) {
        this.rawValue = rawValue;
        this.value = rawValue != null ? new String(rawValue) : null;
        LOG.fine("Set raw value for tag " + tagId);
    }
    
    public VR getVr() {
        return vr;
    }
    
    public void setVr(VR vr) {
        this.vr = vr != null ? vr : VR.UN;
        LOG.fine("Set VR for tag " + tagId + ": " + vr);
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public boolean isRequired() {
        return required;
    }
    
    public void setRequired(boolean required) {
        this.required = required;
    }
    
    public boolean isRetired() {
        return retired;
    }
    
    public void setRetired(boolean retired) {
        this.retired = retired;
    }
    
    public String getValueAsString() {
        return value;
    }
    
    public Integer getValueAsInteger() {
        try {
            return value != null ? Integer.parseInt(value.trim()) : null;
        } catch (NumberFormatException e) {
            LOG.warning("Failed to parse integer value for tag " + tagId + ": " + value);
            return null;
        }
    }
    
    public Float getValueAsFloat() {
        try {
            return value != null ? Float.parseFloat(value.trim()) : null;
        } catch (NumberFormatException e) {
            LOG.warning("Failed to parse float value for tag " + tagId + ": " + value);
            return null;
        }
    }
    
    public Double getValueAsDouble() {
        try {
            return value != null ? Double.parseDouble(value.trim()) : null;
        } catch (NumberFormatException e) {
            LOG.warning("Failed to parse double value for tag " + tagId + ": " + value);
            return null;
        }
    }
    
    @Override
    public String toString() {
        return String.format("DicomTag[id=%s, name=%s, value=%s, vr=%s]", 
            tagId, name, value, vr);
    }

    /**
     * 创建新的DICOM标签
     * @param tagId 标签ID
     * @param name 标签名称
     * @param value 标签值
     * @param vr 值表示
     * @param description 描述
     * @param required 是否必需
     * @return 创建的DicomTag实例，如果创建失败则返回null
     */
    public static DicomTag createNewTag(String tagId, String name, String value, VR vr, String description, boolean required) {
        try {
            DicomTag tag = new DicomTag(tagId, name, value, vr, description);
            tag.setRequired(required);
            return tag;
        } catch (DicomException e) {
            LOG.severe("Failed to create tag " + tagId + ": " + e.getMessage());
            return null;
        }
    }

    private static DicomTag createTag(String tagId, String name, String value, VR vr, String description, boolean required) {
        try {
            DicomTag tag = new DicomTag(tagId, name, value, vr, description);
            tag.setRequired(required);
            return tag;
        } catch (DicomException e) {
            LOG.severe("Failed to create tag " + tagId + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取标签组号
     */
    public int getGroup() {
        try {
            return Integer.parseInt(tagId.substring(0, 4), 16);
        } catch (Exception e) {
            LOG.warning("Failed to parse group from tag ID: " + tagId);
            return 0;
        }
    }

    /**
     * 获取标签元素号
     */
    public int getElement() {
        try {
            return Integer.parseInt(tagId.substring(4), 16);
        } catch (Exception e) {
            LOG.warning("Failed to parse element from tag ID: " + tagId);
            return 0;
        }
    }
    
    /**
     * 获取值多重性
     */
    public String getVM() {
        return vm;
    }

    /**
     * 设置值多重性
     */
    public void setVM(String vm) {
        this.vm = vm;
    }

    /**
     * 是否为私有标签
     */
    public boolean isPrivate() {
        return privateTag;
    }

    /**
     * 设置是否为私有标签
     */
    public void setPrivate(boolean privateTag) {
        this.privateTag = privateTag;
    }
    
    /**
     * 验证标签值
     */
    public boolean validate(String value) {
        if (value == null || value.trim().isEmpty()) {
            return !required;
        }
        
        // 根据VR类型验证值格式
        Pattern pattern = DicomTagUtil.getVRPattern(vr.name());
        if (pattern == null) {
            return true; // 如果没有对应的验证模式，默认通过
        }
        
        return pattern.matcher(value).matches();
    }
} 