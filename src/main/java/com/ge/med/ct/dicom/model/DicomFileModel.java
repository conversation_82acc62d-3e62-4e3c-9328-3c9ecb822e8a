package com.ge.med.ct.dicom.model;

import java.util.*;
import java.util.logging.Logger;
import org.dcm4che3.data.VR;
import com.ge.med.ct.dicom.tag.DicomTag;
import com.ge.med.ct.dicom.util.DicomException;

/**
 * DICOM文件模型类
 * 管理DICOM文件的属性和标签
 */
public class DicomFileModel {
    private static final Logger LOG = Logger.getLogger(DicomFileModel.class.getName());
    
    private String id;
    private String filePath;
    private String fileName;
    private String fileType;
    private long fileSize;
    private DicomImage image;

    private Map<String, DicomTag> tags;
    
    public DicomFileModel(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException("ID cannot be null or empty");
        }
        this.id = id;
        this.tags = new HashMap<>();
        this.image = new DicomImage(id);
    }
    
    public String getId() {
        return id;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
        LOG.fine("Set file path for file " + id + ": " + filePath);
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
        LOG.fine("Set file name for file " + id + ": " + fileName);
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
        LOG.fine("Set file size for file " + id + ": " + fileSize);
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
        LOG.fine("Set file type for file " + id + ": " + fileType);
    }
    
    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }
    
    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            LOG.fine("Added tag " + tagId + " to file " + id);
        }
    }
    
    /**
     * 添加标签（支持字节数组值）
     */
    public void addTag(String tagId, byte[] value) {
        if (tagId != null && value != null) {
            try {
                DicomTag tag = new DicomTag(tagId, new String(value), VR.UN);
                tags.put(tagId, tag);
                LOG.fine("Added tag " + tagId + " with byte array value to file " + id);
            } catch (DicomException e) {
                LOG.warning("Failed to add tag " + tagId + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * 验证标签
     */
    public boolean validateTag(String tagId, VR expectedVR) {
        DicomTag tag = getTag(tagId);
        if (tag == null) {
            return false;
        }
        return tag.getVr() == expectedVR;
    }
    
    public String getTagValueAsString(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }
    
    public Integer getTagValueAsInteger(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsInteger() : null;
    }
    
    public Float getTagValueAsFloat(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsFloat() : null;
    }
    
    public Double getTagValueAsDouble(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsDouble() : null;
    }

    // DICOM标签访问方法
    public String getStudyInstanceUID() {
        return getTagValueAsString(DicomTag.StudyInstanceUID.getTagId());
    }

    public String getSeriesInstanceUID() {
        return getTagValueAsString(DicomTag.SeriesInstanceUID.getTagId());
    }

    public String getPatientId() {
        return getTagValueAsString(DicomTag.PatientID.getTagId());
    }

    public String getPatientName() {
        return getTagValueAsString(DicomTag.PatientName.getTagId());
    }

    public String getStudyDate() {
        return getTagValueAsString(DicomTag.StudyDate.getTagId());
    }

    public String getStudyTime() {
        return getTagValueAsString(DicomTag.StudyTime.getTagId());
    }

    public String getAccessionNumber() {
        return getTagValueAsString(DicomTag.AccessionNumber.getTagId());
    }

    public String getModality() {
        return getTagValueAsString(DicomTag.Modality.getTagId());
    }

    public String getSeriesDescription() {
        return getTagValueAsString(DicomTag.SeriesDescription.getTagId());
    }

    public String getSeriesNumber() {
        return getTagValueAsString(DicomTag.SeriesNumber.getTagId());
    }

    public DicomImage getImage() {
        return image;
    }

    public void setImage(DicomImage image) {
        this.image = image;
        LOG.fine("Set image for file " + id);
    }
    
    @Override
    public String toString() {
        return String.format("%s[id=%s, filePath=%s, fileName=%s, fileSize=%d, fileType=%s, tags=%s]",
            getClass().getSimpleName(), id, filePath, fileName, fileSize, fileType, tags);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DicomFileModel other = (DicomFileModel) obj;
        return id != null && id.equals(other.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}