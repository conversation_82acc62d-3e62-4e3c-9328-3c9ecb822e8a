package com.ge.med.ct.dicom.util;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

import com.ge.med.ct.dicom.model.DicomExam;
import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.model.DicomSeries;

/**
 * DICOM数据提供者接口
 * 提供DICOM数据的访问和管理功能
 */
public interface DicomDataProvider {
    
    /**
     * 获取所有检查列表
     */
    List<DicomExam> getAllExams();
    
    /**
     * 获取指定检查
     */
    DicomExam getExam(String studyInstanceUID);
    
    /**
     * 获取所有序列列表
     */
    List<DicomSeries> getAllSeries();
    
    /**
     * 获取指定序列
     */
    DicomSeries getSeries(String seriesInstanceUID);
    
    /**
     * 获取所有图像列表
     */
    List<DicomImage> getAllImages();
    
    /**
     * 获取指定图像
     */
    DicomImage getImage(String imageId);
    
    /**
     * 搜索检查
     */
    List<DicomExam> searchExams(String patientId, String studyId);
    
    /**
     * 设置状态回调
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * 获取检查列表
     */
    List<DicomExam> getExamList();
    
    /**
     * 添加序列
     */
    boolean addSeries(DicomSeries series);
    
    /**
     * 添加序列到检查
     */
    void addSeriesToExam(String examId, String seriesId);
    
    /**
     * 添加图像到序列
     */
    void addImageToSeries(String seriesId, String imageId);
    
    /**
     * 搜索图像
     */
    List<DicomImage> searchImages(String seriesId, String imageNumber);
    
    /**
     * 搜索序列
     */
    List<DicomSeries> searchSeries(String examId, String seriesNumber);
    
    /**
     * 搜索检查
     */
    List<DicomExam> searchExaminations(String patientName, String examId);
    
    /**
     * 通过患者姓名搜索
     */
    List<DicomExam> searchByPatientName(String patientName);
    
    /**
     * 添加图像
     */
    boolean addImage(DicomImage image);
    
    /**
     * 异步获取检查
     */
    CompletableFuture<DicomExam> getExamAsync(String examId);
    
    /**
     * 异步添加检查
     */
    CompletableFuture<Void> addExamAsync(DicomExam exam);
    
    /**
     * 在事务中执行操作
     */
    <T> T executeInTransaction(Supplier<T> operation);
    
    /**
     * 获取指定检查的所有序列
     */
    List<DicomSeries> getSeriesForExam(String examId);
    
    /**
     * 获取指定序列的所有图像
     */
    List<DicomImage> getImagesForSeries(String seriesId);
    
    /**
     * 获取图像标签值
     * @param imageId 图像ID
     * @param tagId 标签ID
     * @return 标签值
     */
    String getTagValue(String imageId, String tagId);
    
    /**
     * 添加检查
     */
    boolean addExam(DicomExam exam);
} 