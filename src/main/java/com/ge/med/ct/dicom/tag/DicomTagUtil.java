package com.ge.med.ct.dicom.tag;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.regex.Pattern;
import java.util.Objects;
import org.dcm4che3.data.VR;
import com.ge.med.ct.dicom.model.DicomImage;
import com.ge.med.ct.dicom.util.DicomException;

import java.util.logging.Logger;

/**
 * DICOM标签工具类
 * 整合了标签验证和文件属性处理功能
 */
public class DicomTagUtil {
    private static final Logger LOG = Logger.getLogger(DicomTagUtil.class.getName());
    private static final Map<String, String> normalizedTagIdCache = new ConcurrentHashMap<>();
    
    // VR类型及其对应的值格式验证
    private static final Map<String, Pattern> VR_PATTERNS = new HashMap<>();
    static {
        // 添加常用VR类型的验证模式
        VR_PATTERNS.put("AE", Pattern.compile("^[\\x20-\\x7E]{1,16}$")); // Application Entity
        VR_PATTERNS.put("AS", Pattern.compile("^\\d{3}[DWMY]$")); // Age String
        VR_PATTERNS.put("CS", Pattern.compile("^[A-Z0-9_]{1,16}$")); // Code String
        VR_PATTERNS.put("DA", Pattern.compile("^\\d{8}$")); // Date
        VR_PATTERNS.put("DS", Pattern.compile("^[+-]?\\d*\\.?\\d*$")); // Decimal String
        VR_PATTERNS.put("DT", Pattern.compile("^\\d{14}(\\.\\d{1,6})?$")); // Date Time
        VR_PATTERNS.put("IS", Pattern.compile("^[+-]?\\d+$")); // Integer String
        VR_PATTERNS.put("LO", Pattern.compile("^[\\x20-\\x7E]{1,64}$")); // Long String
        VR_PATTERNS.put("LT", Pattern.compile("^[\\x20-\\x7E]{1,10240}$")); // Long Text
        VR_PATTERNS.put("PN", Pattern.compile("^[\\x20-\\x7E]{1,64}$")); // Person Name
        VR_PATTERNS.put("SH", Pattern.compile("^[\\x20-\\x7E]{1,16}$")); // Short String
        VR_PATTERNS.put("TM", Pattern.compile("^\\d{6}(\\.\\d{1,6})?$")); // Time
        VR_PATTERNS.put("UI", Pattern.compile("^[0-9\\.]{1,64}$")); // Unique Identifier
        VR_PATTERNS.put("US", Pattern.compile("^\\d{1,5}$")); // Unsigned Short
    }

    // 文件属性相关字段
    private String filePath;
    private long fileSize;
    private String format;
    private DicomImage image;

    public DicomTagUtil() {
        this.filePath = "";
        this.fileSize = 0;
        this.format = "";
        this.image = null;
    }

    /**
     * 标准化标签ID
     */
    public static String normalizeTagId(String tagId) {
        if (tagId == null) {
            return null;
        }
        return normalizedTagIdCache.computeIfAbsent(tagId, 
            key -> key.replace("(", "").replace(")", "").replace(" ", "").toUpperCase());
    }
    
    /**
     * 验证标签是否存在且值不为空
     */
    public static boolean validateTag(Map<String, DicomTag> tags, String tagId) {
        if (tagId == null || tagId.trim().isEmpty()) {
            return false;
        }
        DicomTag tag = tags.get(normalizeTagId(tagId));
        return tag != null && tag.getValue() != null && !tag.getValue().trim().isEmpty();
    }
    
    /**
     * 验证标签值是否符合预期格式
     */
    public static boolean validateTagFormat(Map<String, DicomTag> tags, String tagId, String regex) {
        if (!validateTag(tags, tagId)) {
            return false;
        }
        String value = tags.get(normalizeTagId(tagId)).getValue();
        return value.matches(regex);
    }
    
    /**
     * 获取标签统计信息
     */
    public static Map<String, Integer> getTagStatistics(Map<String, DicomTag> tags) {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        DicomTagRegistry registry = DicomTagRegistry.getInstance();
        
        tags.values().parallelStream().forEach(tag -> {
            String category = registry.getTagCategory(tag.getTagId());
            stats.merge(category, 1, Integer::sum);
        });
        
        return Collections.unmodifiableMap(stats);
    }
    
    /**
     * 搜索标签
     */
    public static List<DicomTag> searchTags(Map<String, DicomTag> tags, String pattern) {
        if (pattern == null || pattern.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        String searchPattern = pattern.toLowerCase();
        return tags.values().parallelStream()
            .filter(tag -> {
                String name = tag.getName().toLowerCase();
                String value = tag.getValue() != null ? tag.getValue().toLowerCase() : "";
                return name.contains(searchPattern) || value.contains(searchPattern);
            })
            .collect(Collectors.toList());
    }
    
    /**
     * 按类别获取标签
     */
    public static Map<String, List<DicomTag>> getTagsByCategory(Map<String, DicomTag> tags) {
        Map<String, List<DicomTag>> result = new ConcurrentHashMap<>();
        DicomTagRegistry registry = DicomTagRegistry.getInstance();
        
        tags.values().parallelStream().forEach(tag -> {
            String category = registry.getTagCategory(tag.getTagId());
            result.computeIfAbsent(category, k -> new ArrayList<>()).add(tag);
        });
        
        return result;
    }
    
    /**
     * 验证必需标签
     */
    public static boolean validateRequiredTags(Map<String, DicomTag> tags, String... requiredTagIds) {
        if (requiredTagIds == null || requiredTagIds.length == 0) {
            return true;
        }
        
        for (String tagId : requiredTagIds) {
            if (!validateTag(tags, tagId)) {
                LOG.warning("Missing required tag: " + tagId);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取标签值，如果不存在则返回默认值
     */
    public static String getTagValueOrDefault(Map<String, DicomTag> tags, String tagId, String defaultValue) {
        if (!validateTag(tags, tagId)) {
            return defaultValue;
        }
        return tags.get(normalizeTagId(tagId)).getValue();
    }
    
    /**
     * 获取标签原始值，如果不存在则返回null
     */
    public static byte[] getTagRawValue(Map<String, DicomTag> tags, String tagId) {
        if (tagId == null || tagId.trim().isEmpty()) {
            return null;
        }
        DicomTag tag = tags.get(normalizeTagId(tagId));
        return tag != null ? tag.getRawValue() : null;
    }
    
    /**
     * 批量添加标签
     */
    public static void addTags(Map<String, DicomTag> tags, Map<String, String> tagValues) {
        tagValues.forEach((tagId, value) -> {
            try {
                DicomTag tag = new DicomTag(tagId, value, VR.UN);
                tags.put(tagId, tag);
            } catch (DicomException e) {
                LOG.warning("Failed to add tag: " + tagId);
            }
        });
    }

    // ===== 文件属性方法 =====

    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
        LOG.fine("Set file path: " + filePath);
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
        LOG.fine("Set file size: " + fileSize);
    }
    
    public String getFormat() {
        return format;
    }
    
    public void setFormat(String format) {
        this.format = format;
        LOG.fine("Set format: " + format);
    }
    
    public DicomImage getImage() {
        return image;
    }
    
    public void setImage(DicomImage image) {
        this.image = image;
        LOG.fine("Set image");
    }

    // ===== 增强功能方法 =====

    /**
     * 获取标签的VR类型对应的正则表达式
     */
    public static Pattern getVRPattern(String vrType) {
        return VR_PATTERNS.get(vrType);
    }

    /**
     * 检查文件是否为有效的DICOM文件
     */
    public boolean isValidDicomFile() {
        return fileSize > 0 && 
               filePath != null && !filePath.isEmpty() && 
               format != null && format.toUpperCase().contains("DICOM") &&
               image != null;
    }

    /**
     * 获取文件的基本信息描述
     */
    public String getFileDescription() {
        return String.format("DICOM File: %s (Size: %d bytes, Format: %s)", 
                           filePath, fileSize, format);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DicomTagUtil that = (DicomTagUtil) obj;
        return fileSize == that.fileSize &&
               Objects.equals(filePath, that.filePath) &&
               Objects.equals(format, that.format) &&
               Objects.equals(image, that.image);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(filePath, fileSize, format, image);
    }
    
    @Override
    public String toString() {
        return "DicomTagUtil{" +
               "filePath='" + filePath + '\'' +
               ", fileSize=" + fileSize +
               ", format='" + format + '\'' +
               ", hasImage=" + (image != null) +
               '}';
    }
} 