package com.ge.med.ct.dicom.io;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

/**
 * 文件系统操作工具类
 * 提供通用的文件系统操作功能
 */
public class FileSystemUtil {
    private static final Logger LOG = LoggerFactory.getLogger(FileSystemUtil.class);
    
    // 缓存实例
    private static final Cache<String, Long> fileSizeCache = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, java.util.concurrent.TimeUnit.HOURS)
        .build();
        
    private static final Cache<String, byte[]> fileContentCache = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, java.util.concurrent.TimeUnit.HOURS)
        .build();
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public static boolean fileExists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }
        return new File(filePath).exists();
    }
    
    /**
     * 确保目录存在，如果不存在则创建
     * 
     * @param directory 目录路径
     * @return 创建是否成功
     */
    public static boolean ensureDirectoryExists(String directory) {
        if (directory == null || directory.trim().isEmpty()) {
            return false;
        }
        
        try {
            Path dirPath = Paths.get(directory);
            if (!dirPath.toFile().exists()) {
                return dirPath.toFile().mkdirs();
            }
            return true;
        } catch (Exception e) {
            LOG.error("创建目录失败: {}", directory, e);
            return false;
        }
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public static long getFileSize(String filePath) {
        if (!fileExists(filePath)) {
            return -1;
        }
        
        return fileSizeCache.get(filePath, key -> {
            try {
                return Files.size(Paths.get(key));
            } catch(IOException e) {
                LOG.error("获取文件大小失败: {}", key, e);
                return -1L;
            }
        });
    }
    
    /**
     * 获取文件大小的格式化字符串
     * 
     * @param filePath 文件路径
     * @return 格式化的文件大小字符串
     */
    public static String getFormattedFileSize(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path) || Files.isDirectory(path)) {
                return "0 B";
            }
            
            long size = Files.size(path);
            return formatFileSize(size);
        } catch (Exception e) {
            LOG.warn("获取文件大小时出错: {}", filePath, e);
            return "未知";
        }
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 删除是否成功
     */
    public static boolean deleteFile(String filePath) {
        if (!fileExists(filePath)) {
            return false;
        }
        
        boolean result = new File(filePath).delete();
        if (result) {
            // 清除缓存
            fileSizeCache.invalidate(filePath);
            fileContentCache.invalidate(filePath);
        }
        return result;
    }
    
    /**
     * 格式化文件大小为易读格式
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 将反斜杠替换为正斜杠
     * 
     * @param path 路径字符串
     * @return 标准化后的路径
     */
    public static String normalizePath(String path) {
        if (path == null) {
            return null;
        }
        return path.replace('\\', '/');
    }
    
    /**
     * 清除所有缓存
     */
    public static void clearCache() {
        fileSizeCache.invalidateAll();
        fileContentCache.invalidateAll();
    }
    
    /**
     * 检查文件是否有效
     * 验证文件路径、存在性、可读性和最小大小要求
     * 
     * @param filePath 文件路径
     * @param minSize 最小文件大小要求（字节）
     * @return 文件是否有效
     */
    public static boolean isFileValid(String filePath, long minSize) {
        // 1. 路径验证
        if (filePath == null || filePath.trim().isEmpty()) {
            LOG.warn("文件路径为空");
            return false;
        }
        
        // 2. 文件存在性检查
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            LOG.warn("文件不存在: {}", filePath);
            return false;
        }
        
        // 3. 文件可读性检查
        if (!Files.isReadable(path)) {
            LOG.warn("文件不可读: {}", filePath);
            return false;
        }
        
        // 4. 文件大小检查
        try {
            if (Files.size(path) < minSize) {
                LOG.warn("文件太小，不是有效的文件: {}", filePath);
                return false;
            }
            return true;
        } catch (IOException e) {
            LOG.warn("获取文件大小失败: {}, 错误: {}", filePath, e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取目录大小
     * @param directoryPath 目录路径
     * @param recursive 是否递归计算子目录
     * @return 目录大小（字节）
     */
    public static long getDirectorySize(String directoryPath, boolean recursive) {
        if (!isFileValid(directoryPath, 0)) {
            LOG.warn("Invalid directory: {}", directoryPath);
            return 0;
        }
        
        try {
            Stream<Path> pathStream = recursive ? 
                Files.walk(Paths.get(directoryPath)) : 
                Files.list(Paths.get(directoryPath));
            
            long size = pathStream.filter(Files::isRegularFile)
                                .mapToLong(p -> getFileSize(p.toString()))
                                .filter(s -> s > 0)
                                .sum();
            
            pathStream.close();
            return size;
        } catch (IOException e) {
            LOG.error("Error calculating directory size: {}", directoryPath, e);
            return 0;
        }
    }
    
    /**
     * 获取目录中的所有文件
     */
    public static List<String> listFiles(String directoryPath, boolean recursive) {
        List<String> files = new ArrayList<>();
        if (!isFileValid(directoryPath, 0)) {
            return files;
        }
        
        try {
            Stream<Path> pathStream = recursive ? 
                Files.walk(Paths.get(directoryPath)) : 
                Files.list(Paths.get(directoryPath));
            
            files = pathStream.filter(Files::isRegularFile)
                            .map(Path::toString)
                            .collect(Collectors.toList());
            
            pathStream.close();
        } catch (IOException e) {
            LOG.error("Error listing files: {}", directoryPath, e);
        }
        
        return files;
    }
    
    /**
     * 移动文件
     */
    public static boolean moveFile(String sourcePath, String targetPath) {
        if (!fileExists(sourcePath)) {
            return false;
        }
        
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            // 确保目标目录存在
            ensureDirectoryExists(target.getParent().toString());
            
            Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
            
            // 更新缓存
            fileSizeCache.invalidate(sourcePath);
            fileContentCache.invalidate(sourcePath);
            
            return true;
        } catch (IOException e) {
            LOG.error("移动文件失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }
    
    /**
     * 复制文件
     */
    public static boolean copyFile(String sourcePath, String targetPath) {
        if (!fileExists(sourcePath)) {
            return false;
        }
        
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            // 确保目标目录存在
            ensureDirectoryExists(target.getParent().toString());
            
            Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
            return true;
        } catch (IOException e) {
            LOG.error("复制文件失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }
} 