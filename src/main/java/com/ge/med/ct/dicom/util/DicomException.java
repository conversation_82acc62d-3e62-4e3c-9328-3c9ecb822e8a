package com.ge.med.ct.dicom.util;

/**
 * DICOM异常类
 * 用于统一处理DICOM相关的异常
 */
public class DicomException extends Exception {
    private static final long serialVersionUID = 1L;
    
    private final String tagId;
    private final String operation;
    private final String context;
    
    public DicomException(String message) {
        this(message, null, null, null, null);
    }
    
    public DicomException(String message, String tagId, String operation) {
        this(message, tagId, operation, null, null);
    }
    
    public DicomException(String message, String tagId, String operation, String context) {
        this(message, tagId, operation, context, null);
    }
    
    public DicomException(String message, Throwable cause) {
        this(message, null, null, null, cause);
    }
    
    public DicomException(String message, String tagId, String operation, String context, Throwable cause) {
        super(message, cause);
        this.tagId = tagId;
        this.operation = operation;
        this.context = context;
    }
    
    public String getTagId() {
        return tagId;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public String getContext() {
        return context;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getName());
        sb.append(": ").append(getMessage());
        if (tagId != null) {
            sb.append(" [Tag: ").append(tagId).append("]");
        }
        if (operation != null) {
            sb.append(" [Operation: ").append(operation).append("]");
        }
        if (context != null) {
            sb.append(" [Context: ").append(context).append("]");
        }
        if (getCause() != null) {
            sb.append(" [Cause: ").append(getCause().getMessage()).append("]");
        }
        return sb.toString();
    }
} 