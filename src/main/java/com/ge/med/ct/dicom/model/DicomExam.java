package com.ge.med.ct.dicom.model;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.logging.Logger;

import org.dcm4che3.data.VR;

import com.ge.med.ct.dicom.util.DicomException;
import com.ge.med.ct.dicom.tag.DicomTag;

/**
 * DICOM检查类
 * 管理DICOM检查属性和序列
 */
public class DicomExam {
    private static final Logger LOG = Logger.getLogger(DicomExam.class.getName());
    
    private String id;
    private Map<String, DicomTag> tags;
    private List<DicomSeries> series;
    
    public DicomExam(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException("ID cannot be null or empty");
        }
        this.id = id;
        this.tags = new HashMap<>();
        this.series = new ArrayList<>();
    }
    
    public String getId() {
        return id;
    }
    
    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }
    
    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            LOG.fine("Added tag " + tagId + " to exam " + id);
        }
    }
    
    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValue() : null;
    }
    
    public String getStudyInstanceUID() {
        return getTagValue(DicomTag.StudyInstanceUID.getTagId());
    }
    
    public void setStudyInstanceUID(String uid) throws DicomException {
        setTagValue(DicomTag.StudyInstanceUID.getTagId(), uid);
    }
    
    public String getPatientID() {
        return getTagValue(DicomTag.PatientID.getTagId());
    }
    
    public void setPatientID(String patientId) throws DicomException {
        setTagValue(DicomTag.PatientID.getTagId(), patientId);
    }
    
    public String getPatientName() {
        return getTagValue(DicomTag.PatientName.getTagId());
    }
    
    public void setPatientName(String name) throws DicomException {
        setTagValue(DicomTag.PatientName.getTagId(), name);
    }
    
    public String getStudyDate() {
        return getTagValue(DicomTag.StudyDate.getTagId());
    }
    
    public void setStudyDate(String date) throws DicomException {
        setTagValue(DicomTag.StudyDate.getTagId(), date);
    }
    
    public String getStudyTime() {
        return getTagValue(DicomTag.StudyTime.getTagId());
    }
    
    public void setStudyTime(String time) throws DicomException {
        setTagValue(DicomTag.StudyTime.getTagId(), time);
    }
    
    private void setTagValue(String tagId, String value) throws DicomException {
        DicomTag tag = tags.get(tagId);
        if (tag == null) {
            tag = DicomTag.createNewTag(tagId, "Tag-" + tagId, value, VR.UN, "Dynamic tag " + tagId, false);
            if (tag == null) {
                throw new DicomException("Failed to create tag: " + tagId);
            }
            tags.put(tagId, tag);
        } else {
            tag.setValue(value);
        }
        LOG.fine("Set tag " + tagId + " value to " + value + " for exam " + id);
    }
    
    public List<DicomSeries> getSeries() {
        return Collections.unmodifiableList(series);
    }
    
    public void setSeries(List<DicomSeries> series) {
        if (series == null) {
            throw new IllegalArgumentException("Series list cannot be null");
        }
        
        // Remove all existing series
        for (DicomSeries s : new ArrayList<>(this.series)) {
            removeSeries(s);
        }
        
        // Add all new series
        for (DicomSeries s : series) {
            addSeries(s);
        }
    }
    
    public void addSeries(DicomSeries series) {
        if (series != null && !this.series.contains(series)) {
            this.series.add(series);
            if (series.getExam() != this) {
                series.setExam(this);
            }
            LOG.fine("Added series " + series.getId() + " to exam " + id);
        }
    }
    
    public void removeSeries(DicomSeries series) {
        if (series != null && this.series.contains(series)) {
            this.series.remove(series);
            if (series.getExam() == this) {
                series.setExam(null);
            }
            LOG.fine("Removed series " + series.getId() + " from exam " + id);
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s[id=%s, studyInstanceUID=%s, patientID=%s, patientName=%s, studyDate=%s, studyTime=%s, seriesCount=%d]",
            getClass().getSimpleName(), id, getStudyInstanceUID(), getPatientID(), getPatientName(),
            getStudyDate(), getStudyTime(), series.size());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DicomExam other = (DicomExam) obj;
        return id != null && id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}