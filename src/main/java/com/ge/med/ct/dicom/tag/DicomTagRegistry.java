package com.ge.med.ct.dicom.tag;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import org.dcm4che3.data.VR;
import java.util.logging.Logger;

/**
 * DICOM标签注册表
 * 提供标签的注册、查找和管理功能
 */
public class DicomTagRegistry {
    private static final Logger LOG = Logger.getLogger(DicomTagRegistry.class.getName());
    private static DicomTagRegistry instance;
    
    private final Map<String, DicomTag> tagMap = new ConcurrentHashMap<>();
    private final Map<String, List<DicomTag>> categoryMap = new ConcurrentHashMap<>();
    
    private DicomTagRegistry() {
        initializeTags();
    }
    
    /**
     * 获取注册表实例
     */
    public static synchronized DicomTagRegistry getInstance() {
        if (instance == null) {
            instance = new DicomTagRegistry();
        }
        return instance;
    }
    
    /**
     * 初始化标签
     */
    private void initializeTags() {
        // 注册所有预定义的标签
        registerTag(DicomTag.PatientID);
        registerTag(DicomTag.PatientName);
        registerTag(DicomTag.StudyInstanceUID);
        registerTag(DicomTag.SeriesInstanceUID);
        registerTag(DicomTag.SOPInstanceUID);
        registerTag(DicomTag.Modality);
        registerTag(DicomTag.StudyDate);
        registerTag(DicomTag.StudyTime);
        registerTag(DicomTag.SeriesNumber);
        registerTag(DicomTag.InstanceNumber);
        registerTag(DicomTag.ImagePosition);
        registerTag(DicomTag.ImageOrientation);
        registerTag(DicomTag.Rows);
        registerTag(DicomTag.Columns);
        registerTag(DicomTag.PixelData);
        registerTag(DicomTag.BitsAllocated);
        registerTag(DicomTag.BitsStored);
        registerTag(DicomTag.HighBit);
        registerTag(DicomTag.PixelRepresentation);
        registerTag(DicomTag.SamplesPerPixel);
        registerTag(DicomTag.PlanarConfiguration);
        registerTag(DicomTag.WindowCenter);
        registerTag(DicomTag.WindowWidth);
        registerTag(DicomTag.RescaleIntercept);
        registerTag(DicomTag.RescaleSlope);
        registerTag(DicomTag.RescaleType);
        registerTag(DicomTag.ImageType);
        registerTag(DicomTag.ImageOrientationPatient);
        registerTag(DicomTag.ImagePositionPatient);
        registerTag(DicomTag.SliceLocation);
        registerTag(DicomTag.ImageIndex);
        
        // 注册Study相关标签
        registerTag(DicomTag.StudyID);
        registerTag(DicomTag.StudyDescription);
        registerTag(DicomTag.AccessionNumber);
        registerTag(DicomTag.ReferringPhysicianName);
        registerTag(DicomTag.StudyComments);
        registerTag(DicomTag.StudyCompletionDate);
        registerTag(DicomTag.StudyCompletionTime);
        registerTag(DicomTag.StudyVerifiedDate);
        registerTag(DicomTag.StudyVerifiedTime);
        registerTag(DicomTag.StudyStatus);
        registerTag(DicomTag.StudyPriority);
        registerTag(DicomTag.StudyReadDate);
        registerTag(DicomTag.StudyReadTime);
        registerTag(DicomTag.StudyType);
        registerTag(DicomTag.StudyReason);
        
        // 注册Series相关标签
        registerTag(DicomTag.SeriesDescription);
        registerTag(DicomTag.BodyPartExamined);
        registerTag(DicomTag.SeriesUID);
        registerTag(DicomTag.SeriesType);
        registerTag(DicomTag.SeriesDate);
        registerTag(DicomTag.SeriesTime);
        registerTag(DicomTag.SeriesStatus);
        registerTag(DicomTag.SeriesPriority);
        registerTag(DicomTag.SeriesCompletionDate);
        registerTag(DicomTag.SeriesCompletionTime);
        registerTag(DicomTag.SeriesVerifiedDate);
        registerTag(DicomTag.SeriesVerifiedTime);
        registerTag(DicomTag.SeriesReadDate);
        registerTag(DicomTag.SeriesReadTime);
        
        // 注册Image相关标签
        registerTag(DicomTag.ImageComments);
        registerTag(DicomTag.ImageDate);
        registerTag(DicomTag.ImageTime);
        registerTag(DicomTag.SliceThickness);
        registerTag(DicomTag.PhotometricInterpretation);
        
        // 注册Patient相关标签
        registerTag(DicomTag.PatientBirthDate);
        registerTag(DicomTag.PatientSex);
        registerTag(DicomTag.PatientAge);
        registerTag(DicomTag.PatientSize);
        registerTag(DicomTag.PatientWeight);
        registerTag(DicomTag.PatientComments);
        registerTag(DicomTag.PatientState);
        registerTag(DicomTag.PregnancyStatus);
        registerTag(DicomTag.MedicalAlerts);
        registerTag(DicomTag.Allergies);
        registerTag(DicomTag.SpecialNeeds);
        
        // 注册Equipment相关标签
        registerTag(DicomTag.InstitutionAddress);
        registerTag(DicomTag.InstitutionDepartmentName);
        registerTag(DicomTag.DateOfLastCalibration);
        registerTag(DicomTag.TimeOfLastCalibration);
        
        // 注册Protocol相关标签
        registerTag(DicomTag.ProtocolContextSequence);
        
        // 注册Acquisition相关标签
        registerTag(DicomTag.AcquisitionNumber);
        registerTag(DicomTag.AcquisitionDeviceProcessingDescription);
        registerTag(DicomTag.AcquisitionDeviceProcessingCode);
        registerTag(DicomTag.AcquisitionStartCondition);
        registerTag(DicomTag.AcquisitionStartConditionData);
        registerTag(DicomTag.AcquisitionTerminationCondition);
        registerTag(DicomTag.AcquisitionTerminationConditionData);
        registerTag(DicomTag.AcquisitionContextSequence);
        registerTag(DicomTag.AcquisitionProtocolElementSequence);
        registerTag(DicomTag.AcquisitionDuration);
        
        // 注册Content相关标签
        registerTag(DicomTag.ContentDate);
        registerTag(DicomTag.ContentTime);
    }
    
    /**
     * 注册新标签
     */
    public void registerTag(DicomTag tag) {
        if (tag == null) {
            return;
        }
        tagMap.put(tag.getTagId(), tag);
        
        // 根据标签组号确定类别
        String category = getCategoryForGroup(tag.getGroup());
        categoryMap.computeIfAbsent(category, k -> new ArrayList<>()).add(tag);
    }
    
    /**
     * 通过标签ID查找标签
     */
    public DicomTag getTag(String tagId) {
        return tagMap.get(normalizeTagId(tagId));
    }
    
    /**
     * 通过组号和元素号查找标签
     */
    public DicomTag getTag(int group, int element) {
        String tagId = String.format("%04X%04X", group, element);
        return getTag(tagId);
    }
    
    /**
     * 获取指定类别的所有标签
     */
    public List<DicomTag> getTagsByCategory(String category) {
        return categoryMap.getOrDefault(category, Collections.emptyList());
    }
    
    /**
     * 获取所有已注册的标签
     */
    public Collection<DicomTag> getAllTags() {
        return Collections.unmodifiableCollection(tagMap.values());
    }
    
    /**
     * 通过名称搜索标签
     */
    public List<DicomTag> searchTagsByName(String namePattern) {
        if (namePattern == null || namePattern.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        String pattern = namePattern.toLowerCase();
        return tagMap.values().stream()
                .filter(tag -> tag.getName().toLowerCase().contains(pattern))
                .collect(Collectors.toList());
    }
    
    /**
     * 检查标签是否存在
     */
    public boolean hasTag(String tagId) {
        return tagMap.containsKey(normalizeTagId(tagId));
    }
    
    /**
     * 获取标签所属的类别
     */
    public String getTagCategory(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? getCategoryForGroup(tag.getGroup()) : null;
    }
    
    /**
     * 根据组号确定标签类别
     */
    private String getCategoryForGroup(int group) {
        switch (group) {
            case 0x0008:
                return "General";
            case 0x0010:
                return "Patient";
            case 0x0018:
                return "Acquisition";
            case 0x0020:
                return "Image";
            case 0x0028:
                return "Image Presentation";
            case 0x0032:
                return "Study";
            case 0x0038:
                return "Visit";
            case 0x0040:
                return "Procedure";
            case 0x0050:
                return "Device";
            case 0x0054:
                return "Nuclear Medicine";
            case 0x7FE0:
                return "Pixel Data";
            default:
                return "Other";
        }
    }
    
    /**
     * 规范化标签ID
     */
    private String normalizeTagId(String tagId) {
        if (tagId == null) {
            return null;
        }
        return tagId.replace("(", "").replace(")", "").replace(" ", "").toUpperCase();
    }
    
    /**
     * 清除所有注册的标签
     */
    public void clear() {
        tagMap.clear();
        categoryMap.clear();
    }
    
    /**
     * 获取指定VR的所有标签
     */
    public List<DicomTag> getTagsByVR(VR vr) {
        return tagMap.values().stream()
                .filter(tag -> tag.getVr() == vr)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定VM的所有标签
     */
    public List<DicomTag> getTagsByVM(String vm) {
        return tagMap.values().stream()
                .filter(tag -> tag.getVM().equals(vm))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有私有标签
     */
    public List<DicomTag> getTagsByPrivate(boolean isPrivate) {
        return tagMap.values().stream()
                .filter(tag -> tag.isPrivate() == isPrivate)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证标签
     */
    public boolean validateTag(String tagId, String value) {
        DicomTag tag = getTag(tagId);
        if (tag == null) {
            LOG.warning("标签不存在: " + tagId);
            return false;
        }
        
        return tag.validate(value);
    }
} 