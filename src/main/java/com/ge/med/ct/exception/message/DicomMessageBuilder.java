package com.ge.med.ct.exception.message;

import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.factory.ExceptionFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * DICOM消息构建器
 * 用于构建DICOM消息
 */
public class DicomMessageBuilder {
    private final Message message;
    private final List<Object> params = new ArrayList<>();

    /**
     * 私有构造函数
     *
     * @param message 消息
     */
    private DicomMessageBuilder(Message message) {
        this.message = Objects.requireNonNull(message, "Message cannot be null");
    }

    /**
     * 创建DICOM消息构建器
     *
     * @param message 消息
     * @return DICOM消息构建器
     * @throws QAToolException 如果消息为null
     */
    public static DicomMessageBuilder of(Message message) {
        if (message == null) {
            throw ExceptionFactory.createException("Message cannot be null");
        }
        return new DicomMessageBuilder(message);
    }

    /**
     * 添加参数
     *
     * @param param 参数
     * @return DICOM消息构建器
     */
    public DicomMessageBuilder param(Object param) {
        params.add(param);
        return this;
    }

    /**
     * 获取消息
     *
     * @return 消息
     */
    public Message getMessage() {
        return message;
    }

    /**
     * 获取参数列表
     *
     * @return 参数列表
     */
    public List<Object> getParams() {
        return Collections.unmodifiableList(params);
    }

    /**
     * 构建消息
     *
     * @return 格式化后的消息字符串
     */
    public String build() {
        return message.format(params.toArray()).toStr();
    }
}
