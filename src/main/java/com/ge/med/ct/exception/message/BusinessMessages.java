package com.ge.med.ct.exception.message;

/**
 * 业务消息枚举
 * 定义业务相关的消息
 */
public enum BusinessMessages implements Message {
    // 基础错误
    UNKNOWN_ERROR("business.unknown", "未知业务错误"),
    PROCESSING_ERROR("business.processing.error", "业务处理错误: {0}"),

    // 数据相关错误
    DATA_NOT_FOUND("business.data.not.found", "未找到请求的数据: {0}"),
    DATA_INVALID("business.data.invalid", "无效的数据: {0}"),
    DATA_DUPLICATE("business.data.duplicate", "数据重复: {0}"),

    // 操作相关错误
    OPERATION_NOT_ALLOWED("business.operation.not.allowed", "不允许的操作: {0}"),
    OPERATION_FAILED("business.operation.failed", "操作失败: {0}"),

    // 状态相关错误
    STATE_INVALID("business.state.invalid", "无效的状态: {0}"),
    STATE_TRANSITION_INVALID("business.state.transition.invalid", "无效的状态转换: {0} -> {1}"),

    // 验证相关错误
    VALIDATION_ERROR("business.validation.error", "验证错误: {0}"),
    VALIDATION_FAILED("business.validation.failed", "验证失败: {0}"),

    // 配置相关错误
    CONFIG_ERROR("business.config.error", "配置错误: {0}"),
    CONFIG_MISSING("business.config.missing", "缺少配置: {0}"),
    CONFIG_INVALID("business.config.invalid", "无效的配置: {0}"),

    // 资源相关错误
    RESOURCE_NOT_FOUND("business.resource.not.found", "未找到资源: {0}"),
    RESOURCE_UNAVAILABLE("business.resource.unavailable", "资源不可用: {0}"),

    // 权限相关错误
    PERMISSION_DENIED("business.permission.denied", "权限不足: {0}"),
    AUTHENTICATION_FAILED("business.authentication.failed", "认证失败: {0}");

    private final AbstractMessage delegate;

    BusinessMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage) {};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}
