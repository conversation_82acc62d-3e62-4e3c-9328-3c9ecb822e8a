package com.ge.med.ct.exception.factory;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.*;
import com.ge.med.ct.exception.message.*;

/**
 * 异常工厂类
 * 用于创建各种类型的异常
 */
public final class ExceptionFactory {

    private ExceptionFactory() {
        throw new AssertionError("Utility class should not be instantiated");
    }

    public static QAToolException createException(String message) {
        return QAToolException.builder()
                .message(CommonMessages.UNKNOWN_ERROR)
                .messageArgs(message)
                .build();
    }

    public static QAToolException createException(String message, Throwable cause) {
        return QAToolException.builder()
                .message(CommonMessages.UNKNOWN_ERROR)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static QAToolException createException(ErrorCode errorCode, Message message, Object... args) {
        return QAToolException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .build();
    }

    public static QAToolException createException(ErrorCode errorCode, Message message, Throwable cause,
            Object... args) {
        return QAToolException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .cause(cause)
                .build();
    }

    public static DicomException createDicomException(String message) {
        return (DicomException) DicomException.builder()
                .message(DicomMessages.FILE_INVALID)
                .messageArgs(message)
                .build();
    }

    public static DicomException createDicomException(String message, Throwable cause) {
        return (DicomException) DicomException.builder()
                .message(DicomMessages.FILE_INVALID)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static DicomException createDicomException(ErrorCode errorCode, String message) {
        return (DicomException) DicomException.builder()
                .errorCode(errorCode)
                .message(DicomMessages.FILE_INVALID)
                .messageArgs(message)
                .build();
    }

    public static DicomException createDicomException(ErrorCode errorCode, String message, Throwable cause) {
        return (DicomException) DicomException.builder()
                .errorCode(errorCode)
                .message(DicomMessages.FILE_INVALID)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static DicomException createDicomException(ErrorCode errorCode, Message message, Object... args) {
        return (DicomException) DicomException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .build();
    }

    public static DicomException createDicomException(ErrorCode errorCode, Message message, Throwable cause,
            Object... args) {
        return (DicomException) DicomException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .cause(cause)
                .build();
    }

    public static DicomException createDicomDataException(String message) {
        return (DicomException) DicomException.dataBuilder()
                .messageArgs(message)
                .build();
    }

    public static DicomException createDicomDataException(String message, Throwable cause) {
        return (DicomException) DicomException.dataBuilder()
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static DicomException createDicomDataException(ErrorCode errorCode, String message) {
        return (DicomException) DicomException.dataBuilder()
                .errorCode(errorCode)
                .messageArgs(message)
                .build();
    }

    public static DicomException createDicomDataException(ErrorCode errorCode, String message, Throwable cause) {
        return (DicomException) DicomException.dataBuilder()
                .errorCode(errorCode)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static BusinessException createBusinessException(String message) {
        return (BusinessException) BusinessException.builder()
                .message(BusinessMessages.PROCESSING_ERROR)
                .messageArgs(message)
                .build();
    }

    public static BusinessException createBusinessException(String message, Throwable cause) {
        return (BusinessException) BusinessException.builder()
                .message(BusinessMessages.PROCESSING_ERROR)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static BusinessException createBusinessException(ErrorCode errorCode, String message) {
        return (BusinessException) BusinessException.builder()
                .errorCode(errorCode)
                .message(BusinessMessages.PROCESSING_ERROR)
                .messageArgs(message)
                .build();
    }

    public static BusinessException createBusinessException(ErrorCode errorCode, String message, Throwable cause) {
        return (BusinessException) BusinessException.builder()
                .errorCode(errorCode)
                .message(BusinessMessages.PROCESSING_ERROR)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static BusinessException createBusinessException(ErrorCode errorCode, Message message, Object... args) {
        return (BusinessException) BusinessException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .build();
    }

    public static BusinessException createBusinessException(ErrorCode errorCode, Message message, Throwable cause,
            Object... args) {
        return (BusinessException) BusinessException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .cause(cause)
                .build();
    }

    public static UIException createUIException(String message) {
        return (UIException) UIException.builder()
                .message(UIMessages.OPERATION_ERROR)
                .messageArgs(message)
                .build();
    }

    public static UIException createUIException(String message, Throwable cause) {
        return (UIException) UIException.builder()
                .message(UIMessages.OPERATION_ERROR)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static UIException createUIException(ErrorCode errorCode, String message) {
        return (UIException) UIException.builder()
                .errorCode(errorCode)
                .message(UIMessages.OPERATION_ERROR)
                .messageArgs(message)
                .build();
    }

    public static UIException createUIException(ErrorCode errorCode, String message, Throwable cause) {
        return (UIException) UIException.builder()
                .errorCode(errorCode)
                .message(UIMessages.OPERATION_ERROR)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static UIException createUIException(ErrorCode errorCode, Message message, Object... args) {
        return (UIException) UIException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .build();
    }

    public static UIException createUIException(ErrorCode errorCode, Message message, Throwable cause,
            Object... args) {
        return (UIException) UIException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .cause(cause)
                .build();
    }

    public static ConfigValidationException createConfigValidationException(String message) {
        return (ConfigValidationException) ConfigValidationException.builder()
                .message(ConfigMessages.VALIDATION_FAILED)
                .messageArgs(message)
                .build();
    }

    public static ConfigValidationException createConfigValidationException(String message, Throwable cause) {
        return (ConfigValidationException) ConfigValidationException.builder()
                .message(ConfigMessages.VALIDATION_FAILED)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static ConfigValidationException createConfigValidationException(ErrorCode errorCode, String message) {
        return (ConfigValidationException) ConfigValidationException.builder()
                .errorCode(errorCode)
                .message(ConfigMessages.VALIDATION_FAILED)
                .messageArgs(message)
                .build();
    }

    public static ConfigValidationException createConfigValidationException(ErrorCode errorCode, String message,
            Throwable cause) {
        return (ConfigValidationException) ConfigValidationException.builder()
                .errorCode(errorCode)
                .message(ConfigMessages.VALIDATION_FAILED)
                .messageArgs(message)
                .cause(cause)
                .build();
    }

    public static ConfigValidationException createConfigValidationException(ErrorCode errorCode, Message message,
            Object... args) {
        return (ConfigValidationException) ConfigValidationException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .build();
    }

    public static ConfigValidationException createConfigValidationException(ErrorCode errorCode, Message message,
            Throwable cause, Object... args) {
        return (ConfigValidationException) ConfigValidationException.builder()
                .errorCode(errorCode)
                .message(message)
                .messageArgs(args)
                .cause(cause)
                .build();
    }
}
