package com.ge.med.ct.exception.core;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.exception.message.Message;

import java.util.Map;

/**
 * 业务异常类
 * 用于表示业务逻辑处理过程中的异常
 */
public class BusinessException extends QAToolException {
    private static final long serialVersionUID = 1L;

    /**
     * 创建异常构建器
     *
     * @return 异常构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建业务异常
     *
     * @param message 异常消息
     */
    public BusinessException(Message message) {
        super(ErrorCode.PROCESSING, message);
    }

    /**
     * 创建带原因的业务异常
     *
     * @param message 异常消息
     * @param cause   原因
     */
    public BusinessException(Message message, Throwable cause) {
        super(ErrorCode.PROCESSING, message, cause);
    }

    /**
     * 创建带错误码的业务异常
     *
     * @param errorCode 错误码
     * @param message   异常消息
     */
    public BusinessException(ErrorCode errorCode, Message message) {
        super(errorCode, message);
    }

    /**
     * 创建带错误码和原因的业务异常
     *
     * @param errorCode 错误码
     * @param message   异常消息
     * @param cause     原因
     */
    public BusinessException(ErrorCode errorCode, Message message, Throwable cause) {
        super(errorCode, message, cause);
    }

    /**
     * 创建带消息的业务异常
     *
     * @param message 消息
     * @param args    消息参数
     */
    public BusinessException(DicomMessages message, Object... args) {
        super(ErrorCode.PROCESSING, message, args);
    }

    /**
     * 创建带消息和原因的业务异常
     *
     * @param message 消息
     * @param cause   原因
     * @param args    消息参数
     */
    public BusinessException(DicomMessages message, Throwable cause, Object... args) {
        super(ErrorCode.PROCESSING, message, cause, args);
    }

    /**
     * 创建带错误码和消息的业务异常
     *
     * @param errorCode 错误码
     * @param message   消息
     * @param args      消息参数
     */
    public BusinessException(ErrorCode errorCode, DicomMessages message, Object... args) {
        super(errorCode, message, args);
    }

    /**
     * 创建带错误码、消息和原因的业务异常
     *
     * @param errorCode 错误码
     * @param message   消息
     * @param cause     原因
     * @param args      消息参数
     */
    public BusinessException(ErrorCode errorCode, Message message, Throwable cause, Object... args) {
        super(errorCode, message, cause, args);
    }

    /**
     * 业务异常构建器
     */
    public static class Builder extends QAToolException.Builder {
        /**
         * 创建业务异常构建器
         */
        public Builder() {
            super();
            errorCode(ErrorCode.PROCESSING);
            message(DicomMessages.PROCESSING_ERROR);
        }

        /**
         * 设置错误码
         *
         * @param errorCode 错误码
         * @return 构建器
         */
        public Builder errorCode(ErrorCode errorCode) {
            super.errorCode(errorCode);
            return this;
        }

        /**
         * 构建业务异常
         *
         * @return 业务异常实例
         */
        @Override
        public BusinessException build() {
            // 直接创建BusinessException对象，而不是先创建QAToolException对象
            ErrorCode errorCode = this.errorCode;
            Message message = this.message;
            Throwable cause = this.cause;
            Object[] messageArgs = this.messageArgs;

            BusinessException businessException = new BusinessException(
                    errorCode,
                    message,
                    cause,
                    messageArgs);

            // 添加上下文
            for (Map.Entry<String, Object> entry : this.context.entrySet()) {
                businessException.addContext(entry.getKey(), entry.getValue());
            }

            return businessException;
        }
    }
}
