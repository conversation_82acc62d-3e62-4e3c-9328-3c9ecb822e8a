package com.ge.med.ct.exception.util;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Lambda表达式异常处理工具类
 * 提供处理Lambda表达式中受检异常的辅助方法
 */
public final class LambdaExceptionUtil {

    private LambdaExceptionUtil() {
        throw new AssertionError("Utility class should not be instantiated");
    }

    /**
     * 包装可能抛出受检异常的Consumer
     */
    public static <T> Consumer<T> uncheckedConsumer(CheckedConsumer<T> consumer) {
        return t -> {
            try {
                consumer.accept(t);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }

    /**
     * 包装可能抛出受检异常的Function
     */
    public static <T, R> Function<T, R> uncheckedFunction(CheckedFunction<T, R> function) {
        return t -> {
            try {
                return function.apply(t);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }

    /**
     * 包装可能抛出受检异常的Supplier
     */
    public static <T> Supplier<T> uncheckedSupplier(CheckedSupplier<T> supplier) {
        return () -> {
            try {
                return supplier.get();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }

    /**
     * 包装可能抛出受检异常的Runnable
     */
    public static Runnable uncheckedRunnable(CheckedRunnable runnable) {
        return () -> {
            try {
                runnable.run();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }

    /**
     * 可抛出受检异常的Consumer接口
     */
    @FunctionalInterface
    public interface CheckedConsumer<T> {
        void accept(T t) throws Exception;
    }

    /**
     * 可抛出受检异常的Function接口
     */
    @FunctionalInterface
    public interface CheckedFunction<T, R> {
        R apply(T t) throws Exception;
    }

    /**
     * 可抛出受检异常的Supplier接口
     */
    @FunctionalInterface
    public interface CheckedSupplier<T> {
        T get() throws Exception;
    }

    /**
     * 可抛出受检异常的Runnable接口
     */
    @FunctionalInterface
    public interface CheckedRunnable {
        void run() throws Exception;
    }
}
