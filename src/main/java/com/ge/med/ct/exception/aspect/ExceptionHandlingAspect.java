package com.ge.med.ct.exception.aspect;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.event.ExceptionEvent;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.*;
import com.ge.med.ct.exception.util.ExceptionStatistics;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 异常处理切面
 */
@Aspect
public class ExceptionHandlingAspect {
    private static final Logger LOGGER = Logger.getLogger(ExceptionHandlingAspect.class.getName());

    /**
     * 处理标记了HandleException注解的方法
     */
    @Around("@annotation(com.ge.med.ct.exception.aspect.HandleException)")
    public Object handleException(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Throwable ex) {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();

            // 获取方法上的注解
            HandleException annotation = method.getAnnotation(HandleException.class);
            if (annotation == null) {
                // 如果方法上没有注解，则尝试获取类上的注解
                annotation = method.getDeclaringClass().getAnnotation(HandleException.class);
            }

            if (annotation != null) {
                // 检查异常类型是否匹配
                Class<? extends Throwable>[] exceptionTypes = annotation.value();
                if (exceptionTypes.length > 0) {
                    boolean matches = false;
                    for (Class<? extends Throwable> type : exceptionTypes) {
                        if (type.isInstance(ex)) {
                            matches = true;
                            break;
                        }
                    }
                    if (!matches) {
                        throw ex; // 不处理不匹配的异常类型
                    }
                }

                // 处理异常
                return handleExceptionInternal(ex, annotation.errorCode(), annotation.logException(),
                        annotation.publishEvent(),
                        method.getName(), joinPoint.getArgs());
            }

            throw ex;
        }
    }

    /**
     * 处理类级别的HandleException注解
     */
    @Around("@within(com.ge.med.ct.exception.aspect.HandleException)")
    public Object handleExceptionAtClassLevel(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Throwable ex) {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();

            // 获取方法上的注解
            HandleException annotation = method.getAnnotation(HandleException.class);
            if (annotation == null) {
                // 如果方法上没有注解，则尝试获取类上的注解
                annotation = method.getDeclaringClass().getAnnotation(HandleException.class);
            }

            if (annotation != null) {
                // 处理异常
                return handleExceptionInternal(ex, annotation.errorCode(), annotation.logException(),
                        annotation.publishEvent(),
                        method.getName(), joinPoint.getArgs());
            }

            throw ex;
        }
    }

    /**
     * 内部异常处理方法
     */
    private Object handleExceptionInternal(Throwable ex, ErrorCode errorCode, boolean logException,
            boolean publishEvent,
            String methodName, Object[] args) {

        // 如果已经是QAToolException，则直接使用
        if (ex instanceof QAToolException) {
            QAToolException qaEx = (QAToolException) ex;

            // 记录异常
            if (logException) {
                logException(qaEx, methodName, args);
            }

            // 发布事件
            if (publishEvent) {
                publishExceptionEvent(qaEx);
            }

            throw qaEx;
        }

        // 如果不是QAToolException，则包装为QAToolException
        if (errorCode == null) {
            errorCode = ErrorCode.PROCESSING;
        }

        Message messageKey = getDefaultMessageForErrorCode(errorCode);
        QAToolException qaEx = ExceptionFactory.createException(errorCode, messageKey, ex, methodName);

        // 记录异常
        if (logException) {
            logException(qaEx, methodName, args);
        }

        // 发布事件
        if (publishEvent) {
            publishExceptionEvent(qaEx);
        }

        throw qaEx;
    }

    /**
     * 记录异常
     */
    private void logException(QAToolException ex, String methodName, Object[] args) {
        LOGGER.log(Level.SEVERE, "Exception in method {0}: {1}",
                new Object[] { methodName, ex.getMessage() });
        LOGGER.log(Level.SEVERE, "", ex);

        // 记录异常统计
        ExceptionStatistics.getInstance().recordException(ex);
    }

    /**
     * 发布异常事件
     */
    private void publishExceptionEvent(QAToolException ex) {
        ExceptionEvent event = new ExceptionEvent(this, ex);
        com.ge.med.ct.laf2.utils.EventBus.getInstance().post(event);
    }

    /**
     * 根据错误码获取默认消息
     *
     * @param errorCode 错误码
     * @return 默认消息
     */
    private Message getDefaultMessageForErrorCode(ErrorCode errorCode) {
        if (errorCode == null) {
            return CommonMessages.UNKNOWN_ERROR;
        }

        String code = errorCode.getCode();
        if (code.startsWith("DICOM-")) {
            return DicomMessages.PROCESSING_ERROR;
        } else if (code.startsWith("BIZ-")) {
            return BusinessMessages.PROCESSING_ERROR;
        } else if (code.startsWith("UI-")) {
            return UIMessages.OPERATION_ERROR;
        } else {
            return CommonMessages.SYSTEM_ERROR;
        }
    }
}