package com.ge.med.ct.exception.core;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.message.Message;
import com.ge.med.ct.exception.message.UIMessages;

import java.util.Map;

/**
 * UI异常类
 * 用于表示UI操作过程中的异常
 */
public class UIException extends QAToolException {
    private static final long serialVersionUID = 1L;

    /**
     * 创建异常构建器
     *
     * @return 异常构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建UI异常
     *
     * @param message 异常消息
     */
    public UIException(Message message) {
        super(ErrorCode.OPERATION, message);
    }

    /**
     * 创建带原因的UI异常
     *
     * @param message 异常消息
     * @param cause   原因
     */
    public UIException(Message message, Throwable cause) {
        super(ErrorCode.OPERATION, message, cause);
    }

    /**
     * 创建带错误码的UI异常
     *
     * @param errorCode 错误码
     * @param message   异常消息
     */
    public UIException(ErrorCode errorCode, Message message) {
        super(errorCode, message);
    }

    /**
     * 创建带错误码和原因的UI异常
     *
     * @param errorCode 错误码
     * @param message   异常消息
     * @param cause     原因
     */
    public UIException(ErrorCode errorCode, Message message, Throwable cause) {
        super(errorCode, message, cause);
    }

    /**
     * 创建带消息的UI异常
     *
     * @param message 消息
     * @param args    消息参数
     */
    public UIException(Message message, Object... args) {
        super(ErrorCode.OPERATION, message, args);
    }

    /**
     * 创建带消息和原因的UI异常
     *
     * @param message 消息
     * @param cause   原因
     * @param args    消息参数
     */
    public UIException(Message message, Throwable cause, Object... args) {
        super(ErrorCode.OPERATION, message, cause, args);
    }

    /**
     * 创建带错误码和消息的UI异常
     *
     * @param errorCode 错误码
     * @param message   消息
     * @param args      消息参数
     */
    public UIException(ErrorCode errorCode, Message message, Object... args) {
        super(errorCode, message, args);
    }

    /**
     * 创建带错误码、消息和原因的UI异常
     *
     * @param errorCode 错误码
     * @param message   消息
     * @param cause     原因
     * @param args      消息参数
     */
    public UIException(ErrorCode errorCode, Message message, Throwable cause, Object... args) {
        super(errorCode, message, cause, args);
    }

    /**
     * UI异常构建器
     */
    public static class Builder extends QAToolException.Builder {
        /**
         * 创建UI异常构建器
         */
        public Builder() {
            super();
            errorCode(ErrorCode.OPERATION);
            message(UIMessages.OPERATION_ERROR);
        }

        /**
         * 设置错误码
         *
         * @param errorCode 错误码
         * @return 构建器
         */
        public Builder errorCode(ErrorCode errorCode) {
            super.errorCode(errorCode);
            return this;
        }

        /**
         * 构建UI异常
         *
         * @return UI异常实例
         */
        @Override
        public UIException build() {
            // 直接创建UIException对象，而不是先创建QAToolException对象
            ErrorCode errorCode = this.errorCode;
            Message message = this.message;
            Throwable cause = this.cause;
            Object[] messageArgs = this.messageArgs;

            UIException uiException = new UIException(
                    errorCode,
                    message,
                    cause,
                    messageArgs);

            // 添加上下文
            for (Map.Entry<String, Object> entry : this.context.entrySet()) {
                uiException.addContext(entry.getKey(), entry.getValue());
            }

            return uiException;
        }
    }
}
