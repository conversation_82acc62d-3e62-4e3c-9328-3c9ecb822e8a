package com.ge.med.ct.exception.core;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.message.CommonMessages;
import com.ge.med.ct.exception.message.Message;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 质量分析工具基础异常类
 * 所有自定义异常的基类
 */
public class QAToolException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    protected ErrorCode errorCode;
    protected Message message;
    protected Object[] messageArgs;
    protected LocalDateTime timestamp;
    protected Map<String, Object> context;

    /**
     * 创建异常构建器
     *
     * @return 异常构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建基础异常
     *
     * @param message 异常消息
     */
    public QAToolException(Message message) {
        this(message, Optional.empty());
    }

    /**
     * 创建带原因的基础异常
     *
     * @param message 异常消息
     * @param cause 原因
     */
    public QAToolException(Message message, Throwable cause) {
        this(ErrorCode.UNEXPECTED, message, cause);
    }

    /**
     * 创建带错误码的基础异常
     *
     * @param errorCode 错误码
     * @param message 异常消息
     */
    public QAToolException(ErrorCode errorCode, Message message) {
        this(errorCode, message, Optional.empty());
    }

    /**
     * 创建带错误码和原因的基础异常
     *
     * @param errorCode 错误码
     * @param message 异常消息
     * @param cause 原因
     */
    public QAToolException(ErrorCode errorCode, Message message, Throwable cause) {
        super(message != null ? message.toStr() : "Unknown error", cause);
        this.errorCode = errorCode != null ? errorCode : ErrorCode.UNEXPECTED;
        this.message = message;
        this.messageArgs = null;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
    }

    /**
     * 创建带消息的基础异常
     *
     * @param message 消息
     * @param args 消息参数
     */
    public QAToolException(Message message, Object... args) {
        this(ErrorCode.UNEXPECTED, message, null, args);
    }

    /**
     * 创建带消息和原因的基础异常
     *
     * @param message 消息
     * @param cause 原因
     * @param args 消息参数
     */
    public QAToolException(Message message, Throwable cause, Object... args) {
        this(ErrorCode.UNEXPECTED, message, cause, args);
    }

    /**
     * 创建带错误码和消息的基础异常
     *
     * @param errorCode 错误码
     * @param message 消息
     * @param args 消息参数
     */
    public QAToolException(ErrorCode errorCode, Message message, Object... args) {
        this(errorCode, message, null, args);
    }

    /**
     * 创建带错误码、消息和原因的基础异常
     *
     * @param errorCode 错误码
     * @param message 消息
     * @param cause 原因
     * @param args 消息参数
     */
    public QAToolException(ErrorCode errorCode, Message message, Throwable cause, Object... args) {
        super(message != null ? message.toStr(args) : CommonMessages.UNEXPECTED_ERROR.toStr(), cause);
        this.errorCode = errorCode != null ? errorCode : ErrorCode.UNEXPECTED;
        this.message = message;
        this.messageArgs = args;
        this.timestamp = LocalDateTime.now();
        this.context = new HashMap<>();
    }

    /**
     * 私有构造方法，供构建器使用
     */
    protected QAToolException(Builder builder) {
        super(builder.formattedMessage, builder.cause);
        this.errorCode = builder.errorCode;
        this.message = builder.message;
        this.messageArgs = builder.messageArgs;
        this.timestamp = builder.timestamp;
        this.context = new HashMap<>(builder.context);
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public ErrorCode getErrorCode() {
        return errorCode;
    }

    /**
     * 获取消息
     *
     * @return 消息
     */
    public Message getMessageObject() {
        return message;
    }

    /**
     * 获取消息参数
     *
     * @return 消息参数数组
     */
    public Object[] getMessageArgs() {
        return messageArgs;
    }

    /**
     * 获取时间戳
     *
     * @return 时间戳
     */
    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    /**
     * 获取上下文
     *
     * @return 上下文Map
     */
    public Map<String, Object> getContext() {
        return new HashMap<>(context);
    }

    /**
     * 获取上下文值
     *
     * @param key 键
     * @return 值
     */
    public Object getContextValue(String key) {
        return context.get(key);
    }

    /**
     * 添加上下文
     *
     * @param key 键
     * @param value 值
     * @return 当前异常实例
     */
    public QAToolException addContext(String key, Object value) {
        context.put(key, value);
        return this;
    }

    /**
     * 异常构建器
     */
    public static class Builder {
        protected ErrorCode errorCode = ErrorCode.UNEXPECTED;
        protected Message message = null;
        protected Object[] messageArgs = new Object[0];
        protected Throwable cause;
        protected LocalDateTime timestamp = LocalDateTime.now();
        protected Map<String, Object> context = new HashMap<>();
        protected String formattedMessage;

        /**
         * 设置错误码
         *
         * @param errorCode 错误码
         * @return 构建器
         */
        public Builder errorCode(ErrorCode errorCode) {
            this.errorCode = errorCode != null ? errorCode : ErrorCode.UNEXPECTED;
            return this;
        }

        /**
         * 设置消息
         *
         * @param message 消息
         * @return 构建器
         */
        public Builder message(Message message) {
            this.message = message != null ? message :CommonMessages.UNEXPECTED_ERROR;
            return this;
        }

        /**
         * 设置消息参数
         *
         * @param args 消息参数
         * @return 构建器
         */
        public Builder messageArgs(Object... args) {
            this.messageArgs = args != null ? args : new Object[0];
            return this;
        }

        /**
         * 设置原因
         *
         * @param cause 原因
         * @return 构建器
         */
        public Builder cause(Throwable cause) {
            this.cause = cause;
            return this;
        }

        /**
         * 设置时间戳
         *
         * @param timestamp 时间戳
         * @return 构建器
         */
        public Builder timestamp(LocalDateTime timestamp) {
            this.timestamp = timestamp != null ? timestamp : LocalDateTime.now();
            return this;
        }

        /**
         * 添加上下文
         *
         * @param key 键
         * @param value 值
         * @return 构建器
         */
        public Builder context(String key, Object value) {
            this.context.put(key, value);
            return this;
        }

        /**
         * 添加上下文Map
         *
         * @param context 上下文Map
         * @return 构建器
         */
        public Builder context(Map<String, Object> context) {
            if (context != null) {
                this.context.putAll(context);
            }
            return this;
        }

        /**
         * 构建异常
         *
         * @return 异常实例
         */
        public QAToolException build() {
            this.formattedMessage = message != null ? message.format(messageArgs).toStr() : CommonMessages.UNKNOWN_ERROR.toStr();
            return new QAToolException(this);
        }
    }
}
