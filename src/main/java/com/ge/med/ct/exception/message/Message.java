package com.ge.med.ct.exception.message;

/**
 * 消息接口
 * 定义消息的基本操作
 */
public interface Message {
    /**
     * 获取消息键
     * 
     * @return 消息键
     */
    String getKey();
    
    /**
     * 获取默认消息
     * 
     * @return 默认消息
     */
    String getDefaultMessage();
    
    /**
     * 格式化消息
     * 
     * @param args 消息参数
     * @return 格式化后的消息
     */
    Message format(Object... args);
    
    /**
     * 转换为字符串
     * 
     * @return 消息字符串
     */
    String toStr();
    
    /**
     * 获取消息编码
     * 兼容 IMessage 接口
     * 
     * @return 消息编码
     */
    default String getCode() {
        return getKey();
    }
    
    /**
     * 获取消息模板
     * 兼容 IMessage 接口
     * 
     * @return 消息模板
     */
    default String getTemplate() {
        return getDefaultMessage();
    }
    
    /**
     * 格式化消息并返回字符串
     * 兼容 IMessage 接口
     * 
     * @param args 消息参数
     * @return 格式化后的消息字符串
     */
    default String toStr(Object... args) {
        return format(args).toStr();
    }
}
