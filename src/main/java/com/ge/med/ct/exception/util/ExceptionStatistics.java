package com.ge.med.ct.exception.util;

import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.service.LogManager;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 异常统计类
 * 记录和统计系统中发生的异常
 */
public class ExceptionStatistics {
    private static final Logger LOG = LogManager.getInstance().getLogger(ExceptionStatistics.class);
    private static final ExceptionStatistics INSTANCE = new ExceptionStatistics();
    
    // 异常计数
    private final Map<String, AtomicInteger> exceptionCounts = new ConcurrentHashMap<>();
    
    // 首次出现时间
    private final Map<String, LocalDateTime> firstOccurrence = new ConcurrentHashMap<>();
    
    // 最后出现时间
    private final Map<String, LocalDateTime> lastOccurrence = new ConcurrentHashMap<>();
    
    // 异常上下文
    private final Map<String, Map<String, Object>> exceptionContext = new ConcurrentHashMap<>();
    
    private ExceptionStatistics() {
        LOG.info("异常统计服务初始化");
    }
    
    /**
     * 获取实例
     * 
     * @return ExceptionStatistics实例
     */
    public static ExceptionStatistics getInstance() {
        return INSTANCE;
    }
    
    /**
     * 记录异常
     * 
     * @param exception 异常
     */
    public void recordException(QAToolException exception) {
        if (exception == null) {
            return;
        }
        
        String key = getExceptionKey(exception);
        
        // 更新计数
        exceptionCounts.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
        
        // 更新时间
        LocalDateTime now = LocalDateTime.now();
        firstOccurrence.putIfAbsent(key, now);
        lastOccurrence.put(key, now);
        
        // 更新上下文
        Map<String, Object> context = exception.getContext();
        if (!context.isEmpty()) {
            exceptionContext.computeIfAbsent(key, k -> new ConcurrentHashMap<>()).putAll(context);
        }
        
        // 记录日志
        LOG.log(Level.FINE, "记录异常: {0}, 总计: {1}", 
                new Object[]{key, exceptionCounts.get(key).get()});
    }
    
    /**
     * 获取异常键
     * 
     * @param exception 异常
     * @return 异常键
     */
    private String getExceptionKey(QAToolException exception) {
        return exception.getErrorCode().getCode();
    }
    
    /**
     * 获取异常计数
     * 
     * @return 异常计数映射
     */
    public Map<String, Integer> getExceptionCounts() {
        Map<String, Integer> result = new HashMap<>();
        exceptionCounts.forEach((key, count) -> result.put(key, count.get()));
        return result;
    }
    
    /**
     * 获取异常首次出现时间
     * 
     * @param errorCode 错误码
     * @return 首次出现时间
     */
    public LocalDateTime getFirstOccurrence(String errorCode) {
        return firstOccurrence.get(errorCode);
    }
    
    /**
     * 获取异常最后出现时间
     * 
     * @param errorCode 错误码
     * @return 最后出现时间
     */
    public LocalDateTime getLastOccurrence(String errorCode) {
        return lastOccurrence.get(errorCode);
    }
    
    /**
     * 获取异常上下文
     * 
     * @param errorCode 错误码
     * @return 异常上下文
     */
    public Map<String, Object> getExceptionContext(String errorCode) {
        Map<String, Object> context = exceptionContext.get(errorCode);
        return context != null ? new HashMap<>(context) : new HashMap<>();
    }
    
    /**
     * 获取异常频率（每小时）
     * 
     * @param errorCode 错误码
     * @return 每小时异常数
     */
    public double getExceptionFrequency(String errorCode) {
        LocalDateTime first = firstOccurrence.get(errorCode);
        LocalDateTime last = lastOccurrence.get(errorCode);
        AtomicInteger count = exceptionCounts.get(errorCode);
        
        if (first == null || last == null || count == null) {
            return 0.0;
        }
        
        long seconds = java.time.Duration.between(first, last).getSeconds();
        if (seconds <= 0) {
            return count.get(); // 如果时间间隔太短，直接返回计数
        }
        
        return (double) count.get() / seconds * 3600; // 转换为每小时
    }
    
    /**
     * 重置统计
     */
    public void reset() {
        exceptionCounts.clear();
        firstOccurrence.clear();
        lastOccurrence.clear();
        exceptionContext.clear();
        LOG.info("异常统计已重置");
    }
    
    /**
     * 获取统计报告
     * 
     * @return 统计报告
     */
    public String getReport() {
        StringBuilder report = new StringBuilder("异常统计报告:\n");
        
        if (exceptionCounts.isEmpty()) {
            report.append("没有记录异常\n");
            return report.toString();
        }
        
        report.append(String.format("%-15s %-10s %-25s %-25s %-10s\n", 
                "错误码", "计数", "首次出现", "最后出现", "频率(每小时)"));
        report.append("--------------------------------------------------------------------------------\n");
        
        exceptionCounts.forEach((key, count) -> {
            LocalDateTime first = firstOccurrence.get(key);
            LocalDateTime last = lastOccurrence.get(key);
            double frequency = getExceptionFrequency(key);
            
            report.append(String.format("%-15s %-10d %-25s %-25s %-10.2f\n", 
                    key, count.get(), first, last, frequency));
        });
        
        return report.toString();
    }
}
