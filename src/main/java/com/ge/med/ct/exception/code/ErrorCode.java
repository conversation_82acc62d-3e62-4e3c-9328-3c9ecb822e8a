package com.ge.med.ct.exception.code;

/**
 * 统一错误码枚举
 * 包含所有类型的错误码
 */
public enum ErrorCode {
    // 系统错误码 (1000-1999)
    UNKNOWN("SYS-1000", "未知错误"),
    UNEXPECTED("SYS-1001", "意外错误"),
    CONFIG("SYS-1002", "配置错误"),
    IO("SYS-1003", "IO错误"),
    INIT("SYS-1004", "初始化错误"),
    DATA("SYS-1005", "数据错误"),
    SYS_EXTERNAL("SYS-1006", "外部系统错误"),
    SYS_RESOURCE("SYS-1007", "资源错误"),
    THREAD_INTERRUPTED("SYS-1008", "线程中断"),

    // DICOM错误码 (2000-2999)
    PROCESSING("DICOM-2000", "DICOM处理错误"),
    DICOM_VALIDATION("DICOM-2001", "DICOM验证错误"),
    READ("DICOM-2004", "DICOM读取错误"),
    WRITE("DICOM-2005", "DICOM写入错误"),
    INVALID_FILE("DICOM-2006", "无效的DICOM文件"),
    DICOM_INIT("DICOM-2007", "DICOM Manager 初始化错误"),
    TAG_PROCESSING("DICOM-2009", "DICOM标签处理错误"),
    DICOM_SERIES_PROCESSING("DICOM-2010", "DICOM序列处理错误"),
    DICOM_IMAGE_PROCESSING("DICOM-2011", "DICOM图像处理错误"),
    DICOM_STORAGE_ERROR("DICOM-2012", "DICOM存储错误"),
    DICOM_CONFIGURATION("DICOM-2013", "DICOM配置错误"),

    // 业务错误码 (3000-3999)
    BIZ_VALIDATION("BIZ-3001", "业务验证错误"),
    BIZ_PROCESSING("BIZ-3002", "业务处理错误"),
    CONFIGURATION("BIZ-3003", "DICOM配置错误"),

    // 分析模块错误码 (3100-3199)
    VALIDATION("ANALYSIS-3101", "参数验证错误"),
    ANALYSIS("ANALYSIS-3102", "分析执行错误"),
    PROTOCOL("ANALYSIS-3103", "协议处理错误"),
    MANAGEMENT("ANALYSIS-3104", "分析管理错误"),

    // UI错误码 (4000-4999)
    UI_UNKNOWN("UI-4000", "未知UI错误"),
    DISPLAY("UI-4001", "显示错误"),
    INPUT("UI-4002", "输入错误"),
    OPERATION("UI-4003", "UI操作错误"),
    UI_INIT("UI-4004", "UI初始化错误");

    private final String code;
    private final String description;

    ErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取错误码
     * @return 错误码字符串
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取错误描述
     * @return 错误描述字符串
     */
    public String getDescription() {
        return description;
    }

}
