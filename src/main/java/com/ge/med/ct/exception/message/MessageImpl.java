package com.ge.med.ct.exception.message;

import java.text.MessageFormat;

/**
 * 消息实现类
 */
public class MessageImpl implements IMessage, Message {
    
    private final String code;
    private final String template;
    private Object[] args;
    
    /**
     * 创建消息
     * 
     * @param code 消息编码
     * @param template 消息模板
     */
    public MessageImpl(String code, String template) {
        this.code = code;
        this.template = template;
        this.args = new Object[0];
    }
    
    /**
     * 创建带参数的消息
     * 
     * @param code 消息编码
     * @param template 消息模板
     * @param args 消息参数
     */
    public MessageImpl(String code, String template, Object... args) {
        this.code = code;
        this.template = template;
        this.args = args;
    }
    
    @Override
    public String getCode() {
        return code;
    }
    
    @Override
    public String getTemplate() {
        return template;
    }
    
    @Override
    public String getKey() {
        return code;
    }
    
    @Override
    public String getDefaultMessage() {
        return template;
    }
    
    @Override
    public Message format(Object... args) {
        return new MessageImpl(this.code, this.template, args);
    }
    
    @Override
    public String toStr() {
        return toStr(this.args);
    }
    
    @Override
    public String toStr(Object... args) {
        if (args != null && args.length > 0) {
            return MessageFormat.format(template, args);
        } else if (this.args.length > 0) {
            return MessageFormat.format(template, this.args);
        } else {
            return template;
        }
    }
    
    /**
     * 使用指定参数创建新的消息实例
     * 
     * @param args 消息参数
     * @return 新的消息实例
     */
    public MessageImpl with(Object... args) {
        return new MessageImpl(this.code, this.template, args);
    }
    
    @Override
    public String toString() {
        return "[" + code + "] " + toStr();
    }
}
