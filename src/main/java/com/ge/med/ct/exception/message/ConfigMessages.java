package com.ge.med.ct.exception.message;

/**
 * 配置验证相关的消息枚举
 * 定义配置验证过程中的错误消息
 */
public enum ConfigMessages implements Message {
    // 基础配置错误
    CONFIG_NULL("config.null", "配置对象为空"),
    CONFIG_INVALID("config.invalid", "无效的配置: {0}"),

    // 表格配置错误
    TABLE_CONFIG_EMPTY("table.config.empty", "表格配置为空"),
    TABLE_TYPE_INVALID("table.type.invalid", "无效的表格类型: {0}"),

    // 列配置错误
    COLUMN_CONFIG_EMPTY("column.config.empty", "表格类型 {0} 的列配置为空"),
    COLUMN_NAME_EMPTY("column.name.empty", "列名不能为空"),
    COLUMN_TAG_MISSING("column.tag.missing", "列 {0} 没有对应的标签映射"),

    // 标签配置错误
    TAG_CONFIG_EMPTY("tag.config.empty", "标签映射配置为空"),
    TAG_CONFIG_TYPE_EMPTY("tag.config.type.empty", "表格类型 {0} 的标签映射配置为空"),

    // 验证结果
    VALIDATION_FAILED("validation.failed", "配置验证失败: {0}");

    private final AbstractMessage delegate;

    ConfigMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage){};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}