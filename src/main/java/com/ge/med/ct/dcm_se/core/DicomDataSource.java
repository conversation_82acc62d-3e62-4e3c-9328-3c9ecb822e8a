package com.ge.med.ct.dcm_se.core;

import java.util.Map;

/**
 * DICOM数据源配置接口
 * 支持文件系统和PESI两种数据源类型
 */
public interface DicomDataSource {
    
    /**
     * 获取数据源类型
     */
    DataSourceType getType();
    
    /**
     * 获取数据源配置参数
     */
    Map<String, Object> getConfig();
    
    /**
     * 验证数据源配置是否有效
     */
    boolean isValid();
    
    /**
     * 获取数据源描述
     */
    String getDescription();
}
