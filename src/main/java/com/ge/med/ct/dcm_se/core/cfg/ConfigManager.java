package com.ge.med.ct.dcm_se.core.cfg;

import com.ge.med.ct.dcm_se.core.cfg.dicom.DicomConfigService;
import com.ge.med.ct.dcm_se.core.cfg.report.ReportConfigService;
import com.ge.med.ct.dcm_se.core.cfg.table.TableColumnManager;
import com.ge.med.ct.service.PlatformUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Logger;

/**
 * 配置管理器
 * 负责加载、管理和提供应用程序配置
 *
 * 支持从配置文件加载静态配置，以及在运行时动态设置配置项
 */
public class ConfigManager {
    private static final Logger LOG = Logger.getLogger(ConfigManager.class.getName());
    private static volatile ConfigManager instance;

    // 配置缓存
    private final Properties properties;
    // 运行时设置的属性，优先级高于配置文件
    private final Properties runtimeProperties;
    private final Map<String, Object> configCache;
    private final String configPath;

    // 子配置模块 - 使用懒加载
    private DicomConfigService dicomConfig;
    private TableColumnManager tableConfig;
    private ReportConfigService reportConfig;

    // 配置监听器
    private final List<ConfigChangeListener> listeners = new CopyOnWriteArrayList<>();

    /**
     * 获取默认配置实例
     */
    public static ConfigManager getInstance() {
        return getInstance(AppDefaults.DEFAULT_CONFIG_PATH);
    }

    /**
     * 获取指定配置路径的实例
     */
    public static synchronized ConfigManager getInstance(String configPath) {
        if (instance == null || !instance.configPath.equals(configPath)) {
            instance = new ConfigManager(configPath);
        }
        return instance;
    }

    /**
     * 获取应用程序运行目录
     */
    private String getAppRunDir() {
        return System.getProperty("user.dir");
    }

    /**
     * 构建配置文件路径
     * 
     * @param basePath 基础路径
     * @param configFile 配置文件名或相对路径
     * @return 完整的配置文件路径
     */
    private String buildConfigPath(String basePath, String configFile) {
        return basePath + File.separator + configFile;
    }

    /**
     * 私有构造函数
     */
    private ConfigManager(String configPath) {
        this.configPath = configPath;
        this.properties = new Properties();
        this.runtimeProperties = new Properties();
        this.configCache = new ConcurrentHashMap<>();

        try {
            loadConfig();
        } catch (RuntimeException e) {
            LOG.severe("初始化配置失败: " + e.getMessage());
            throw e; // 重新抛出异常，不使用默认配置
        }
    }

    /**
     * 加载配置
     * 
     * 配置加载顺序：
     * - Windows: 从类路径的config/目录加载主配置文件
     * - Linux: 从程序运行目录的config/目录加载主配置文件
     * - 如果主配置文件加载成功，加载表格配置文件
     */
    private void loadConfig() {
        // 清空现有配置
        properties.clear();
        configCache.clear();

        // 尝试加载配置
        boolean configLoaded = false;
        StringBuilder errorMessages = new StringBuilder();

        if (PlatformUtils.isWindows()) {
            // Windows系统 - 从类路径加载
            configLoaded = loadFromClasspath(configPath, properties, errorMessages, "配置");
        } else {
            // Linux/Unix系统 - 从程序运行目录加载
            configLoaded = loadFromRunDir(configPath, properties, errorMessages, "配置");
        }

        // 如果配置加载成功，加载表格配置
        if (configLoaded) {
            loadTableConfig();

            // 初始化子配置
            this.dicomConfig = new DicomConfigService(this);
            this.tableConfig = new TableColumnManager(this.properties);
            this.reportConfig = new ReportConfigService(this);

            // 通知监听器
            notifyListeners();
        } else {
            // 所有加载尝试都失败
            String errorMsg = "无法加载配置文件: " + configPath + "\n尝试了以下方法: " + errorMessages;
            LOG.severe(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    /**
     * 从类路径加载资源
     * 
     * @param resourcePath 资源路径
     * @param targetProps 要加载到的Properties对象
     * @param errorMessages 错误信息收集器
     * @param resourceType 资源类型描述（用于日志）
     * @return 加载是否成功
     */
    private boolean loadFromClasspath(String resourcePath, Properties targetProps, 
                                     StringBuilder errorMessages, String resourceType) {
        LOG.info("Windows系统 - 尝试从类路径加载" + resourceType + ": " + resourcePath);
        
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(resourcePath)) {
            if (is != null) {
                try (InputStreamReader reader = new InputStreamReader(is, StandardCharsets.UTF_8)) {
                    targetProps.load(reader);
                    LOG.info("从类路径加载" + resourceType + "(UTF-8): " + resourcePath);
                    return true;
                }
            } else {
                String message = "类路径中未找到" + resourceType + "文件: " + resourcePath;
                LOG.warning(message);
                errorMessages.append(message).append("; ");
            }
        } catch (IOException e) {
            String message = "从类路径加载" + resourceType + "失败: " + e.getMessage();
            LOG.warning(message);
            errorMessages.append(message).append("; ");
        }
        
        return false;
    }

    /**
     * 从运行目录加载资源
     * 
     * @param resourcePath 资源路径
     * @param targetProps 要加载到的Properties对象
     * @param errorMessages 错误信息收集器
     * @param resourceType 资源类型描述（用于日志）
     * @return 加载是否成功
     */
    private boolean loadFromRunDir(String resourcePath, Properties targetProps, 
                                  StringBuilder errorMessages, String resourceType) {
        // 获取应用运行目录
        String appDir = getAppRunDir();
        LOG.info("Linux系统 - 应用运行目录: " + appDir);
        
        // 构建运行目录下的配置文件路径
        String fullPath = buildConfigPath(appDir, resourcePath);
        LOG.info("尝试从路径加载" + resourceType + ": " + fullPath);

        // 尝试从应用运行目录加载
        try (FileInputStream fis = new FileInputStream(fullPath);
                InputStreamReader reader = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
            targetProps.load(reader);
            LOG.info("从应用运行目录加载" + resourceType + "(UTF-8): " + fullPath);
            return true;
        } catch (IOException e) {
            String message = "从应用运行目录加载" + resourceType + "失败: " + e.getMessage();
            LOG.fine(message);
            errorMessages.append(message).append("; ");
        }
        
        return false;
    }

    /**
     * 加载表格配置文件
     */
    private void loadTableConfig() {
        // 如果存在表格配置文件，加载它
        String tableConfigFile = getString("table.config.file", AppDefaults.DEFAULT_TABLE_CONFIG_PATH);

        // 确保表格配置文件路径包含config前缀
        if (!tableConfigFile.startsWith("config/") && !tableConfigFile.startsWith("config\\")) {
            // 添加config/前缀
            tableConfigFile = "config/" + tableConfigFile;
        }

        LOG.info("处理后的表格配置文件路径: " + tableConfigFile);
        
        boolean tableConfigLoaded = false;
        StringBuilder tableErrorMessages = new StringBuilder();
        Properties tableProperties = new Properties();

        if (PlatformUtils.isWindows()) {
            // Windows系统 - 从类路径加载表格配置
            tableConfigLoaded = loadFromClasspath(tableConfigFile, tableProperties, tableErrorMessages, "表格配置");
        } else {
            // Linux系统 - 从运行目录加载表格配置
            tableConfigLoaded = loadFromRunDir(tableConfigFile, tableProperties, tableErrorMessages, "表格配置");
        }
        
        // 如果加载成功，合并到主配置中
        if (tableConfigLoaded) {
            properties.putAll(tableProperties);
        } else {
            // 如果未能加载表格配置，记录警告
            LOG.warning("无法加载表格配置文件: " + tableErrorMessages.toString());
        }
    }

    /**
     * 设置配置属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void setProperty(String key, String value) {
        runtimeProperties.setProperty(key, value);
        // 清除缓存，确保下次获取时使用新值
        configCache.remove(key);
        LOG.info("设置配置属性: " + key + " = " + value);
        // 通知监听器配置已变更
        notifyListeners();
    }

    /**
     * 获取字符串配置项
     * 优先从运行时属性中获取，如果没有则从配置文件中获取
     */
    public String getString(String key, String defaultValue) {
        return (String) configCache.computeIfAbsent(key, k -> {
            // 首先从运行时属性中获取
            String value = runtimeProperties.getProperty(k);
            if (value != null) {
                return value;
            }
            // 然后从配置文件中获取
            value = properties.getProperty(k);
            return value != null ? value : defaultValue;
        });
    }

    /**
     * 获取整数配置项
     * 优先从运行时属性中获取，如果没有则从配置文件中获取
     */
    public int getInt(String key, int defaultValue) {
        return (int) configCache.computeIfAbsent(key, k -> {
            // 首先从运行时属性中获取
            String value = runtimeProperties.getProperty(k);
            if (value != null) {
                try {
                    return Integer.parseInt(value);
                } catch (NumberFormatException e) {
                    LOG.warning("无法解析运行时整数配置项 " + k + ": " + value);
                }
            }
            // 然后从配置文件中获取
            value = properties.getProperty(k);
            try {
                return value != null ? Integer.parseInt(value) : defaultValue;
            } catch (NumberFormatException e) {
                LOG.warning("无法解析整数配置项 " + k + ": " + value + ", 使用默认值: " + defaultValue);
                return defaultValue;
            }
        });
    }

    /**
     * 获取布尔配置项
     * 优先从运行时属性中获取，如果没有则从配置文件中获取
     */
    public boolean getBoolean(String key, boolean defaultValue) {
        return (boolean) configCache.computeIfAbsent(key, k -> {
            // 首先从运行时属性中获取
            String value = runtimeProperties.getProperty(k);
            if (value != null) {
                return Boolean.parseBoolean(value);
            }
            // 然后从配置文件中获取
            value = properties.getProperty(k);
            return value != null ? Boolean.parseBoolean(value) : defaultValue;
        });
    }

    /**
     * 获取列表配置项
     */
    public List<String> getList(String key) {
        String value = getString(key, null);
        if (value == null) {
            return new ArrayList<>();
        }
        return Arrays.asList(value.split(","));
    }

    public boolean hasKey(String key) {
        return runtimeProperties.containsKey(key) || properties.containsKey(key);
    }

    public String getApplicationName() {
        return getString("app.name", AppDefaults.DEFAULT_APP_NAME);
    }

    public String getApplicationVersion() {
        return getString("app.version", AppDefaults.DEFAULT_APP_VERSION);
    }

    public String getUITheme() {
        return getString("ui.theme", AppDefaults.DEFAULT_UI_THEME);
    }

    public String getLogLevel() {
        return getString("log.level", AppDefaults.DEFAULT_LOG_LEVEL);
    }

    public DicomConfigService getDicomConfig() {
        return dicomConfig;
    }

    public TableColumnManager getTableColumnManager() {
        return tableConfig;
    }

    public ReportConfigService getReportConfig() {
        return reportConfig;
    }

    public void addChangeListener(ConfigChangeListener listener) {
        if (listener != null) {
            listeners.add(listener);
        }
    }

    public void removeChangeListener(ConfigChangeListener listener) {
        listeners.remove(listener);
    }

    private void notifyListeners() {
        for (ConfigChangeListener listener : listeners) {
            try {
                listener.onConfigChanged();
            } catch (Exception e) {
                LOG.warning("通知配置变更监听器失败: " + e.getMessage());
            }
        }
    }

    /**
     * 重新加载所有配置
     * 注意：运行时设置的属性不会被清除
     */
    public void reloadAll() {
        configCache.clear();
        properties.clear();
        loadConfig();

        if (dicomConfig != null) {
            dicomConfig.reload();
        }

        if (tableConfig != null) {
            tableConfig.reload();
        }
        
        if (reportConfig != null) {
            reportConfig.reload();
        }

        LOG.info("所有配置已重新加载");
    }

    public void clearRuntimeProperties() {
        runtimeProperties.clear();
        configCache.clear();
        LOG.info("所有运行时属性已清除");
        notifyListeners();
    }

    public interface ConfigChangeListener {
        void onConfigChanged();
    }
}
