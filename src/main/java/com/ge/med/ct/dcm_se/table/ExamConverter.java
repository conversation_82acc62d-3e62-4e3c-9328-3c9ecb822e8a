package com.ge.med.ct.dcm_se.table;

import com.ge.med.ct.dcm_se.model.DicomExam;

import java.util.logging.Logger;

/**
 * 检查数据转换器
 * 负责将DicomExam对象转换为表格显示格式
 */
public class ExamConverter implements TableDataEngine.DataConverter<DicomExam> {
    private static final Logger LOG = Logger.getLogger(ExamConverter.class.getName());
    
    private final TagFormatter formatter;
    private final TableDataEngine.TableColumnConfig columnConfig;
    
    public ExamConverter(TagFormatter formatter, TableDataEngine.TableColumnConfig columnConfig) {
        this.formatter = formatter;
        this.columnConfig = columnConfig;
    }
    
    @Override
    public String getColumnValue(DicomExam exam, String columnName, TableDataEngine.TableType tableType) {
        if (exam == null) {
            return "";
        }
        
        try {
            // 处理特殊列
            if (columnConfig.isSpecialColumn(columnName)) {
                return getSpecialColumnValue(exam, columnName);
            }
            
            // 获取标签ID
            String tagId = columnConfig.getTagId(columnName);
            if (tagId == null || tagId.isEmpty()) {
                LOG.fine("列 " + columnName + " 未找到对应的标签ID");
                return "";
            }
            
            // 获取标签值
            String tagValue = exam.getTagValue(tagId);
            if (tagValue == null || tagValue.trim().isEmpty()) {
                return "";
            }
            
            // 根据列名进行特殊格式化
            return formatColumnValue(columnName, tagId, tagValue);
            
        } catch (Exception e) {
            LOG.warning(String.format("获取检查列值失败: %s - %s", columnName, e.getMessage()));
            return "";
        }
    }
    
    /**
     * 获取特殊列的值
     */
    private String getSpecialColumnValue(DicomExam exam, String columnName) {
        switch (columnName) {
            case "SeriesCount":
                return String.valueOf(exam.getSeriesCount());
            default:
                return "";
        }
    }
    
    /**
     * 根据列名格式化值
     */
    private String formatColumnValue(String columnName, String tagId, String tagValue) {
        switch (columnName) {
            case "PatientID":
                return formatter.formatSafeText(tagValue);
                
            case "PatientName":
                return formatter.formatPatientName(tagValue);
                
            case "PatientSex":
                return formatter.formatSex(tagValue);
                
            case "PatientAge":
                return formatter.formatAge(tagValue);
                
            case "StudyDate":
                return formatter.formatDate(tagValue);
                
            case "StudyTime":
                return formatter.formatTime(tagValue);
                
            case "StudyDescription":
                return formatter.formatSafeText(tagValue);
                
            default:
                // 使用通用格式化
                return formatter.format(tagId, tagValue);
        }
    }
}
