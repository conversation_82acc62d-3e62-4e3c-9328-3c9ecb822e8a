# DICOM-SE (DICOM Simplified Edition)

## 📋 项目概述

DICOM-SE 是质量保证工具中DICOM功能的简化重构版本，专注于核心有用功能，移除了复杂的冗余代码。

### 🎯 设计目标

- **简化架构**: 移除过度设计，保持核心功能
- **统一接口**: 支持文件系统和PESI两种数据源的统一访问
- **向后兼容**: 保持与现有功能的等价性
- **易于维护**: 清晰的职责分离，简化的依赖关系
- **PESI集成**: 原生支持PESI路径查询方案

## 📁 目录结构

```
dcm_se/
├── core/           # 核心接口和服务实现
├── model/          # DICOM数据模型
├── tag/            # 标签处理和转换
├── table/          # 表格数据转换
├── storage/        # 数据导出功能
└── DicomSeExample.java  # 使用示例
```

## 🚀 核心功能

### 1. 统一数据源接口

支持两种数据获取方式：

#### 文件系统扫描
```java
FileSystemDataSource fileSource = new FileSystemDataSource.Builder()
    .rootDirectory("/path/to/dicom/files")
    .recursive(true)
    .enableMonitoring(false)
    .build();
```

#### PESI路径查询
```java
PesiDataSource pesiSource = new PesiDataSource.Builder()
    .executeQueryScript("/usr/local/bin/executequery.sh")
    .imagePoolPath("/usr/g/sdc_image_pool/images")
    .queryTimeout(30)
    .build();
```

### 2. 统一服务接口

```java
DicomDataService service = DicomDataServiceImpl.getInstance();

// 加载数据
service.loadDicomData(dataSource, progressCallback);

// 获取数据提供者
DicomDataProvider provider = service.getDataProvider();

// 使用数据
List<DicomExam> exams = provider.getAllExams();
String filePath = provider.getImageFilePath(imageId);
```

### 3. 表格数据转换

```java
TableDataEngine tableEngine = new TableDataEngine();

// 转换检查数据
Vector<Vector<String>> examData = tableEngine.convertExamData(exams);
String[] columns = tableEngine.getTableColumnDisplayNames(TableType.EXAM);
```

### 4. 数据导出功能

```java
DicomExporter exporter = new DicomExporter();

// 导出JSON
String jsonFile = exporter.exportToJson(provider, "export.json");

// 导出CSV
String csvFile = exporter.exportToCsv(provider, "export.csv", ExportLevel.EXAM);

// 导出统计
String statsFile = exporter.exportStatistics(provider, "stats.json");
```

## 🏗️ 架构设计

### 核心组件

#### 1. 数据源层 (DataSource)
- `DicomDataSource`: 数据源配置接口
- `FileSystemDataSource`: 文件系统数据源
- `PesiDataSource`: PESI数据源

#### 2. 服务层 (Service)
- `DicomDataService`: 主服务接口
- `DicomDataServiceImpl`: 服务实现
- `PesiQueryService`: PESI查询服务
- `SystemCommandService`: 系统命令服务

#### 3. 数据提供层 (Provider)
- `DicomDataProvider`: 统一数据访问接口
- `AbstractDicomDataProvider`: 抽象基类
- `FileSystemDataProvider`: 文件系统数据提供者
- `PesiDataProvider`: PESI数据提供者

#### 4. 数据模型层 (Model)
- `DicomExam`: 检查模型
- `DicomSeries`: 序列模型
- `DicomImage`: 图像模型
- `DicomFileModel`: 文件模型
- `DicomTag`: 标签模型

#### 5. 标签处理层 (Tag)
- `DicomTagConstants`: 标签常量
- `DicomTagConverter`: 标签转换器

#### 6. 表格转换层 (Table)
- `TableDataEngine`: 表格数据引擎
- `ExamConverter/SeriesConverter/ImageConverter`: 数据转换器
- `TagFormatter`: 标签格式化器

#### 7. 存储层 (Storage)
- `DicomExporter`: 数据导出器

### 数据流

```
数据源 → 服务层 → 数据提供层 → 应用层
  ↓         ↓         ↓          ↓
配置    → 加载    → 查询      → 使用
```

## 🔧 关键特性

### 1. 简化设计
- 移除了复杂的异步处理
- 合并了功能重叠的类
- 简化了依赖关系

### 2. 统一接口
- 文件系统和PESI使用相同的接口
- 透明的数据源切换
- 一致的错误处理

### 3. PESI集成
- 完整实现executequery.sh调用
- 支持find命令构建PESI路径
- 路径验证和文件存在性检查

### 4. 性能优化
- 使用ConcurrentHashMap提高并发性能
- 缓存常用属性减少计算
- 优化的数据结构构建

### 5. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 超时控制和资源管理

## 📊 与原版对比

| 特性 | 原版 (dicom2/) | 简化版 (dcm_se/) |
|------|----------------|------------------|
| 包数量 | 7个 | 5个 |
| 类数量 | 30+ | 20+ |
| 代码行数 | ~8000 | ~4000 |
| 复杂度 | 高 | 中等 |
| 维护性 | 困难 | 容易 |
| 功能完整性 | 100% | 100% |
| PESI支持 | 无 | 完整 |

## 🚀 使用指南

### 1. 基本使用

```java
// 创建数据源
DicomDataSource source = new FileSystemDataSource.Builder()
    .rootDirectory("/path/to/dicom")
    .build();

// 获取服务
DicomDataService service = DicomDataServiceImpl.getInstance();

// 加载数据
service.loadDicomData(source, callback);

// 使用数据
DicomDataProvider provider = service.getDataProvider();
List<DicomExam> exams = provider.getAllExams();
```

### 2. PESI查询

```java
// 创建PESI数据源
PesiDataSource pesiSource = PesiDataSource.createDefault();

// 加载PESI数据
service.loadDicomData(pesiSource, callback);

// 搜索患者
DicomSearchCriteria criteria = new DicomSearchCriteria.Builder()
    .patientName("Patient Name")
    .seriesDescription("Series Description")
    .build();

List<DicomExam> results = provider.searchExams(criteria);
```

### 3. 表格转换

```java
TableDataEngine engine = new TableDataEngine();

// 转换数据
Vector<Vector<String>> data = engine.convertExamData(exams);
String[] columns = engine.getTableColumnDisplayNames(TableType.EXAM);
```

### 4. 数据导出

```java
DicomExporter exporter = new DicomExporter();

// 导出JSON
exporter.exportToJson(provider, "output.json");

// 导出CSV
exporter.exportToCsv(provider, "output.csv", ExportLevel.EXAM);
```

## 🔍 扩展指南

### 添加新的数据源类型

1. 实现 `DicomDataSource` 接口
2. 继承 `AbstractDicomDataProvider` 类
3. 在 `DicomDataServiceImpl` 中添加处理逻辑

### 添加新的表格列

1. 在 `TableColumnConfig` 中添加列配置
2. 在对应的 `Converter` 中添加格式化逻辑
3. 在 `TagFormatter` 中添加特殊格式化（如需要）

### 添加新的导出格式

1. 在 `DicomExporter` 中添加新的导出方法
2. 实现相应的格式化逻辑

## 📝 注意事项

1. **线程安全**: 所有核心组件都是线程安全的
2. **资源管理**: 使用完毕后调用 `close()` 方法释放资源
3. **错误处理**: 所有方法都有完善的异常处理
4. **日志记录**: 使用Java标准日志记录重要操作
5. **性能考虑**: 大量数据时注意内存使用

## 🎯 未来规划

1. **性能优化**: 进一步优化大数据量处理
2. **功能扩展**: 根据需要添加新的DICOM标签支持
3. **测试完善**: 增加更多的单元测试和集成测试
4. **文档完善**: 提供更详细的API文档

---

**版本**: 1.0.0  
**最后更新**: 2024年  
**维护者**: Quality Assurance Tool Team
