package com.ge.med.ct.dcm_se.core;

import java.util.List;

/**
 * 系统命令执行服务接口
 * 封装executequery.sh和find命令的执行
 */
public interface SystemCommandService {
    
    /**
     * 执行executequery.sh命令
     * @param sql SQL查询语句
     * @return 命令执行结果
     */
    CommandResult executeQuery(String sql);
    
    /**
     * 执行find命令查找文件
     * @param searchPattern 搜索模式
     * @return 找到的文件路径列表
     */
    List<String> findFiles(String searchPattern);
    
    /**
     * 检查executequery.sh命令是否可用
     * @return executequery.sh是否可用
     */
    boolean isExecuteQueryAvailable();
    
    /**
     * 设置命令执行超时时间
     * @param timeoutSeconds 超时时间（秒）
     */
    void setCommandTimeout(int timeoutSeconds);
    
    /**
     * 命令执行结果
     */
    class CommandResult {
        private final int exitCode;
        private final String stdout;
        private final String stderr;
        private final boolean success;
        private final long executionTime;
        
        public CommandResult(int exitCode, String stdout, String stderr, boolean success, long executionTime) {
            this.exitCode = exitCode;
            this.stdout = stdout != null ? stdout : "";
            this.stderr = stderr != null ? stderr : "";
            this.success = success;
            this.executionTime = executionTime;
        }
        
        public int getExitCode() {
            return exitCode;
        }
        
        public String getStdout() {
            return stdout;
        }
        
        public String getStderr() {
            return stderr;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public long getExecutionTime() {
            return executionTime;
        }
        
        /**
         * 获取输出行列表
         */
        public List<String> getOutputLines() {
            if (stdout == null || stdout.trim().isEmpty()) {
                return List.of();
            }
            return List.of(stdout.split("\n"));
        }
        
        /**
         * 检查是否有错误输出
         */
        public boolean hasError() {
            return !success || (stderr != null && !stderr.trim().isEmpty());
        }
        
        /**
         * 获取完整的错误信息
         */
        public String getErrorMessage() {
            if (success && (stderr == null || stderr.trim().isEmpty())) {
                return null;
            }
            
            StringBuilder error = new StringBuilder();
            if (!success) {
                error.append("命令执行失败 (退出码: ").append(exitCode).append(")");
            }
            if (stderr != null && !stderr.trim().isEmpty()) {
                if (error.length() > 0) {
                    error.append(": ");
                }
                error.append(stderr);
            }
            return error.toString();
        }
        
        @Override
        public String toString() {
            return String.format("CommandResult{exitCode=%d, success=%s, executionTime=%dms, stdoutLines=%d, stderrLength=%d}",
                    exitCode, success, executionTime, getOutputLines().size(), stderr.length());
        }
    }
}
