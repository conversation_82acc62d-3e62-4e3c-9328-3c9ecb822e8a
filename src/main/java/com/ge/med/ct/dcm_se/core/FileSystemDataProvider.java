package com.ge.med.ct.dcm_se.core;

import com.ge.med.ct.dcm_se.model.DicomFileModel;
import com.ge.med.ct.exception.core.DicomException;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 文件系统数据提供者
 * 从本地文件系统扫描和加载DICOM文件
 */
public class FileSystemDataProvider extends AbstractDicomDataProvider {
    
    private ExecutorService executorService;
    private FileSystemDataSource currentSource;
    
    public FileSystemDataProvider() {
        this.dataSourceType = DataSourceType.FILE_SYSTEM;
        this.executorService = Executors.newFixedThreadPool(4);
    }
    
    /**
     * 从文件系统加载DICOM数据
     */
    public void loadFromFileSystem(FileSystemDataSource source, DicomDataService.ProgressCallback callback) {
        this.currentSource = source;
        
        try {
            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("开始扫描文件系统...", 0));
            }
            
            // 清除现有数据
            clearData();
            
            // 扫描文件
            List<File> dicomFiles = scanDicomFiles(source);
            
            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo(
                        String.format("找到 %d 个DICOM文件，开始加载...", dicomFiles.size()), 10));
            }
            
            // 加载文件
            loadDicomFiles(dicomFiles, callback);
            
            LOG.info(String.format("文件系统加载完成: %d个检查, %d个序列, %d个图像", 
                    exams.size(), series.size(), images.size()));
            
        } catch (Exception e) {
            LOG.severe("文件系统加载失败: " + e.getMessage());
            throw new RuntimeException("文件系统加载失败", e);
        }
    }
    
    @Override
    protected String getDataSourceDescription() {
        if (currentSource != null) {
            return currentSource.getDescription();
        }
        return "文件系统数据源";
    }
    
    @Override
    protected void performShutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    // === 私有方法 ===
    
    private List<File> scanDicomFiles(FileSystemDataSource source) {
        List<File> dicomFiles = new ArrayList<>();
        File rootDir = new File(source.getRootDirectory());
        
        if (!rootDir.exists() || !rootDir.isDirectory()) {
            LOG.warning("根目录不存在或不是目录: " + source.getRootDirectory());
            return dicomFiles;
        }
        
        scanDirectory(rootDir, source, dicomFiles);
        
        LOG.info(String.format("扫描完成，找到 %d 个DICOM文件", dicomFiles.size()));
        return dicomFiles;
    }
    
    private void scanDirectory(File directory, FileSystemDataSource source, List<File> dicomFiles) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            if (file.isDirectory() && source.isRecursive()) {
                scanDirectory(file, source, dicomFiles);
            } else if (file.isFile() && isDicomFile(file, source)) {
                dicomFiles.add(file);
            }
        }
    }
    
    private boolean isDicomFile(File file, FileSystemDataSource source) {
        String fileName = file.getName().toLowerCase();
        String[] extensions = source.getFileExtensions();
        
        if (extensions == null || extensions.length == 0) {
            // 默认扩展名
            return fileName.endsWith(".dcm") || fileName.endsWith(".dicom") || !fileName.contains(".");
        }
        
        return Arrays.stream(extensions)
                .anyMatch(ext -> ext.isEmpty() ? !fileName.contains(".") : fileName.endsWith(ext.toLowerCase()));
    }
    
    private void loadDicomFiles(List<File> dicomFiles, DicomDataService.ProgressCallback callback) {
        int totalFiles = dicomFiles.size();
        int processedFiles = 0;
        
        for (File file : dicomFiles) {
            try {
                loadDicomFile(file);
                processedFiles++;
                
                if (callback != null && processedFiles % 10 == 0) {
                    int progress = 10 + (int) ((processedFiles * 80.0) / totalFiles);
                    callback.onProgress(new DicomDataService.ProgressInfo(
                            String.format("已处理 %d/%d 个文件", processedFiles, totalFiles), progress));
                }
                
            } catch (Exception e) {
                LOG.warning(String.format("加载文件失败: %s - %s", file.getAbsolutePath(), e.getMessage()));
            }
        }
        
        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo("构建数据结构...", 90));
        }
        
        // 构建索引
        buildDataStructure();
        
        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo("加载完成", 100));
        }
    }
    
    private void loadDicomFile(File file) throws DicomException {
        // 这里应该使用实际的DICOM读取器
        // 为了简化，我们创建一个基本的文件模型
        
        String fileId = generateFileId(file);
        DicomFileModel model = new DicomFileModel(fileId);
        model.setFilePath(file.getAbsolutePath());
        model.setFileName(file.getName());
        model.setFileSize(file.length());
        model.setFileType("DICOM");
        
        // 这里应该读取DICOM标签
        // 暂时使用模拟数据
        addMockDicomTags(model, file);
        
        addFileModel(model);
    }
    
    private String generateFileId(File file) {
        // 生成唯一的文件ID
        return "file_" + Math.abs(file.getAbsolutePath().hashCode());
    }
    
    private void addMockDicomTags(DicomFileModel model, File file) throws DicomException {
        // 这里应该使用真实的DICOM读取器来读取标签
        // 暂时添加一些模拟标签用于测试
        
        String fileName = file.getName();
        
        // 模拟患者信息
        model.addTag("(0010,0020)", new com.ge.med.ct.dcm_se.model.DicomTag(
                "(0010,0020)", "Patient_" + fileName.hashCode(), org.dcm4che3.data.VR.LO));
        
        model.addTag("(0010,0010)", new com.ge.med.ct.dcm_se.model.DicomTag(
                "(0010,0010)", "Patient Name " + fileName.substring(0, Math.min(fileName.length(), 5)), org.dcm4che3.data.VR.PN));
        
        // 模拟检查信息
        model.addTag("(0020,000D)", new com.ge.med.ct.dcm_se.model.DicomTag(
                "(0020,000D)", "1.2.3.4.5." + Math.abs(fileName.hashCode()), org.dcm4che3.data.VR.UI));
        
        // 模拟序列信息
        model.addTag("(0020,000E)", new com.ge.med.ct.dcm_se.model.DicomTag(
                "(0020,000E)", "1.2.3.4.5.6." + Math.abs(fileName.hashCode()), org.dcm4che3.data.VR.UI));
        
        // 模拟图像信息
        model.addTag("(0008,0018)", new com.ge.med.ct.dcm_se.model.DicomTag(
                "(0008,0018)", "1.2.3.4.5.6.7." + Math.abs(fileName.hashCode()), org.dcm4che3.data.VR.UI));
    }
    
    private void buildDataStructure() {
        // 根据文件模型构建检查、序列、图像的层次结构
        // 这里应该实现完整的数据结构构建逻辑
        
        LOG.info("构建数据结构: " + fileModels.size() + " 个文件模型");
        
        // 简化实现：每个文件作为一个独立的图像
        for (DicomFileModel model : fileModels.values()) {
            try {
                buildImageFromFileModel(model);
            } catch (Exception e) {
                LOG.warning("构建图像失败: " + model.getId() + " - " + e.getMessage());
            }
        }
    }
    
    private void buildImageFromFileModel(DicomFileModel model) throws DicomException {
        // 从文件模型构建图像、序列、检查
        
        String studyUID = model.getStudyInstanceUID();
        String seriesUID = model.getSeriesInstanceUID();
        String imageUID = model.getSopInstanceUID();
        
        if (studyUID == null || seriesUID == null || imageUID == null) {
            LOG.warning("文件模型缺少必要的UID: " + model.getId());
            return;
        }
        
        // 创建或获取检查
        if (!exams.containsKey(studyUID)) {
            com.ge.med.ct.dcm_se.model.DicomExam exam = new com.ge.med.ct.dcm_se.model.DicomExam(studyUID);
            exam.setPatientID(model.getPatientID());
            exam.setPatientName(model.getPatientName());
            exam.setStudyInstanceUID(studyUID);
            exams.put(studyUID, exam);
        }
        
        // 创建或获取序列
        if (!series.containsKey(seriesUID)) {
            com.ge.med.ct.dcm_se.model.DicomSeries series = new com.ge.med.ct.dcm_se.model.DicomSeries(seriesUID);
            series.setSeriesInstanceUID(seriesUID);
            series.setModality(model.getModality());
            series.setSeriesDescription(model.getSeriesDescription());
            this.series.put(seriesUID, series);
            
            // 建立检查到序列的关系
            examToSeries.computeIfAbsent(studyUID, k -> new ArrayList<>()).add(seriesUID);
        }
        
        // 使用现有的图像
        DicomImage image = model.getImage();
        if (image != null) {
            images.put(imageUID, image);
            imageToFilePath.put(imageUID, model.getFilePath());
            
            // 建立序列到图像的关系
            seriesToImages.computeIfAbsent(seriesUID, k -> new ArrayList<>()).add(imageUID);
        }
    }
}
