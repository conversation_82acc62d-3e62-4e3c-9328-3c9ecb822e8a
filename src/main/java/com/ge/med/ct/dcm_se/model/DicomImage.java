package com.ge.med.ct.dcm_se.model;

import com.ge.med.ct.exception.core.DicomException;

import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM图像类 (简化版)
 * 表示DICOM文件中的图像数据
 */
public class DicomImage {
    private static final Logger LOG = Logger.getLogger(DicomImage.class.getName());
    
    private final String id;
    private final Map<String, DicomTag> tags;
    private DicomSeries series;
    
    // 常用属性缓存
    private String sopInstanceUID;
    private String instanceNumber;
    private String imageType;
    private Integer rows;
    private Integer columns;
    
    // 图像位置和方向
    private float[] imagePosition;
    private float[] imageOrientation;
    private float[] pixelSpacing;
    
    // 窗宽窗位
    private Float windowCenter;
    private Float windowWidth;
    private Float rescaleSlope;
    private Float rescaleIntercept;
    
    // 文件路径 (用于支持PESI路径)
    private String filePath;
    
    public DicomImage(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException("图像ID不能为空");
        }
        this.id = id;
        this.tags = new HashMap<>();
        LOG.fine("创建新的DICOM图像: " + id);
    }
    
    // === 基本属性访问 ===
    
    public String getId() {
        return id;
    }
    
    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }
    
    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            updateCachedAttributes(tagId, tag.getValueAsString());
            LOG.fine("添加标签 " + tagId + " 到图像 " + id);
        }
    }
    
    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }
    
    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }
    
    // === 图像标识信息 ===
    
    public String getSopInstanceUID() {
        return sopInstanceUID != null ? sopInstanceUID : getTagValue("(0008,0018)");
    }
    
    public void setSopInstanceUID(String sopInstanceUID) {
        this.sopInstanceUID = sopInstanceUID;
        setTagValue("(0008,0018)", sopInstanceUID);
    }
    
    public String getInstanceNumber() {
        return instanceNumber != null ? instanceNumber : getTagValue("(0020,0013)");
    }
    
    public void setInstanceNumber(String instanceNumber) {
        this.instanceNumber = instanceNumber;
        setTagValue("(0020,0013)", instanceNumber);
    }
    
    public String getImageType() {
        return imageType != null ? imageType : getTagValue("(0008,0008)");
    }
    
    public void setImageType(String imageType) {
        this.imageType = imageType;
        setTagValue("(0008,0008)", imageType);
    }
    
    // === 图像尺寸信息 ===
    
    public Integer getRows() {
        if (rows != null) return rows;
        String value = getTagValue("(0028,0010)");
        return value != null ? parseInteger(value) : null;
    }
    
    public void setRows(Integer rows) {
        this.rows = rows;
        setTagValue("(0028,0010)", rows != null ? rows.toString() : null);
    }
    
    public Integer getColumns() {
        if (columns != null) return columns;
        String value = getTagValue("(0028,0011)");
        return value != null ? parseInteger(value) : null;
    }
    
    public void setColumns(Integer columns) {
        this.columns = columns;
        setTagValue("(0028,0011)", columns != null ? columns.toString() : null);
    }
    
    // === 图像位置和方向 ===
    
    public float[] getImagePosition() {
        return imagePosition != null ? imagePosition.clone() : null;
    }
    
    public void setImagePosition(float[] imagePosition) {
        this.imagePosition = imagePosition != null ? imagePosition.clone() : null;
    }
    
    public float[] getImageOrientation() {
        return imageOrientation != null ? imageOrientation.clone() : null;
    }
    
    public void setImageOrientation(float[] imageOrientation) {
        this.imageOrientation = imageOrientation != null ? imageOrientation.clone() : null;
    }
    
    public float[] getPixelSpacing() {
        return pixelSpacing != null ? pixelSpacing.clone() : null;
    }
    
    public void setPixelSpacing(float[] pixelSpacing) {
        this.pixelSpacing = pixelSpacing != null ? pixelSpacing.clone() : null;
    }
    
    // === 窗宽窗位 ===
    
    public Float getWindowCenter() {
        return windowCenter;
    }
    
    public void setWindowCenter(Float windowCenter) {
        this.windowCenter = windowCenter;
    }
    
    public Float getWindowWidth() {
        return windowWidth;
    }
    
    public void setWindowWidth(Float windowWidth) {
        this.windowWidth = windowWidth;
    }
    
    public Float getRescaleSlope() {
        return rescaleSlope != null ? rescaleSlope : 1.0f;
    }
    
    public void setRescaleSlope(Float rescaleSlope) {
        this.rescaleSlope = rescaleSlope;
    }
    
    public Float getRescaleIntercept() {
        return rescaleIntercept != null ? rescaleIntercept : 0.0f;
    }
    
    public void setRescaleIntercept(Float rescaleIntercept) {
        this.rescaleIntercept = rescaleIntercept;
    }
    
    // === 文件路径 (支持PESI) ===
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    // === 序列关联 ===
    
    public DicomSeries getSeries() {
        return series;
    }
    
    public void setSeries(DicomSeries series) {
        if (this.series != series) {
            DicomSeries oldSeries = this.series;
            this.series = series;
            
            if (oldSeries != null) {
                oldSeries.removeImage(this);
            }
            
            if (series != null && !series.getImages().contains(this)) {
                series.addImage(this);
            }
        }
    }
    
    // === 工具方法 ===
    
    private void setTagValue(String tagId, String value) {
        if (value != null) {
            try {
                DicomTag tag = new DicomTag(tagId, value, org.dcm4che3.data.VR.LO);
                addTag(tagId, tag);
            } catch (DicomException e) {
                LOG.warning("设置标签值失败: " + tagId + " = " + value);
            }
        }
    }
    
    private void updateCachedAttributes(String tagId, String value) {
        switch (tagId) {
            case "(0008,0018)":
                this.sopInstanceUID = value;
                break;
            case "(0020,0013)":
                this.instanceNumber = value;
                break;
            case "(0008,0008)":
                this.imageType = value;
                break;
            case "(0028,0010)":
                this.rows = parseInteger(value);
                break;
            case "(0028,0011)":
                this.columns = parseInteger(value);
                break;
        }
    }
    
    private Integer parseInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 清除像素数据，释放内存
     */
    public void clearPixelData() {
        tags.remove("(7FE0,0010)"); // Pixel Data tag
    }
    
    @Override
    public String toString() {
        return String.format("DicomImage[id=%s, sopInstanceUID=%s, instanceNumber=%s, imageType=%s, rows=%s, columns=%s]",
                id, getSopInstanceUID(), getInstanceNumber(), getImageType(), getRows(), getColumns());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DicomImage other = (DicomImage) obj;
        return id.equals(other.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
