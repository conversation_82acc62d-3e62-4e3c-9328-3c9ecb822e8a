package com.ge.med.ct.dcm_se.core;

import java.util.List;

/**
 * PESI路径查询服务接口
 * 实现 dicom-pesi-path-query-guide.md 中的查询逻辑
 */
public interface PesiQueryService {
    
    /**
     * 根据患者信息查询PESI路径
     * @param patientName 患者姓名
     * @param seriesDescription 序列描述
     * @return PESI路径信息列表
     */
    List<PesiPathInfo> queryPesiPaths(String patientName, String seriesDescription);
    
    /**
     * 执行自定义SQL查询
     * @param sql SQL查询语句
     * @return 查询结果列表
     */
    List<PesiQueryResult> executeQuery(String sql);
    
    /**
     * 根据数据库查询结果构建PESI路径
     * @param result 数据库查询结果
     * @return 实际的文件路径
     */
    String buildPesiPath(PesiQueryResult result);
    
    /**
     * 验证PESI路径是否存在
     * @param pesiPath PESI路径
     * @return 是否存在对应的文件
     */
    boolean validatePesiPath(String pesiPath);
    
    /**
     * 检查服务是否可用
     * @return executequery.sh是否可用
     */
    boolean isServiceAvailable();
    
    /**
     * PESI查询结果
     */
    class PesiQueryResult {
        private String patientId;
        private String examId;
        private String imageSetId;
        private String imageId;
        private String dcmImageId;
        
        public PesiQueryResult() {}
        
        public PesiQueryResult(String patientId, String examId, String imageSetId, String imageId, String dcmImageId) {
            this.patientId = patientId;
            this.examId = examId;
            this.imageSetId = imageSetId;
            this.imageId = imageId;
            this.dcmImageId = dcmImageId;
        }
        
        // Getter和Setter方法
        public String getPatientId() {
            return patientId;
        }
        
        public void setPatientId(String patientId) {
            this.patientId = patientId;
        }
        
        public String getExamId() {
            return examId;
        }
        
        public void setExamId(String examId) {
            this.examId = examId;
        }
        
        public String getImageSetId() {
            return imageSetId;
        }
        
        public void setImageSetId(String imageSetId) {
            this.imageSetId = imageSetId;
        }
        
        public String getImageId() {
            return imageId;
        }
        
        public void setImageId(String imageId) {
            this.imageId = imageId;
        }
        
        public String getDcmImageId() {
            return dcmImageId;
        }
        
        public void setDcmImageId(String dcmImageId) {
            this.dcmImageId = dcmImageId;
        }
        
        @Override
        public String toString() {
            return String.format("PesiQueryResult{patientId='%s', examId='%s', imageSetId='%s', imageId='%s', dcmImageId='%s'}",
                    patientId, examId, imageSetId, imageId, dcmImageId);
        }
    }
    
    /**
     * PESI路径信息
     */
    class PesiPathInfo {
        private final String actualFilePath;
        private final PesiQueryResult queryResult;
        private final boolean fileExists;
        private final long fileSize;
        
        public PesiPathInfo(String actualFilePath, PesiQueryResult queryResult, boolean fileExists) {
            this(actualFilePath, queryResult, fileExists, 0L);
        }
        
        public PesiPathInfo(String actualFilePath, PesiQueryResult queryResult, boolean fileExists, long fileSize) {
            this.actualFilePath = actualFilePath;
            this.queryResult = queryResult;
            this.fileExists = fileExists;
            this.fileSize = fileSize;
        }
        
        public String getActualFilePath() {
            return actualFilePath;
        }
        
        public PesiQueryResult getQueryResult() {
            return queryResult;
        }
        
        public boolean isFileExists() {
            return fileExists;
        }
        
        public long getFileSize() {
            return fileSize;
        }
        
        @Override
        public String toString() {
            return String.format("PesiPathInfo{actualFilePath='%s', fileExists=%s, fileSize=%d}",
                    actualFilePath, fileExists, fileSize);
        }
    }
}
