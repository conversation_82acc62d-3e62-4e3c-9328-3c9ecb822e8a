package com.ge.med.ct.dcm_se.core;

/**
 * 数据源类型枚举
 */
public enum DataSourceType {
    
    /**
     * 文件系统扫描模式
     * 扫描指定目录下的DICOM文件
     */
    FILE_SYSTEM("文件系统", "扫描本地文件系统中的DICOM文件"),
    
    /**
     * PESI路径查询模式
     * 通过数据库查询获取PESI路径
     */
    PESI("PESI查询", "通过数据库查询获取DICOM文件的PESI路径");
    
    private final String displayName;
    private final String description;
    
    DataSourceType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
