package com.ge.med.ct.dcm_se.table;

import com.ge.med.ct.dcm_se.tag.DicomTagConverter;

import java.text.DecimalFormat;
import java.util.logging.Logger;

/**
 * 标签格式化器
 * 用于格式化DICOM标签值以便在表格中显示
 */
public class TagFormatter {
    private static final Logger LOG = Logger.getLogger(TagFormatter.class.getName());
    
    // 数值格式化器
    private static final DecimalFormat INTEGER_FORMAT = new DecimalFormat("#0");
    private static final DecimalFormat FLOAT_FORMAT = new DecimalFormat("#0.##");
    private static final DecimalFormat POSITION_FORMAT = new DecimalFormat("#0.0");
    
    /**
     * 格式化标签值
     */
    public String format(String tagId, Object value) {
        if (value == null) {
            return "";
        }
        
        // 使用DicomTagConverter进行基本格式化
        String formattedValue = DicomTagConverter.formatTagValue(tagId, value);
        
        // 针对表格显示进行额外的格式化
        return formatForTable(tagId, formattedValue);
    }
    
    /**
     * 针对表格显示的特殊格式化
     */
    private String formatForTable(String tagId, String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        
        // 根据标签类型进行特殊格式化
        switch (tagId) {
            case "(0020,0032)": // Image Position Patient
                return formatImagePosition(value);
            case "(0028,0030)": // Pixel Spacing
                return formatPixelSpacing(value);
            case "(0028,1050)": // Window Center
            case "(0028,1051)": // Window Width
                return formatWindowValue(value);
            case "(0018,0050)": // Slice Thickness
                return formatSliceThickness(value);
            case "(0020,1041)": // Slice Location
                return formatSliceLocation(value);
            default:
                return value;
        }
    }
    
    /**
     * 格式化图像位置
     */
    public String formatImagePosition(String position) {
        if (position == null || position.trim().isEmpty()) {
            return "";
        }
        
        try {
            // DICOM图像位置格式: x\y\z
            String[] parts = position.split("\\\\");
            if (parts.length >= 3) {
                float x = Float.parseFloat(parts[0].trim());
                float y = Float.parseFloat(parts[1].trim());
                float z = Float.parseFloat(parts[2].trim());
                
                return String.format("(%.1f, %.1f, %.1f)", x, y, z);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析图像位置: " + position);
        }
        
        return position;
    }
    
    /**
     * 格式化像素间距
     */
    public String formatPixelSpacing(String spacing) {
        if (spacing == null || spacing.trim().isEmpty()) {
            return "";
        }
        
        try {
            // DICOM像素间距格式: row\column
            String[] parts = spacing.split("\\\\");
            if (parts.length >= 2) {
                float row = Float.parseFloat(parts[0].trim());
                float col = Float.parseFloat(parts[1].trim());
                
                return String.format("%.2f x %.2f", row, col);
            } else if (parts.length == 1) {
                float value = Float.parseFloat(parts[0].trim());
                return FLOAT_FORMAT.format(value);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析像素间距: " + spacing);
        }
        
        return spacing;
    }
    
    /**
     * 格式化窗位/窗宽值
     */
    public String formatWindowValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 可能包含多个值，用\分隔
            String[] parts = value.split("\\\\");
            if (parts.length > 0) {
                float firstValue = Float.parseFloat(parts[0].trim());
                return INTEGER_FORMAT.format(firstValue);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析窗位/窗宽值: " + value);
        }
        
        return value;
    }
    
    /**
     * 格式化层厚
     */
    public String formatSliceThickness(String thickness) {
        if (thickness == null || thickness.trim().isEmpty()) {
            return "";
        }
        
        try {
            float value = Float.parseFloat(thickness.trim());
            return FLOAT_FORMAT.format(value) + " mm";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析层厚: " + thickness);
        }
        
        return thickness;
    }
    
    /**
     * 格式化层位置
     */
    public String formatSliceLocation(String location) {
        if (location == null || location.trim().isEmpty()) {
            return "";
        }
        
        try {
            float value = Float.parseFloat(location.trim());
            return POSITION_FORMAT.format(value);
        } catch (NumberFormatException e) {
            LOG.fine("无法解析层位置: " + location);
        }
        
        return location;
    }
    
    /**
     * 格式化整数值
     */
    public String formatInteger(Object value) {
        if (value == null) {
            return "";
        }
        
        Integer intValue = DicomTagConverter.getIntegerValue(value);
        if (intValue != null) {
            return INTEGER_FORMAT.format(intValue);
        }
        
        return DicomTagConverter.getStringValue(value);
    }
    
    /**
     * 格式化浮点数值
     */
    public String formatFloat(Object value) {
        if (value == null) {
            return "";
        }
        
        Float floatValue = DicomTagConverter.getFloatValue(value);
        if (floatValue != null) {
            return FLOAT_FORMAT.format(floatValue);
        }
        
        return DicomTagConverter.getStringValue(value);
    }
    
    /**
     * 格式化日期
     */
    public String formatDate(String date) {
        return DicomTagConverter.formatDicomDate(date);
    }
    
    /**
     * 格式化时间
     */
    public String formatTime(String time) {
        return DicomTagConverter.formatDicomTime(time);
    }
    
    /**
     * 格式化患者姓名
     */
    public String formatPatientName(String name) {
        return DicomTagConverter.formatPatientName(name);
    }
    
    /**
     * 格式化年龄
     */
    public String formatAge(String age) {
        return DicomTagConverter.formatAge(age);
    }
    
    /**
     * 格式化性别
     */
    public String formatSex(String sex) {
        return DicomTagConverter.formatSex(sex);
    }
    
    /**
     * 格式化模态
     */
    public String formatModality(String modality) {
        return DicomTagConverter.formatModality(modality);
    }
    
    /**
     * 截断长文本
     */
    public String truncateText(String text, int maxLength) {
        if (text == null || text.length() <= maxLength) {
            return text;
        }
        
        return text.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * 格式化为表格显示的安全文本
     */
    public String formatSafeText(String text) {
        if (text == null) {
            return "";
        }
        
        // 移除换行符和制表符
        String safeText = text.replaceAll("[\r\n\t]", " ");
        
        // 限制长度
        return truncateText(safeText.trim(), 50);
    }
}
