package com.ge.med.ct.cfg.table;

/**
 * 表格列
 * 表示表格中的一列，包含列的所有属性
 */
public class TableColumn {
    private final String name;
    private final String tagId;
    private final String displayName;
    private final int width;
    private final boolean visible;
    private final int order;

    /**
     * 构造函数
     */
    TableColumn(String name, String tagId, String displayName, int width, boolean visible, int order) {
        this.name = name;
        this.tagId = tagId;
        this.displayName = displayName;
        this.width = width;
        this.visible = visible;
        this.order = order;
    }

    /**
     * 获取列名
     */
    public String getName() {
        return name;
    }

    /**
     * 获取标签ID
     */
    public String getTagId() {
        return tagId;
    }

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取列宽度
     */
    public int getWidth() {
        return width;
    }

    /**
     * 是否可见
     */
    public boolean isVisible() {
        return visible;
    }

    /**
     * 获取排序顺序
     */
    public int getOrder() {
        return order;
    }

    @Override
    public String toString() {
        return "TableColumn{" +
                "name='" + name + '\'' +
                ", tagId='" + tagId + '\'' +
                ", displayName='" + displayName + '\'' +
                ", width=" + width +
                ", visible=" + visible +
                ", order=" + order +
                '}';
    }
}
