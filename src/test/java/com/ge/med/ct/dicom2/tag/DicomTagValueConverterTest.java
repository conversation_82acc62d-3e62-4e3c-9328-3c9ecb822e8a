package com.ge.med.ct.dicom2.tag;

import org.dcm4che3.data.VR;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * DicomTagValueConverter单元测试
 */
public class DicomTagValueConverterTest {
    
    @Test
    public void testGetIntegerValue() {
        // 测试有效整数
        assertEquals(Integer.valueOf(123), DicomTagValueConverter.getIntegerValue("123"));
        assertEquals(Integer.valueOf(-456), DicomTagValueConverter.getIntegerValue("-456"));
        assertEquals(Integer.valueOf(0), DicomTagValueConverter.getIntegerValue("0"));
        
        // 测试带空格的整数
        assertEquals(Integer.valueOf(789), DicomTagValueConverter.getIntegerValue(" 789 "));
        
        // 测试无效输入
        assertNull(DicomTagValueConverter.getIntegerValue(null));
        assertNull(DicomTagValueConverter.getIntegerValue(""));
        assertNull(DicomTagValueConverter.getIntegerValue("   "));
        assertNull(DicomTagValueConverter.getIntegerValue("abc"));
        assertNull(DicomTagValueConverter.getIntegerValue("123.45"));
    }
    
    @Test
    public void testGetFloatValue() {
        // 测试有效浮点数
        assertEquals(Float.valueOf(123.45f), DicomTagValueConverter.getFloatValue("123.45"));
        assertEquals(Float.valueOf(-456.78f), DicomTagValueConverter.getFloatValue("-456.78"));
        assertEquals(Float.valueOf(0.0f), DicomTagValueConverter.getFloatValue("0"));
        
        // 测试带空格的浮点数
        assertEquals(Float.valueOf(789.0f), DicomTagValueConverter.getFloatValue(" 789 "));
        
        // 测试无效输入
        assertNull(DicomTagValueConverter.getFloatValue(null));
        assertNull(DicomTagValueConverter.getFloatValue(""));
        assertNull(DicomTagValueConverter.getFloatValue("   "));
        assertNull(DicomTagValueConverter.getFloatValue("abc"));
    }
    
    @Test
    public void testGetDoubleValue() {
        // 测试有效双精度浮点数
        assertEquals(Double.valueOf(123.45), DicomTagValueConverter.getDoubleValue("123.45"));
        assertEquals(Double.valueOf(-456.78), DicomTagValueConverter.getDoubleValue("-456.78"));
        
        // 测试多值属性（用反斜杠分隔）
        assertEquals(Double.valueOf(123.45), DicomTagValueConverter.getDoubleValue("123.45\\678.90"));
        
        // 测试带空格的双精度浮点数
        assertEquals(Double.valueOf(789.0), DicomTagValueConverter.getDoubleValue(" 789 "));
        
        // 测试无效输入
        assertNull(DicomTagValueConverter.getDoubleValue(null));
        assertNull(DicomTagValueConverter.getDoubleValue(""));
        assertNull(DicomTagValueConverter.getDoubleValue("   "));
        assertNull(DicomTagValueConverter.getDoubleValue("abc"));
    }
    
    @Test
    public void testGetBytesValue() {
        // 测试字符串转字节数组
        byte[] expected1 = "test".getBytes();
        byte[] actual1 = DicomTagValueConverter.getBytesValue("test", VR.LO);
        assertArrayEquals(expected1, actual1);
        
        // 测试十六进制字符串转字节数组（OB和UN类型）
        byte[] expected2 = {(byte)0xAB, (byte)0xCD, (byte)0xEF};
        byte[] actual2 = DicomTagValueConverter.getBytesValue("ABCDEF", VR.OB);
        assertArrayEquals(expected2, actual2);
        
        // 测试奇数长度的十六进制字符串
        byte[] expected3 = {(byte)0x0A, (byte)0xBC};
        byte[] actual3 = DicomTagValueConverter.getBytesValue("ABC", VR.UN);
        assertArrayEquals(expected3, actual3);
        
        // 测试空输入
        assertEquals(0, DicomTagValueConverter.getBytesValue(null, VR.OB).length);
        assertEquals(0, DicomTagValueConverter.getBytesValue("", VR.OB).length);
    }
    
    @Test
    public void testGetRawValue() {
        // 测试各种VR类型的原始值
        assertEquals(Double.valueOf(123.45), DicomTagValueConverter.getRawValue("123.45", VR.DS));
        assertEquals(Double.valueOf(123.45), DicomTagValueConverter.getRawValue("123.45", VR.FD));
        assertEquals(Float.valueOf(123.45f), DicomTagValueConverter.getRawValue("123.45", VR.FL));
        assertEquals(Integer.valueOf(123), DicomTagValueConverter.getRawValue("123", VR.IS));
        assertEquals(Integer.valueOf(123), DicomTagValueConverter.getRawValue("123", VR.SL));
        
        // 测试字符串类型
        assertEquals("test", DicomTagValueConverter.getRawValue("test", VR.LO));
        assertEquals("test", DicomTagValueConverter.getRawValue("test", VR.PN));
        
        // 测试二进制类型
        assertTrue(DicomTagValueConverter.getRawValue("ABCDEF", VR.OB) instanceof byte[]);
    }
    
    @Test
    public void testHexStringToByteArray() {
        // 测试有效十六进制字符串
        byte[] expected1 = {(byte)0x01, (byte)0x23, (byte)0x45, (byte)0x67, (byte)0x89, (byte)0xAB, (byte)0xCD, (byte)0xEF};
        byte[] actual1 = DicomTagValueConverter.hexStringToByteArray("0123456789ABCDEF");
        assertArrayEquals(expected1, actual1);
        
        // 测试奇数长度的十六进制字符串（应该在前面添加0）
        byte[] expected2 = {(byte)0x01, (byte)0x23, (byte)0x45};
        byte[] actual2 = DicomTagValueConverter.hexStringToByteArray("12345");
        assertArrayEquals(expected2, actual2);
        
        // 测试空输入
        assertEquals(0, DicomTagValueConverter.hexStringToByteArray(null).length);
        assertEquals(0, DicomTagValueConverter.hexStringToByteArray("").length);
    }
    
    @Test
    public void testGetGroupFromTagId() {
        assertEquals(0x0010, DicomTagValueConverter.getGroupFromTagId("(0010,0020)"));
        assertEquals(0x0008, DicomTagValueConverter.getGroupFromTagId("(0008,0030)"));
        assertEquals(0x0020, DicomTagValueConverter.getGroupFromTagId("(0020,000D)"));
        
        // 测试无效输入
        assertEquals(0, DicomTagValueConverter.getGroupFromTagId(null));
        assertEquals(0, DicomTagValueConverter.getGroupFromTagId("invalid"));
    }
    
    @Test
    public void testGetElementFromTagId() {
        assertEquals(0x0020, DicomTagValueConverter.getElementFromTagId("(0010,0020)"));
        assertEquals(0x0030, DicomTagValueConverter.getElementFromTagId("(0008,0030)"));
        assertEquals(0x000D, DicomTagValueConverter.getElementFromTagId("(0020,000D)"));
        
        // 测试无效输入
        assertEquals(0, DicomTagValueConverter.getElementFromTagId(null));
        assertEquals(0, DicomTagValueConverter.getElementFromTagId("invalid"));
    }
} 