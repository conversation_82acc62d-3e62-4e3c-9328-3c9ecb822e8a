package com.ge.med.ct.cfg.table;

import com.ge.med.ct.dcm_se.core.cfg.table.TableColumn;
import com.ge.med.ct.dcm_se.core.cfg.table.TableColumnManager;
import com.ge.med.ct.exception.core.QAToolException;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Properties;

import static org.junit.Assert.*;

/**
 * TableColumnManager测试类
 * 用于测试重构后的TableColumnManager功能
 */
public class TableColumnManagerTest {
    private Properties properties;
    private TableColumnManager manager;

    @Before
    public void setUp() {
        properties = new Properties();
        
        // 设置测试属性
        // 检查表格列配置
        properties.setProperty("table.exam.columns", "PatientID,PatientName,StudyDate");
        properties.setProperty("table.exam.column.PatientID.display-name", "患者ID");
        properties.setProperty("table.exam.column.PatientID.width", "100");
        properties.setProperty("table.exam.column.PatientID.visible", "true");
        properties.setProperty("table.exam.column.PatientID.order", "1");
        
        properties.setProperty("table.exam.column.PatientName.display-name", "患者姓名");
        properties.setProperty("table.exam.column.PatientName.width", "150");
        properties.setProperty("table.exam.column.PatientName.visible", "true");
        properties.setProperty("table.exam.column.PatientName.order", "2");
        
        properties.setProperty("table.exam.column.StudyDate.display-name", "检查日期");
        properties.setProperty("table.exam.column.StudyDate.width", "120");
        properties.setProperty("table.exam.column.StudyDate.visible", "true");
        properties.setProperty("table.exam.column.StudyDate.order", "3");
        
        // 序列表格列配置
        properties.setProperty("table.series.columns", "SeriesNumber,Modality,ImageCount");
        properties.setProperty("table.series.column.SeriesNumber.display-name", "序列号");
        properties.setProperty("table.series.column.SeriesNumber.width", "80");
        properties.setProperty("table.series.column.SeriesNumber.visible", "true");
        properties.setProperty("table.series.column.SeriesNumber.order", "1");
        
        properties.setProperty("table.series.column.Modality.display-name", "模态");
        properties.setProperty("table.series.column.Modality.width", "80");
        properties.setProperty("table.series.column.Modality.visible", "true");
        properties.setProperty("table.series.column.Modality.order", "2");
        
        properties.setProperty("table.series.column.ImageCount.display-name", "图像数量");
        properties.setProperty("table.series.column.ImageCount.width", "100");
        properties.setProperty("table.series.column.ImageCount.visible", "true");
        properties.setProperty("table.series.column.ImageCount.order", "3");
        properties.setProperty("table.series.column.ImageCount.special", "true");
        
        // 图像表格列配置
        properties.setProperty("table.image.columns", "InstanceNumber,SliceThickness,KVP");
        properties.setProperty("table.image.column.InstanceNumber.display-name", "实例号");
        properties.setProperty("table.image.column.InstanceNumber.width", "80");
        properties.setProperty("table.image.column.InstanceNumber.visible", "true");
        properties.setProperty("table.image.column.InstanceNumber.order", "1");
        
        properties.setProperty("table.image.column.SliceThickness.display-name", "层厚");
        properties.setProperty("table.image.column.SliceThickness.width", "80");
        properties.setProperty("table.image.column.SliceThickness.visible", "true");
        properties.setProperty("table.image.column.SliceThickness.order", "2");
        
        properties.setProperty("table.image.column.KVP.display-name", "管电压");
        properties.setProperty("table.image.column.KVP.width", "80");
        properties.setProperty("table.image.column.KVP.visible", "true");
        properties.setProperty("table.image.column.KVP.order", "3");
        
        manager = new TableColumnManager(properties);
    }

    @Test
    public void testGetColumns() {
        // 测试获取检查表格列
        List<TableColumn> examColumns = manager.getColumns(TableColumnManager.TABLE_EXAM);
        assertEquals(3, examColumns.size());
        assertEquals("PatientID", examColumns.get(0).getName());
        assertEquals("患者ID", examColumns.get(0).getDisplayName());
        
        // 测试获取序列表格列
        List<TableColumn> seriesColumns = manager.getColumns(TableColumnManager.TABLE_SERIES);
        assertEquals(3, seriesColumns.size());
        assertEquals("SeriesNumber", seriesColumns.get(0).getName());
        assertEquals("序列号", seriesColumns.get(0).getDisplayName());
        
        // 测试获取图像表格列
        List<TableColumn> imageColumns = manager.getColumns(TableColumnManager.TABLE_IMAGE);
        assertEquals(3, imageColumns.size());
        assertEquals("InstanceNumber", imageColumns.get(0).getName());
        assertEquals("实例号", imageColumns.get(0).getDisplayName());
    }

    @Test
    public void testGetVisibleColumns() {
        // 测试获取可见列
        List<TableColumn> visibleColumns = manager.getVisibleColumns(TableColumnManager.TABLE_EXAM);
        assertEquals(3, visibleColumns.size());
        
        // 修改一列为不可见
        properties.setProperty("table.exam.column.StudyDate.visible", "false");
        manager = new TableColumnManager(properties);
        
        visibleColumns = manager.getVisibleColumns(TableColumnManager.TABLE_EXAM);
        assertEquals(2, visibleColumns.size());
    }

    @Test
    public void testGetColumnNames() {
        // 测试获取列名
        List<String> columnNames = manager.getColumnNames(TableColumnManager.TABLE_EXAM);
        assertEquals(3, columnNames.size());
        assertEquals("PatientID", columnNames.get(0));
        assertEquals("PatientName", columnNames.get(1));
        assertEquals("StudyDate", columnNames.get(2));
    }

    @Test
    public void testResolveTagId() {
        // 测试解析标签ID
        String tagId = manager.resolveTagId(TableColumnManager.TABLE_EXAM, "PatientID");
        assertEquals("(0010,0020)", tagId);
        
        // 测试解析特殊列的标签ID
        String specialTagId = manager.resolveTagId(TableColumnManager.TABLE_SERIES, "ImageCount");
        assertEquals("special", specialTagId);
    }

    @Test
    public void testResolveTagName() {
        // 测试解析标签名
        String tagName = manager.resolveTagName(TableColumnManager.TABLE_EXAM, "PatientID");
        assertEquals("PatientID", tagName);
        
        // 测试解析特殊列的标签名
        String specialTagName = manager.resolveTagName(TableColumnManager.TABLE_SERIES, "ImageCount");
        assertEquals("ImageCount", specialTagName);
    }

    @Test
    public void testValidate() {
        // 测试验证功能
        TableColumnManager.ValidationResult result = manager.validate();
        assertTrue(result.isValid());
        assertEquals(0, result.getErrors().size());
        
        // 测试无效配置
        Properties invalidProps = new Properties();
        // 不设置任何属性，导致表格没有列配置
        TableColumnManager invalidManager = new TableColumnManager(invalidProps);
        
        TableColumnManager.ValidationResult invalidResult = invalidManager.validate();
        assertFalse(invalidResult.isValid());
        assertTrue(invalidResult.getErrors().size() > 0);
    }

    @Test
    public void testValidateOrThrow() {
        // 测试正常验证不抛出异常
        try {
            manager.validateOrThrow();
        } catch (QAToolException e) {
            fail("不应该抛出异常");
        }
        
        // 测试无效配置抛出异常
        Properties invalidProps = new Properties();
        // 不设置任何属性，导致表格没有列配置
        TableColumnManager invalidManager = new TableColumnManager(invalidProps);
        
        try {
            invalidManager.validateOrThrow();
            fail("应该抛出异常");
        } catch (QAToolException e) {
            // 预期会抛出异常
            assertTrue(e.getMessage().contains("表格配置"));
        }
    }

    @Test
    public void testStaticValidateTableConfigOrThrow() {
        // 测试静态验证方法
        try {
            TableColumnManager.validateTableConfigOrThrow(manager);
        } catch (QAToolException e) {
            fail("不应该抛出异常");
        }
        
        // 测试null配置抛出异常
        try {
            TableColumnManager.validateTableConfigOrThrow(null);
            fail("应该抛出异常");
        } catch (QAToolException e) {
            // 预期会抛出异常
            assertTrue(e.getMessage().contains("配置对象为空"));
        }
    }
}
