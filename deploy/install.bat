@echo off
REM CT Quality Assurance Tool Installation Script
REM This script builds the application and copies required files to QATdemo directory

echo ===================================
echo CT Quality Assurance Tool Installer
echo ===================================
echo.

REM Set directories
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "TARGET_DIR=%PROJECT_ROOT%\target"

REM Set installation directories - modify as needed
set "QAT_DIR=C:\HuLFox\SCM\LabCodebase\qatdemo"
set "BIN=C:\GEHC\usr\g\bin"
set "MAVEN_SETTINGS=C:\HuLFox\SCM\CT\sd_tools\config\maven\settings.xml"

REM Create directories if they don't exist
if not exist "%QAT_DIR%" (
    echo Creating QATdemo directory: %QAT_DIR%
    mkdir "%QAT_DIR%"
)

if not exist "%BIN%" (
    echo Creating bin directory: %BIN%
    mkdir "%BIN%"
)

REM Check if Maven settings file exists
if not exist "%MAVEN_SETTINGS%" (
    echo WARNING: Maven settings file not found at %MAVEN_SETTINGS%
    echo Will use default Maven settings
    set "MAVEN_SETTINGS_PARAM="
) else (
    echo Using Maven settings from: %MAVEN_SETTINGS%
    set "MAVEN_SETTINGS_PARAM=-s "%MAVEN_SETTINGS%""
)

REM Build project with Maven
echo Building project...
cd /d "%PROJECT_ROOT%"

REM Set Java and Maven encoding options
set "JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8"
set "MAVEN_OPTS=-Dfile.encoding=UTF-8"

call mvn %MAVEN_SETTINGS_PARAM% -Dfile.encoding=UTF-8 clean package -DskipTests
if %ERRORLEVEL% NEQ 0 (
    echo Build failed, please check Maven errors.
    exit /b %ERRORLEVEL%
)
echo Project built successfully!
echo.

REM Create necessary directories
echo Creating necessary directories...
if not exist "%QAT_DIR%\logs" mkdir "%QAT_DIR%\logs"
if not exist "%QAT_DIR%\scripts" mkdir "%QAT_DIR%\scripts"
if not exist "%QAT_DIR%\config" mkdir "%QAT_DIR%\config"
if not exist "%QAT_DIR%\reports" mkdir "%QAT_DIR%\reports"

REM Copy JAR file with simplified name
echo Copying application files...
copy "%TARGET_DIR%\ct-image-quality-1.0-SNAPSHOT.jar" "%QAT_DIR%\ct-image-quality-1.0-SNAPSHOT.jar"
if %ERRORLEVEL% NEQ 0 (
    echo Failed to copy JAR file, please ensure Maven build was successful.
    exit /b %ERRORLEVEL%
)

echo.

exit /b 0
