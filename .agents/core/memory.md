# 项目核心记忆

## 项目概述
- **项目名称**: CT DICOM图像质量分析工具
- **主要功能**: 分析CT DICOM图像质量，提供数据可视化和分析报告
- **技术栈**: Java 1.8, <PERSON>, <PERSON><PERSON>, DCM4CHE 5.31.1, <PERSON><PERSON><PERSON>, Jackson 2.13.5

## 核心模块
- `dicom2`: DICOM数据处理核心功能
- `laf2`: 用户界面组件
- `service`: 通用服务
- `exception`: 异常处理机制
- `config`: 配置管理

## 模块关系
```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  用户界面 (laf2) +---->+  服务层 (service) +---->+  DICOM处理 (dicom2)|
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
         ^                       ^                        ^
         |                       |                        |
         |                       |                        |
         v                       v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
| 配置管理 (config) |     |  异常处理 (aspect) |     |  事件系统 (events)|
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
```

## 核心设计原则
1. **简洁设计/功能必要即可**: 追求简洁、清晰的设计
2. **实用为上**: 以解决实际问题为出发点
3. **注释精简原则**: 注释要精简，一目了然的方法、字段等无需注释

## 异常处理系统

### 异常处理要点
- 异常创建优先使用 ExceptionFactory 或 Builder 模式
- 异常处理优先使用 AOP 的 HandleException 方式
- 异常应包含错误代码和详细消息
- 添加上下文信息，帮助诊断问题

### 消息系统
- 使用 Message 接口定义消息，支持国际化
- 使用消息枚举提供预定义的消息
- 使用消息构建器支持消息的格式化
- 消息格式使用 {0}, {1} 等占位符

### 异常恢复机制
- 使用 RetryStrategy 提供重试功能
- 支持有返回值和无返回值的重试操作
- 使用 HandleException 注解配置重试策略

## 重要约定
- 与AI助手交流默认使用中文
- 遵循Java 1.8兼容性要求
- UI开发必须使用Swing框架
- 异常处理必须使用当前框架中的aspect定义的相关类
- 文档更新应基于现有代码设计，不擅自更改设计
