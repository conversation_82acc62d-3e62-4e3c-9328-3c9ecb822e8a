# Augment Agent 命令优化

## 1. 文件操作命令

### 1.1 使用 Augment Agent 提供的工具

优先使用 Augment Agent 提供的文件操作工具，而不是直接使用操作系统命令。

### 文件查找

#### 报错命令
```
find src/main/resources -name "*.properties" -type f
```

#### 正确命令
```
dir src\main\resources /s /b | findstr .properties
```
或
```
Get-ChildItem -Path src\main\resources -Recurse -Filter *.properties
```

### 文件移动

#### 报错命令
```
mv src/main/resources/file.txt src/main/resources/config/
```

#### 正确命令
```
Move-Item -Path src\main\resources\file.txt -Destination src\main\resources\config\
```

### 文件复制

#### 报错命令
```
cp src/main/resources/file.txt src/main/resources/config/
```

#### 正确命令
```
Copy-Item -Path src\main\resources\file.txt -Destination src\main\resources\config\
```

### 目录创建

#### 报错命令
```
mkdir -p src/main/resources/config
```

#### 正确命令
```
New-Item -Path src\main\resources\config -ItemType Directory -Force
```
或
```
mkdir src\main\resources\config
```

### 文件删除

#### 报错命令
```
rm src/main/resources/file.txt
```

#### 正确命令
```
Remove-Item -Path src\main\resources\file.txt
```
或使用 `remove-files` 工具

## 文件编辑操作

### 文件内容替换

#### 报错情况
- 使用不精确的字符串匹配
- 忽略特殊字符和空格差异
- 不考虑编码问题

#### 最佳实践
1. 先使用 `view` 命令查看确切的文件内容
2. 使用精确的字符串匹配，包括所有空格和特殊字符
3. 如果替换失败，检查错误信息中的差异，调整匹配字符串

### 文件保存

#### 报错情况
- 尝试覆盖现有文件
- 路径不存在

#### 最佳实践
1. 使用 `str-replace-editor` 工具修改现有文件
2. 使用 `save-file` 工具创建新文件
3. 确保目录存在后再保存文件

## 常见错误处理

1. **路径分隔符问题**：Windows 环境下使用反斜杠 `\`，而不是正斜杠 `/`
2. **权限问题**：确保有足够的权限执行操作
3. **编码问题**：处理包含非ASCII字符的文件时注意编码
4. **命令语法差异**：PowerShell 命令与 Bash 命令有显著差异

## 推荐工具

1. 对于文件查看：使用 `str-replace-editor` 的 `view` 命令
2. 对于文件编辑：使用 `str-replace-editor` 的 `str_replace` 命令
3. 对于文件创建：使用 `save-file` 工具
4. 对于文件删除：使用 `remove-files` 工具
5. 对于命令执行：使用 `launch-process` 工具，优先使用 PowerShell 命令
