# CT图像分析程序命令行模式

## 1. 概述

CT图像分析程序支持命令行模式，允许用户在无需图形界面的情况下执行图像分析任务。这种模式特别适合批处理和自动化测试场景。通过命令行参数，用户可以指定输入图像、分析协议和输出文件。

## 2. 命令行参数

### 2.1 基本语法

```bash
IAUI -text -input "<图像文件>" -output "<输出文件>" -protocol "<协议信息>"
```

### 2.2 参数说明

| 参数 | 描述 | 默认值 | 示例 |
|------|------|--------|------|
| `-text` | 启用命令行模式，禁用图形界面 | 必需 | `-text` |
| `-input` | 指定要分析的CT图像文件列表 | 无 | `-input "/usr/g/sdc_image_pool/images/p6/e9/s39/i302.CTDC.1"` |
| `-output` | 指定分析结果输出文件 | "analysis.log" | `-output "/usr/g/bin/results.log"` |
| `-protocol` | 指定要执行的分析协议 | 无 | `-protocol "QA3\|ImgSer20QA\|6"` |

### 2.3 Protocol参数详解

Protocol参数格式：`协议字符串|协议名称|字符串ID`

| 部分 | 描述 | 示例 |
|------|------|------|
| 协议字符串 | 指定分析协议类型，对应IATest.cfg中的type字段 | "QA3", "MEANS", "CENTER_SMUDGE" |
| 协议名称 | 指定测试套件名称，对应IATest.cfg中的顶级键 | "ImgSer20QA", "GrDrHot20QA" |
| 字符串ID | 结果标识符，用于结果报告中标识测试 | "6", "1" |

#### 2.3.1 测试模式

特殊格式：`Test<测试ID> <系列号> opt <可选协议号>|<协议名称>|<字符串ID>`

示例：`Test1 2 opt 0|GrDrHot20QA|1`
- `Test1`：测试ID
- `2`：系列号，指定使用Series_2
- `opt`：固定分隔符
- `0`：可选协议号，0表示执行所有必需协议
- `GrDrHot20QA`：测试套件名称
- `1`：字符串ID

## 3. 配置文件系统

系统使用两类配置文件，它们在命令行模式下同时起作用：

### 3.1 IAProtocol.cfg

- 定义各种协议的基本结构和ROI（感兴趣区域）信息
- 包含每种协议类型的ROI定义（位置、大小、形状等）
- 相对稳定，不随CT扫描仪型号变化
- 通过`ProtocolConfig`构造函数读取，传入协议类型名称

### 3.2 IATest.cfg

- 定义测试套件，包含多个协议的组合和参数
- 可能有多个变体（如IATest.cfg.kl64, IATest.cfg.rt8等），适应不同CT扫描仪型号
- 包含具体的测试参数（如规格值、阈值等）
- 通过`TestReader`类读取，根据扫描仪型号选择适当的变体
- 使用`getConfigFile`方法确定配置文件路径，`getProtocolConfig`方法获取特定协议配置

### 3.3 配置文件格式及字段说明

#### 3.3.1 IATest.cfg 格式及字段

IATest.cfg使用层次化的键值对格式，定义测试套件、系列和协议：

```
<测试套件名称> = {
  PhantomName = "<模体名称>",
  NumberSeries = <系列数量>,
  Series_1 = {
    SeriesName = "<系列名称>",
    kV = <电压值>,
    mA = <电流值>,
    DFOV = <显示视野>,
    reconFilter = "<重建滤波器>",
    gantryRev = <机架旋转数>,
    SFOV = "<扫描视野>",
    numMacroRows = <探测器行数>,
    sliceThickness = <切片厚度>,
    opMode = "<操作模式>",
    reconMode = "<重建模式>",
    NumberProtocols = <协议数量>,
    Protocol_1 = {
      type = <协议类型>,
      NumberImages = <图像数量>,
      NumberSpecs = <规格数量>,
      Images = <图像列表>;,
      LSL = <下限值>,
      USL = <上限值>,
      Specs = <规格值列表>,
      MandatoryTest = <是否必需>;
    },
    Protocol_2 = { ... },
    // 其他协议...
  },
  Series_2 = { ... },
  // 其他系列...
}
```

**主要字段说明**：

1. **测试套件级别**：
   - `PhantomName`：模体名称
   - `NumberSeries`：包含的系列数量

2. **系列级别**：
   - `SeriesName`：系列名称，通常包含扫描参数信息
   - `kV`：扫描电压，单位为kV
   - `mA`：扫描电流，单位为mA
   - `DFOV`：显示视野，单位为mm
   - `reconFilter`：重建滤波器类型（如"STANDARD", "BONE"）
   - `gantryRev`：机架旋转数
   - `SFOV`：扫描视野（如"Head", "Body"）
   - `numMacroRows`：探测器行数（如64或128）
   - `sliceThickness`：切片厚度，单位为mm
   - `opMode`：操作模式（如"AXIAL MODE", "HELICAL MODE"）
   - `reconMode`：重建模式
   - `NumberProtocols`：协议数量

3. **协议级别**：
   - `type`：协议类型（如"MEANS", "QA3", "CENTER_SMUDGE"）
   - `NumberImages`：需要的图像数量
   - `NumberSpecs`：规格值数量
   - `Images`：图像列表，以分号分隔
   - `LSL`：下限值（Lower Specification Limit）
   - `USL`：上限值（Upper Specification Limit）
   - `Specs`：规格值列表，以分号分隔
   - `MandatoryTest`：是否必需测试（0=可选，1=必需）

#### 3.3.2 IAProtocol.cfg 格式及字段

IAProtocol.cfg定义协议的ROI（感兴趣区域）信息和基本参数：

```
Protocol_<协议类型> = {
  NumberRoi = <ROI数量>,
  Roi_1 = {
    Type = "<ROI类型>",
    Xcenter = <X中心坐标>,
    Ycenter = <Y中心坐标>,
    Xwidth = <X宽度>,
    Ywidth = <Y宽度>,
    Moveable = <是否可移动>,
    Resizeable = <是否可调整大小>
  },
  Roi_2 = { ... },
  // 其他ROI...
  Instructions = "<协议说明>"
}
```

**主要字段说明**：

1. **协议级别**：
   - `NumberRoi`：ROI数量
   - `Instructions`：协议说明或指导文本

2. **ROI级别**：
   - `Type`：ROI类型（如"RECT", "CIRCLE", "LINE"）
   - `Xcenter`：ROI中心X坐标
   - `Ycenter`：ROI中心Y坐标
   - `Xwidth`：ROI X方向宽度
   - `Ywidth`：ROI Y方向宽度
   - `Moveable`：是否可移动（0=固定，1=可移动）
   - `Resizeable`：是否可调整大小（0=固定大小，1=可调整）

### 3.4 配置文件选择逻辑

系统根据扫描仪型号选择适当的IATest.cfg变体：

1. **扫描仪型号检测**：
   - 系统读取环境变量或配置文件获取扫描仪型号
   - 使用`TestReader::getConfigFile`方法确定配置文件路径

2. **配置文件映射**：
   - VCT HD 64型号：使用`vcthdConfig`（对应IATest.cfg.vcthd）
   - VCT 64型号：使用`vct64Config`（对应IATest.cfg.vct64）
   - KL 64型号（OPT680_KL或OPT670）：使用`kl64Config`（对应IATest.cfg.kl64）
   - RT 8型号：使用`rt8Config`（对应IATest.cfg.rt8）
   - RT 16型号：使用`rt16Config`（对应IATest.cfg.rt16）
   - 其他型号：使用默认配置文件

3. **配置文件路径**：
   - 默认路径：`/usr/g/config/protocols/`
   - 可通过环境变量`PROTOCOL_CONFIG_PATH`覆盖

4. **配置加载过程**：
   - 首先确定配置文件路径
   - 然后根据协议名称和协议编号构建查询键
   - 使用`getcfg`函数读取配置值
   - 将配置值存储到`ProtocolConfig`对象中

### 3.5 配置文件与命令行参数的对应关系

命令行参数与配置文件字段之间存在直接的对应关系：

| 命令行参数 | 配置文件字段 | 代码中的使用 |
|------------|------------|------------|
| `-protocol` 第一部分（协议字符串） | IATest.cfg中的`type`字段 | 用于选择Protocol子类，在`createProtocolFromConfig`中匹配 |
| `-protocol` 第二部分（协议名称） | IATest.cfg中的顶级键（如`GrDrHot20QA`） | 用于选择测试套件，在`fill_protocol_info`中使用 |
| `-protocol` 第三部分（字符串ID） | 不直接对应配置文件字段 | 用于结果标识，存储在Protocol对象的`selectedString`属性中 |
| `-input` | 不直接对应配置文件字段 | 创建SDC_SELECTION_FILE，然后由`ImageSupplier`读取 |

### 3.6 配置文件字段在代码中的实际使用

#### 3.6.1 IATest.cfg字段的使用

1. **系列参数字段**：
   - `kV`, `mA`, `DFOV`, `reconFilter`等参数用于与图像元数据进行匹配
   - 在`Protocol::checkConfig`方法中使用这些字段验证图像是否符合要求：
     ```cpp
     if (config->getKv() != scanInfo.getKv()) {
         attributesBad = 1;
         sprintf(badAttributeString, "kV mismatch: config=%d, image=%d",
                 config->getKv(), scanInfo.getKv());
     }
     ```
   - 这些字段对于确保分析的准确性至关重要

2. **协议参数字段**：
   - `LSL`和`USL`：用于设置分析的上下限值，决定测试通过/失败
     ```cpp
     if (result < config->getLSL() || result > config->getUSL()) {
         passFail = false;
     }
     ```
   - `Specs`：用于设置特定的规格值，在不同协议中有不同的含义
   - `MandatoryTest`：在测试模式下决定协议是否必须执行
     ```cpp
     if((optionalNumber == 0) && (config->getMandatoryTest() == 1)) {
         addProtocolToList(createProtocolFromConfig(config));
     }
     ```
   - `NumberImages`：验证图像数量是否满足协议要求

#### 3.6.2 IAProtocol.cfg字段的使用

1. **ROI参数**：
   - 这些参数用于创建ROI对象，定义分析区域
   - 在Protocol子类的构造函数中使用：
     ```cpp
     // 创建ROI对象
     for (int i = 0; i < config->getNumberRoi(); i++) {
         ROI *roi = createROI(config->getRoiType(i),
                             config->getRoiXcenter(i),
                             config->getRoiYcenter(i),
                             config->getRoiXwidth(i),
                             config->getRoiYwidth(i));
         rois.push_back(roi);
     }
     ```
   - ROI参数决定了分析的精确位置和范围，直接影响分析结果

2. **其他参数**：
   - `Instructions`：主要用于GUI模式下显示给用户的指导信息，在命令行模式下不使用
   - `NumberRoi`：决定要创建的ROI数量

### 3.7 配置文件字段的重要性级别

不同配置文件字段在代码中的重要性不同：

1. **关键字段**（缺失或错误将导致分析失败）：
   - IATest.cfg: `type`, `LSL`, `USL`, `Specs`
   - IAProtocol.cfg: `NumberRoi`, `Type`, `Xcenter`, `Ycenter`, `Xwidth`, `Ywidth`

2. **重要字段**（影响分析结果的准确性）：
   - IATest.cfg: `kV`, `mA`, `DFOV`, `reconFilter`, `sliceThickness`
   - IAProtocol.cfg: `Moveable`, `Resizeable`

3. **次要字段**（主要用于显示或组织目的）：
   - IATest.cfg: `PhantomName`, `SeriesName`
   - IAProtocol.cfg: `Instructions`

## 4. 执行逻辑

### 4.1 Series选择逻辑

1. **测试模式**（以"Test"开头的协议字符串）：
   - 直接从协议字符串中提取Series编号
   - 例如：`Test1 2 opt 0|GrDrHot20QA|1`中的`2`指定使用Series_2
   - 在`ProtocolMgr::fill_protocol_info`方法中通过`sscanf`解析

2. **直接协议模式**（如`QA3|ImgSer20QA|6`）：
   - 系统比较输入图像的参数（kV、mA、DFOV等）与各Series的参数
   - 选择参数最匹配的Series
   - 使用`Protocol::checkConfig`方法验证图像参数与Series参数的匹配度
   - 对相同输入，选择结果是确定的

### 4.2 协议字符串处理机制

协议字符串是通过固定名称列表匹配处理的，而非基于规则解析：

1. **固定名称列表**：
   - 程序维护一个预定义的协议字符串列表
   - 每个字符串直接映射到特定的Protocol子类
   - 这种映射是硬编码在`ProtocolMgr::createProtocolFromConfig`或`create_protocol`方法中

2. **特殊情况**：
   - 某些协议字符串可能映射到同一个类，但传入不同参数
   - 例如："QA2"和"QA3"都映射到`MTFContrast2`类，但传入不同的标识符
   - "MTF10_50"和"MTF4_50"都映射到`ImageResolution`类，但传入不同的MTF因子

3. **测试模式例外**：
   - 以"Test"开头的协议字符串是特殊情况
   - 这些字符串通过规则解析（使用`sscanf`）提取测试ID、Series编号和可选协议号
   - 但这不是解析协议类型，而是解析测试参数

### 4.3 协议选择逻辑

当Series中存在多个相同类型的协议（如多个CENTER_SMUDGE）时：

1. **直接协议模式**：
   - 系统在选定的Series中查找与协议字符串匹配的协议
   - 当有多个匹配时，选择第一个匹配的协议或优先选择MandatoryTest=1的协议
   - 字符串ID不影响协议选择，主要用于结果标识
   - 在`TestReader::getProtocolConfig`方法中实现协议查找

2. **测试模式**：
   - 如果`optionalNumber=0`：执行所有`MandatoryTest=1`的协议
   - 如果`optionalNumber>0`：执行第`optionalNumber`个`MandatoryTest=0`的协议
   - 在`ProtocolMgr::selectTestNumber`方法中实现协议选择逻辑

### 4.4 参数获取流程

无论使用哪种模式，系统都会：

1. 从选定的协议定义中获取参数（如LSL、USL、Specs等）
   - 使用`TestReader::getProtocolConfig`方法读取参数
   - 参数包括NumberImages、NumberSpecs、LSL、USL、Specs等

2. 从IAProtocol.cfg中获取协议的ROI信息
   - 使用`ProtocolConfig`构造函数读取ROI信息
   - 信息包括ROI数量、位置、大小、形状等

3. 创建协议对象，并传入这些参数和ROI信息
   - 使用`ProtocolMgr::createProtocolFromConfig`方法创建协议对象
   - 根据协议类型创建不同的子类对象（如Means、Streak、MTFContrast2等）

### 4.5 执行流程

1. 解析命令行参数，提取各个部分（在`main`函数中）
2. 创建临时选择文件并设置环境变量（处理`-input`参数）
3. 创建ProtocolMgr对象并配置协议（调用`fill_protocol_info`方法）
4. 加载图像并执行分析（调用`update`方法）
5. 收集结果并输出到指定文件（使用`getConsolidatedResult`方法）
6. 清理资源（删除临时文件等）

## 5. 支持的协议类型

| 协议字符串 | 对象类型 | 图像支持 | 描述 |
|------------|----------|------------|--------|
| "MEANS" | `Means` | 单图像 | 分析图像均值，评估图像均匀性 |
| "SERIES_MEANS" | `SeriesMeans` | 多图像 | 分析多张图像的均值并比较 |
| "STREAK" | `Streak` | 单图像 | 检测条纹伪影，评估图像质量 |
| "RING" | `Ring` | 单图像 | 检测环形伪影，评估图像质量 |
| "CLUMP" | `Clump` | 单图像 | 分析图像中的团块 |
| "CENTER_SMUDGE" | `CenterSmudge` | 单图像 | 检测中心区域的污点伪影 |
| "LARGE_CENTER_SMUDGE" | `LargeCenterSmudge` | 单图像 | 检测大型中心污点伪影 |
| "CENTER_ARTIFACT" | `CentreArtifact` | 单图像 | 检测中心区域的伪影 |
| "CENTER_SPOT" | `CentreSpot` | 单图像 | 检测中心点伪影 |
| "BAND" | `BandArtifact` | 单图像 | 检测带状伪影 |
| "QA1" | `MTF` | 单图像 | 分析MTF（调制传递函数） |
| "QA2" | `MTFContrast2` | 单图像 | 分析MTF和对比度（QA2标准） |
| "QA3" | `MTFContrast2` | 单图像 | 分析MTF和对比度（QA3标准） |
| "GE_PERF1" | `MTFContrast2` | 单图像 | 分析MTF和对比度（GE性能测试1） |
| "GE_PERF2" | `MTFContrast2` | 单图像 | 分析MTF和对比度（GE性能测试2） |
| "LCD" | `Lcd` | 单图像 | 评估低对比度检测能力 |
| "VISIBLE_LINES" | `VisibleLine` | 单图像 | 分析可见线条（命令行模式不支持） |
| "VISIBLE_HOLES" | `VisibleHoles` | 单图像 | 分析可见孔洞（命令行模式不支持） |
| "MTF10_50" | `ImageResolution` | 单图像 | 分析图像分辨率（MTF因子为10） |
| "MTF4_50" | `ImageResolution` | 单图像 | 分析图像分辨率（MTF因子为4） |
| "P35_NOISE" | `MTFContrast2` | 单图像 | 分析P35噪声 |

## 6. Service Selection文件

### 6.1 文件概述

Service Selection文件用于指定要分析的图像。在命令行模式下，程序会根据`-input`参数自动生成这个文件，并通过环境变量`SDC_SELECTION_FILE`和`IMAGE_SELECTION_FILE`指向它。

### 6.2 文件格式

```
<第一个图像路径> <起始图像编号> <图像总数> <排序类型1> <排序类型2> <排序类型3> <排序类型4>
<图像文件路径1>
<图像文件路径2>
...
```

示例：
```
/usr/g/sdc_image_pool/images/p6/e9/s39/i302.CTDC.1 1 1 Sort_Unknown Sort_Exam_By_Date Sort_Series Sort_Image_By_Image_Number
/usr/g/sdc_image_pool/images/p6/e9/s39/i302.CTDC.1
```

## 7. 使用示例

### 7.1 基本用法（单图像）

```bash
IAUI -text -input "/usr/g/sdc_image_pool/images/p6/e9/s39/i302.CTDC.1" -output "results.log" -protocol "MEANS|AUTO|MEANS_TEST"
```

这个命令将：
1. 以命令行模式运行程序
2. 分析指定的单个CT图像
3. 使用MEANS协议进行分析
4. 将结果输出到results.log文件

### 7.2 QA3协议分析

```bash
IAUI -text -input "/usr/g/sdc_image_pool/images/p6/e9/s39/i302.CTDC.1" -output "/usr/g/bin/output2.log" -protocol "QA3|ImgSer20QA|6"
```

这个命令将：
1. 以命令行模式运行程序
2. 分析指定的CT图像
3. 使用QA3协议进行分析，作为ImgSer20QA测试的一部分
4. 将结果输出到/usr/g/bin/output2.log文件

### 7.3 多图像分析

```bash
IAUI -text -input "/usr/g/sdc_image_pool/images/p6/e9/s39/i302.CTDC.1 /usr/g/sdc_image_pool/images/p6/e9/s39/i303.CTDC.1" -output "results.log" -protocol "SERIES_MEANS|AUTO|SERIES_TEST"
```

这个命令将：
1. 以命令行模式运行程序
2. 分析指定的两张CT图像
3. 使用SERIES_MEANS协议进行分析，比较两张图像的均值
4. 将结果输出到results.log文件

### 7.4 测试模式

```bash
IAUI -text -input "/usr/g/sdc_image_pool/images/p6/e9/s39/i302.CTDC.1" -output "results.log" -protocol "Test1 1 opt 0|GrDrHot20QA|1"
```

这个命令将：
1. 以命令行模式运行程序
2. 分析指定的CT图像
3. 使用GrDrHot20QA套件的Series_1中所有MandatoryTest=1的协议
4. 将结果输出到results.log文件

## 8. 限制与错误处理

### 8.1 限制

- 不支持需要用户交互的协议（如"Visible Holes"、"Visible Line"）
- 命令行模式下无法进行交互式操作
- 无法直观查看分析结果

### 8.2 常见错误

1. **参数解析错误**：`[IA-ERROR] Failed to parse protocol string`
2. **图像加载错误**：`[IA-ERROR] No valid images found in series`
3. **协议配置错误**：`[IA-ERROR] Failed to load protocol configuration`
4. **不支持的协议**：`[IA-ERROR] Unsupported protocol: Visible Holes`

## 9. 代码实现要点

在我们的讨论中，我们分析了一些关键的代码实现要点，这里将它们整理如下：

### 9.1 SDC_SELECTION_FILE处理

1. **文件创建**：
   - 使用`mkstemp`创建临时文件，文件名格式为`/tmp/iaui_XXXXXX`
   - 写入图像文件列表，包括元数据行和图像路径行

2. **环境变量设置**：
   - 同时设置`SDC_SELECTION_FILE`和`IMAGE_SELECTION_FILE`两个环境变量
   - 两个环境变量指向同一个文件，但用于不同的组件

3. **环境变量作用**：
   - `SDC_SELECTION_FILE`：
     - 由SDC（Study Display Component）组件使用
     - 主要用于与GE医疗系统的其他组件兼容
     - 在命令行模式下不直接使用，但需要设置以保持兼容性
   - `IMAGE_SELECTION_FILE`：
     - 由`ImageSupplier::retrieveAndStoreImagePath()`方法显式使用
     - 如果未设置，会导致图像加载失败
     - 代码中的关键检查：
       ```cpp
       fname = getenv("IMAGE_SELECTION_FILE");
       if (fname == NULL) {
           printf("Environmental variable IMAGE_SELECTION_FILE is not set.\n");
           return;
       }
       ```

4. **设置代码**：
   ```cpp
   // 设置环境变量
   setenv("SDC_SELECTION_FILE", selection, 1);
   setenv("IMAGE_SELECTION_FILE", selection, 1);
   ```
   - 两个环境变量必须同时设置
   - 缺少任一个都可能导致程序异常

5. **文件格式**：
   ```
   <第一个图像路径> <起始图像编号> <图像总数> <排序类型...>
   <图像文件路径列表>
   ```
   - 第一行包含元数据，起始图像编号在命令行模式下总是1
   - 第二行包含完整的图像文件列表

### 9.2 多图像处理

1. **图像加载**：
   - `ImageSupplier::retrieveAndStoreImagePath()`方法读取所有图像路径
   - 使用`GvtkGeMedicalFileImageProvider`加载所有图像

2. **协议类型区分**：
   - 单图像协议（如MEANS, STREAK）只处理第一张图像
   - 多图像协议（如SERIES_MEANS）处理所有图像

3. **图像访问方法**：
   - `getfirstImage()`：获取第一张图像，用于单图像协议
   - `getNthImage(int imageNum)`：获取第 N 张图像，用于多图像协议
   - `getNoOfImagesInSeries()`：获取系列中的图像数量

### 9.3 协议选择逻辑

1. **Series选择**：
   - 测试模式：直接从协议字符串中提取Series编号
   - 直接协议模式：比较图像参数与Series参数的匹配度

2. **多个相同类型协议的选择**：
   - 当Series中有多个相同类型的协议（如多个CENTER_SMUDGE）时：
     - 直接协议模式：选择第一个匹配的协议或优先选择MandatoryTest=1的协议
     - 测试模式：根据optionalNumber和MandatoryTest值选择

3. **字符串ID的作用**：
   - 主要用于结果标识和报告
   - 不直接影响协议选择

## 10. 执行流程图

### 10.1 总体执行流程

```
┌─────────────────┌
│  命令行参数解析      │
│  解析-text/-input/  │
│  -output/-protocol  │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  环境准备            │
│  创建临时文件        │
│  设置环境变量        │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  协议配置            │
│  加载配置文件        │
│  创建ProtocolMgr     │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  图像加载            │
│  读取图像路径        │
│  加载图像数据        │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  协议执行            │
│  选择Series         │
│  选择Protocol       │
│  执行分析            │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  结果输出            │
│  收集分析结果        │
│  写入输出文件        │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  资源清理            │
│  删除临时文件        │
│  释放内存            │
└─────────────────┘
```

### 10.2 协议选择流程

```
┌─────────────────┌
│  协议字符串解析      │
│  提取协议类型        │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  测试模式判断        │
│  检查是否以"Test"开头 │
└─────────────────┴──────────┌
           ↓
     ┌─────┴─────┌
     ↓           ↓
┌───────┌ ┌───────┌
│ 测试模式 │ │直接模式 │
└─────┴───┌ └────┴────┌
      ↓          ↓
┌───────┌ ┌───────┌
│从字符串中│ │根据图像 │
│提取Series│ │参数匹配 │
│  编号   │ │ Series  │
└─────┴───┌ └────┴────┌
      ↓          ↓
┌─────────────────┌
│  协议选择            │
│  根据协议字符串      │
│  和MandatoryTest值   │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  协议执行            │
│  调用update()方法    │
└─────────────────┘
```

### 10.3 多图像处理流程

```
┌─────────────────┌
│  读取图像路径        │
│  从临时文件中读取    │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  加载所有图像        │
│  使用GvtkProvider    │
└─────────────────┴──────────┌
           ↓
┌─────────────────┌
│  协议类型判断        │
│  单图像或多图像      │
└─────────────────┴──────────┌
           ↓
     ┌─────┴─────┌
     ↓           ↓
┌───────┌ ┌───────┌
│单图像协议│ │多图像协议│
└─────┴───┌ └────┴────┌
      ↓          ↓
┌───────┌ ┌───────┌
│只处理第一│ │处理所有 │
│  张图像  │ │  图像   │
└───────┘ └───────┘
```

## 11. 总结

CT图像分析程序的命令行模式提供了一种无需图形界面就能执行图像分析的方法。通过命令行参数和Service Selection文件，用户可以指定要分析的图像和使用的协议，适合批处理和自动化测试场景。

程序的配置文件系统（IAProtocol.cfg和IATest.cfg）提供了详细的分析参数和标准，使得系统能够灵活地适应不同的CT扫描仪型号和测试需求。Protocol参数的三个部分（协议字符串、协议名称和字符串ID）与配置文件系统紧密结合，共同决定了分析的执行方式和参数。
