# CT图像分析程序命令行模式执行流程

## 1. 命令行模式执行流程概述

CT图像分析程序的命令行模式执行流程可以分为以下几个主要阶段：

1. **命令行参数解析**
2. **环境准备**
3. **协议配置**
4. **图像加载**
5. **协议执行**
6. **结果输出**
7. **资源清理**

## 2. 详细执行流程

```
┌─────────────────────┐
│  命令行参数解析      │
│  解析-text/-input/  │
│  -output/-protocol  │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  环境准备            │
│  创建临时文件        │
│  设置环境变量        │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  协议配置            │
│  加载配置文件        │
│  创建ProtocolMgr     │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  图像加载            │
│  读取图像路径        │
│  加载图像数据        │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  协议执行            │
│  选择Series         │
│  选择Protocol       │
│  执行分析            │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  结果输出            │
│  收集分析结果        │
│  写入输出文件        │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  资源清理            │
│  删除临时文件        │
│  释放内存            │
└─────────────────────┘
```

## 3. 各阶段详细说明

### 3.1 命令行参数解析

```cpp
// 检测命令行模式
for (loop = 0; loop < argc; loop++) {
    if (!strcmp(argv[loop], "-text")) {
        _gui = SYS_FALSE;
    }
}

// 解析其他参数
if (_gui == SYS_FALSE) {
    // 解析-output参数
    // 解析-input参数
    // 解析-protocol参数
}
```

- **输入**：命令行参数（argc, argv）
- **输出**：解析后的参数值（output, input, protocol等）
- **关键逻辑**：
  - 检测是否包含`-text`参数
  - 解析`-output`、`-input`和`-protocol`参数
  - 提取协议字符串、协议名称和字符串ID

### 3.2 环境准备

```cpp
// 创建临时选择文件
char template_name[] = "/tmp/iaui_XXXXXX";
int fd = mkstemp(template_name);
strncpy(selection, template_name, sizeof(selection) - 1);
file = fdopen(fd, "w");

// 写入选择文件
fprintf(file, "%s %d %d %s %s %s %s\n", first, 1, count, ...);
fprintf(file, "%s\n", input);

// 设置环境变量
setenv("SDC_SELECTION_FILE", selection, 1);
setenv("IMAGE_SELECTION_FILE", selection, 1);
```

- **输入**：解析后的input参数
- **输出**：临时文件路径和环境变量
- **关键逻辑**：
  - 创建临时文件
  - 写入图像文件信息
  - 设置环境变量

### 3.3 协议配置

```cpp
// 创建ProtocolMgr对象
ProtocolMgr analysis("UNKNOWN", "UNKNOWN", "UNKNOWN");

// 配置协议
char *config_result = analysis.fill_protocol_info(selectedProtocolString, selectedProtocolName, selectedStringId);
```

- **输入**：协议字符串、协议名称和字符串ID
- **输出**：配置好的ProtocolMgr对象
- **关键逻辑**：
  - 创建ProtocolMgr对象
  - 根据扫描仪型号选择配置文件
  - 加载协议配置
  - 创建Protocol对象

### 3.4 图像加载

```cpp
// 加载图像
imageSupplier.retrieveAndStoreImagePath();
image = imageSupplier.getfirstImage();
```

- **输入**：环境变量指向的临时文件
- **输出**：加载的图像数据
- **关键逻辑**：
  - 读取环境变量获取文件路径
  - 从文件中读取图像路径
  - 加载图像数据

### 3.5 协议执行

```cpp
// 执行分析
analysis.update();
```

- **输入**：Protocol对象和图像数据
- **输出**：分析结果
- **关键逻辑**：
  - 选择适当的Series
  - 选择适当的Protocol
  - 执行协议分析
  - 收集分析结果

### 3.6 结果输出

```cpp
// 收集结果
list<string> *result = analysis.getConsolidatedResult();

// 输出结果
std::ofstream fout(output);
for (resultIndex = result->begin(); resultIndex != result->end(); resultIndex++) {
    fout << resultIndex->c_str() << endl;
}
```

- **输入**：分析结果和输出文件路径
- **输出**：结果文件
- **关键逻辑**：
  - 收集分析结果
  - 打开输出文件
  - 写入结果数据

### 3.7 资源清理

```cpp
// 清理资源
unlink(selection);
```

- **输入**：临时文件路径
- **输出**：无
- **关键逻辑**：
  - 删除临时文件
  - 释放内存资源

## 4. 协议选择流程

```
┌─────────────────────┐
│  协议字符串解析      │
│  提取协议类型        │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  测试模式判断        │
│  检查是否以"Test"开头 │
└──────────┬──────────┘
           ↓
     ┌─────┴─────┐
     ↓           ↓
┌─────────┐ ┌─────────┐
│ 测试模式 │ │直接模式 │
└─────┬───┘ └────┬────┘
      ↓          ↓
┌─────────┐ ┌─────────┐
│从字符串中│ │根据图像 │
│提取Series│ │参数匹配 │
│  编号   │ │ Series  │
└─────┬───┘ └────┬────┘
      ↓          ↓
┌─────────────────────┐
│  协议选择            │
│  根据协议字符串      │
│  和MandatoryTest值   │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  协议执行            │
│  调用update()方法    │
└─────────────────────┘
```

### 4.1 测试模式与直接模式

#### 测试模式（以"Test"开头）

```cpp
if(!selectedProtocol.compare(0, 4, "Test")) {
    // 从字符串中提取测试ID、Series编号和可选协议号
    sscanf(selectedTest, "%s%s%d%s%d", temp1, &testNum, &seriesNum, temp2, &optionalNum);
    
    // 选择测试
    selectTestNumber(testNum, seriesNum, optionalNum, override, selectedProtocolName);
}
```

- **Series选择**：直接从协议字符串中提取
- **协议选择**：
  - 如果optionalNumber=0：执行所有MandatoryTest=1的协议
  - 如果optionalNumber>0：执行第optionalNumber个MandatoryTest=0的协议

#### 直接模式（普通协议字符串）

```cpp
else {
    // 创建协议
    protocol = create_protocol(selectedProtocol);
    addProtocolToList(protocol);
}
```

- **Series选择**：比较图像参数与Series参数的匹配度
- **协议选择**：选择与协议字符串匹配的协议类型

## 5. 多图像处理流程

```
┌─────────────────────┐
│  读取图像路径        │
│  从临时文件中读取    │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  加载所有图像        │
│  使用GvtkProvider    │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│  协议类型判断        │
│  单图像或多图像      │
└──────────┬──────────┘
           ↓
     ┌─────┴─────┐
     ↓           ↓
┌─────────┐ ┌─────────┐
│单图像协议│ │多图像协议│
└─────┬───┘ └────┬────┘
      ↓          ↓
┌─────────┐ ┌─────────┐
│只处理第一│ │处理所有 │
│  张图像  │ │  图像   │
└─────────┘ └─────────┘
```

### 5.1 单图像与多图像协议

#### 单图像协议（如MEANS, STREAK）

```cpp
// 获取第一张图像
image = imageSupplier.getfirstImage();

// 创建协议对象
Protocol *means = new Means(&imageNum, config, Gvtk_app_context, image);
```

- 只处理第一张图像
- 使用`getfirstImage()`方法获取图像

#### 多图像协议（如SERIES_MEANS）

```cpp
// 获取图像数量
int imageCount = imageSupplier.getNoOfImagesInSeries();

// 处理所有图像
for (int i = 0; i < imageCount; i++) {
    GvtkImage *img = imageSupplier.getNthImage(i);
    // 处理图像...
}
```

- 处理所有加载的图像
- 使用`getNthImage(int imageNum)`方法获取每张图像
