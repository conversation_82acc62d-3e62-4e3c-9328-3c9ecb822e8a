# 异常处理模块设计

## 1. 概述

异常处理模块提供了一套统一的异常处理机制，包括异常类、错误码、AOP 切面、消息处理和工具类。该模块旨在提高代码的可维护性、可读性和健壮性，同时简化异常处理流程。

## 2. 模块结构

```
com.ge.med.ct.exception/
├── aspect/                 # AOP切面和注解
│   ├── HandleException.java        # 通用异常处理注解
│   └── ExceptionHandlingAspect.java # 异常处理切面
├── code/                   # 错误码定义
│   ├── ErrorCode.java              # 错误码接口
│   └── ErrorCodes.java             # 错误码枚举
├── core/                   # 核心异常类
│   ├── QAToolException.java        # 基础异常类
│   ├── DicomException.java         # DICOM异常类
│   ├── BusinessException.java      # 业务异常类
│   ├── ConfigValidationException.java # 配置验证异常类
│   └── UIException.java            # UI异常类
├── event/                  # 异常事件
│   └── ExceptionEvent.java         # 异常事件类
├── factory/                # 异常工厂
│   └── ExceptionFactory.java       # 异常工厂类
├── message/                # 消息处理
│   ├── Message.java                # 消息接口
│   ├── IMessage.java               # 旧版消息接口（向后兼容）
│   ├── MessageImpl.java            # 消息实现类
│   ├── AbstractMessage.java        # 消息抽象基类
│   ├── MessageBuilder.java         # 消息构建器
│   ├── DicomMessageBuilder.java    # DICOM消息构建器
│   ├── CommonMessages.java         # 通用消息枚举
│   ├── DicomMessages.java          # DICOM消息枚举
│   ├── BusinessMessages.java       # 业务消息枚举
│   ├── ConfigMessages.java         # 配置消息枚举
│   └── UIMessages.java             # UI消息枚举
└── util/                   # 工具类
    ├── ExceptionStatistics.java    # 异常统计工具
    └── LambdaExceptionUtil.java    # Lambda异常处理工具类
```

## 3. 核心组件

### 3.1 异常类层次结构

```
RuntimeException
└── QAToolException
    ├── DicomException
    ├── BusinessException
    ├── ConfigValidationException
    └── UIException
```

所有自定义异常都继承自 `QAToolException`，它提供了以下功能：

- 错误码管理
- 消息处理
- 上下文信息
- 时间戳
- 构建器模式支持

### 3.2 消息系统

消息系统提供了一套统一的消息处理机制，包括：

- `Message` 接口：定义消息的基本操作
- `AbstractMessage` 类：提供消息的基本实现
- 消息枚举：提供预定义的消息
- 消息构建器：支持消息的格式化和国际化

消息接口定义：

```java
public interface Message {
    /**
     * 获取消息键
     */
    String getKey();

    /**
     * 获取默认消息
     */
    String getDefaultMessage();

    /**
     * 格式化消息
     */
    Message format(Object... args);

    /**
     * 转换为字符串
     */
    String toStr();
}
```

### 3.3 AOP 异常处理

AOP 异常处理提供了一种声明式的异常处理方式，通过注解来定义异常处理策略：

```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface HandleException {
    // 错误码
    ErrorCode errorCode() default ErrorCode.UNEXPECTED;

    // 是否记录异常
    boolean logException() default true;

    // 是否发布异常事件
    boolean publishEvent() default true;

    // 重试相关属性
    int maxRetries() default 0;
    long retryDelay() default 0;
    Class<? extends Throwable>[] retryFor() default {};
    Class<? extends Throwable>[] noRetryFor() default {};
}
```

### 3.4 异常恢复机制

异常恢复机制提供了重试功能：

- `RetryStrategy`：提供重试功能，支持配置重试次数、延迟时间和重试条件

## 4. 使用示例

### 4.1 使用异常处理注解

```java
// 类级别注解
@HandleException(errorCode = ErrorCode.PROCESSING)
public class DicomProcessor {
    // 使用类级别的默认错误码
    public void processFile(String filePath) {
        // 处理逻辑
    }

    // 覆盖类级别的错误码，使用特定的错误码
    @HandleException(
        errorCode = ErrorCode.DICOM_VALIDATION,
        maxRetries = 3,
        retryDelay = 1000
    )
    public void validateFile(String filePath) {
        // 验证逻辑
    }
}
```

### 4.2 抛出异常

```java
// 使用构建器模式
throw DicomException.builder()
    .errorCode(ErrorCode.INVALID_FILE)
    .message(DicomMessages.FILE_INVALID)
    .messageArgs(filePath)
    .context("fileName", file.getName())
    .context("fileSize", file.length())
    .build();

// 使用工厂方法
throw ExceptionFactory.createDicomException(
    ErrorCode.INVALID_FILE,
    DicomMessages.FILE_INVALID,
    filePath
);
```

### 4.3 使用消息系统

```java
// 使用预定义的消息
Message message = DicomMessages.FILE_INVALID.format(filePath);
String formattedMessage = message.toStr();

// 使用消息构建器
String message = DicomMessageBuilder.of(DicomMessages.SCAN_START)
    .param("/path/to/dir")
    .param("递归")
    .build();
```

### 4.4 使用异常恢复机制

```java
// 使用重试机制
RetryStrategy.retry(
    () -> readFile(filePath),
    "readFile",
    3,
    1000,
    e -> e instanceof IOException
);

// 使用无返回值的重试机制
RetryStrategy.retryVoid(
    () -> writeFile(filePath, content),
    "writeFile"
);
```

## 5. 最佳实践

### 5.1 异常创建与抛出

1. **使用构建器模式**：对于复杂的异常，使用构建器模式创建异常对象
2. **添加上下文信息**：使用`addContext`方法添加上下文信息，帮助诊断问题
3. **使用枚举错误码**：使用枚举错误码，而不是字符串常量
4. **使用消息接口**：使用`Message`接口定义消息，支持国际化
5. **使用工厂方法**：对于常见的异常，使用工厂方法创建异常对象

### 5.2 异常处理注解使用

1. **类级别注解的适用场景**：
   - 类中大多数方法需要相同的异常处理策略
   - 希望简化代码，避免在每个方法上重复添加相同的注解

2. **方法级别注解的适用场景**：
   - 方法需要与类不同的异常处理策略
   - 方法需要特定的错误码或重试策略

3. **明确性优先**：
   - 为了代码的可读性和维护性，建议在重要或复杂的方法上显式添加注解
   - 即使与类级别相同，这样可以使意图更加明确

### 5.3 异常恢复与重试

1. **使用重试机制**：对于可能是暂时性的错误，如网络超时，实现自动重试
2. **配置重试条件**：明确指定哪些异常应该重试，哪些不应该重试
3. **设置合理的重试间隔**：避免过于频繁的重试导致系统负载过高
4. **限制最大重试次数**：防止无限重试导致资源耗尽
