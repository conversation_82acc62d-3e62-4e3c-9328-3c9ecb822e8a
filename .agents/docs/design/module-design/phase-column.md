# CT图像相位(PHASE)列实现文档

## 1. 概述

在CT质量保证工具中，相位(PHASE)列是图像表格中的一个重要列，用于显示CT图像的相位信息。本文档详细说明相位列的实现逻辑和在CT图像分析中的应用。

## 2. 相位信息概念

### 2.1 定义

在CT成像中，相位(Phase)是指在心脏或呼吸周期中采集图像的时间点。对于心脏CT，相位通常表示为心动周期的百分比(如0%-90%)；对于呼吸门控CT，相位表示呼吸周期的阶段。

### 2.2 DICOM标签

相位信息在DICOM标准中由标签(0054,1300)表示，通常称为"Frame Reference Time"或"Phase"。该标签的值可以是数字(表示毫秒)或百分比(表示周期的百分比)。

### 2.3 临床意义

相位信息对于以下应用非常重要：
- 心脏CT：评估不同心动周期阶段的心脏结构和功能
- 呼吸门控CT：评估不同呼吸阶段的肺部结构变化
- 4D CT：分析器官随时间的运动和变形

## 3. 相位列实现

### 3.1 配置定义

在表格列配置文件(table_columns.properties)中，相位列定义如下：

```properties
# 图像表格列配置
image.columns=image,imgctr,thick,tilt,sfov,dfov,matrix,kv,alg,ph
...
image.ph.tagId=00541300
image.ph.displayName=相位
```

### 3.2 数据提取

在TableDataEngine的ImageConverter中，相位列的数据提取实现如下：

```java
private DataConverter<DicomImage> createImageConverter() {
    return (image, columnName, tableType) -> {
        if (image == null) return "";

        // 使用配置文件中定义的列名
        switch (columnName.toLowerCase()) {
            // ...其他列处理...
            case "ph":
                return image.getTagValue(PHASE);
            default:
                String tagId = getColumnTagId(tableType, columnName);
                return !tagId.isEmpty() ? convertTagValue(tagId, image.getTagValue(tagId)) : "";
        }
    };
}
```

其中，PHASE是从DicomTagConstants.CT类中导入的常量：

```java
public static final class CT {
    // ...其他常量...
    public static final String PHASE = "00541300";
}
```

### 3.3 值格式化

相位值可能需要根据不同的CT设备和扫描协议进行格式化。目前的实现直接显示原始值，未来可以添加专门的格式化逻辑：

```java
// 相位值格式化示例(未实现)
converters.put(PHASE, value -> {
    try {
        float phaseValue = Float.parseFloat(value);
        return String.format("%.1f%%", phaseValue);
    } catch (NumberFormatException e) {
        return value;
    }
});
```

## 4. 相位数据处理

### 4.1 数据类型

相位数据可能有以下几种表示形式：
- 百分比：表示周期的百分比，如"75%"
- 毫秒：表示从周期开始的时间，如"750ms"
- 角度：表示周期的角度，如"270°"

### 4.2 数据解析

根据不同的表示形式，需要相应的解析逻辑：

```java
private String parsePhaseValue(String value) {
    if (value == null || value.isEmpty()) {
        return "";
    }

    // 尝试解析为数值
    try {
        float numValue = Float.parseFloat(value);

        // 判断值的范围来确定表示形式
        if (numValue >= 0 && numValue <= 100) {
            // 百分比形式
            return String.format("%.1f%%", numValue);
        } else if (numValue >= 0 && numValue < 1000) {
            // 角度形式
            return String.format("%.1f°", numValue);
        } else {
            // 毫秒形式
            return String.format("%.0fms", numValue);
        }
    } catch (NumberFormatException e) {
        // 非数值形式，直接返回原值
        return value;
    }
}
```

### 4.3 多相位数据

对于多相位扫描，一个序列可能包含多个不同相位的图像。在这种情况下，需要特殊处理：

```java
// 获取序列中的所有相位值
public List<String> getAllPhases(DicomSeries series) {
    Set<String> phases = new HashSet<>();
    for (DicomImage image : series.getImages()) {
        String phase = image.getTagValue(PHASE);
        if (phase != null && !phase.isEmpty()) {
            phases.add(phase);
        }
    }
    return new ArrayList<>(phases);
}

// 获取特定相位的图像
public List<DicomImage> getImagesForPhase(DicomSeries series, String phase) {
    List<DicomImage> images = new ArrayList<>();
    for (DicomImage image : series.getImages()) {
        if (phase.equals(image.getTagValue(PHASE))) {
            images.add(image);
        }
    }
    return images;
}
```

## 5. 相位信息应用

### 5.1 相位筛选

在UI中，可以添加相位筛选功能，允许用户选择特定相位的图像进行查看：

```java
// 相位筛选实现示例
public void applyPhaseFilter(String selectedPhase) {
    if (selectedPhase == null || selectedPhase.isEmpty()) {
        // 显示所有图像
        displayAllImages();
    } else {
        // 显示选定相位的图像
        List<DicomImage> filteredImages = getImagesForPhase(currentSeries, selectedPhase);
        displayImages(filteredImages);
    }
}
```

### 5.2 相位排序

可以根据相位值对图像进行排序，便于分析相位变化：

```java
// 相位排序实现示例
public List<DicomImage> sortImagesByPhase(List<DicomImage> images) {
    return images.stream()
        .sorted((img1, img2) -> {
            String phase1 = img1.getTagValue(PHASE);
            String phase2 = img2.getTagValue(PHASE);

            try {
                float p1 = Float.parseFloat(phase1);
                float p2 = Float.parseFloat(phase2);
                return Float.compare(p1, p2);
            } catch (NumberFormatException e) {
                return phase1.compareTo(phase2);
            }
        })
        .collect(Collectors.toList());
}
```

### 5.3 相位分析

可以添加相位分析功能，分析不同相位图像之间的差异：

```java
// 相位分析实现示例
public PhaseAnalysisResult analyzePhases(DicomSeries series) {
    List<String> phases = getAllPhases(series);
    Map<String, List<DicomImage>> phaseGroups = new HashMap<>();

    // 按相位分组
    for (String phase : phases) {
        phaseGroups.put(phase, getImagesForPhase(series, phase));
    }

    // 分析各相位组的特征
    PhaseAnalysisResult result = new PhaseAnalysisResult();
    for (Map.Entry<String, List<DicomImage>> entry : phaseGroups.entrySet()) {
        String phase = entry.getKey();
        List<DicomImage> images = entry.getValue();

        // 计算该相位组的统计特征
        PhaseStatistics stats = calculatePhaseStatistics(images);
        result.addPhaseStatistics(phase, stats);
    }

    return result;
}
```

## 6. 未来扩展

### 6.1 相位值标准化

为了处理不同设备和协议的相位表示差异，可以实现相位值标准化：

```java
// 相位值标准化示例
public String standardizePhaseValue(String value, String manufacturer) {
    if (value == null || value.isEmpty()) {
        return "";
    }

    // 根据制造商和值的特征进行标准化
    switch (manufacturer.toUpperCase()) {
        case "GE":
            // GE设备的相位值处理
            return standardizeGEPhase(value);
        case "SIEMENS":
            // 西门子设备的相位值处理
            return standardizeSiemensPhase(value);
        case "PHILIPS":
            // 飞利浦设备的相位值处理
            return standardizePhilipsPhase(value);
        default:
            // 默认处理
            return value;
    }
}
```

### 6.2 相位可视化

可以添加相位可视化功能，直观展示不同相位的图像差异：

```java
// 相位可视化示例
public void visualizePhases(DicomSeries series) {
    List<String> phases = getAllPhases(series);

    // 创建相位可视化面板
    JPanel phasePanel = new JPanel(new GridLayout(1, phases.size()));

    // 为每个相位创建缩略图
    for (String phase : phases) {
        List<DicomImage> images = getImagesForPhase(series, phase);
        if (!images.isEmpty()) {
            // 选择该相位的代表图像
            DicomImage representativeImage = images.get(images.size() / 2);

            // 创建图像缩略图
            ImageIcon thumbnail = createThumbnail(representativeImage);

            // 创建带标签的面板
            JPanel imagePanel = new JPanel(new BorderLayout());
            imagePanel.add(new JLabel(thumbnail), BorderLayout.CENTER);
            imagePanel.add(new JLabel("Phase: " + phase, JLabel.CENTER), BorderLayout.SOUTH);

            phasePanel.add(imagePanel);
        }
    }

    // 显示相位可视化面板
    JFrame frame = new JFrame("Phase Visualization");
    frame.setContentPane(phasePanel);
    frame.pack();
    frame.setVisible(true);
}
```

## 7. 总结

相位(PHASE)列是CT图像表格中的重要组成部分，提供了心脏或呼吸周期中图像采集时间点的信息。通过合理的配置和实现，可以有效地展示和分析相位信息，为CT图像质量保证提供重要支持。

未来可以进一步扩展相位信息的处理和应用，如相位值标准化、相位筛选、相位排序和相位可视化等功能，提升CT图像分析的能力和效率。
