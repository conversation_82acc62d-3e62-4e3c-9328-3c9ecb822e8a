# 表格数据模块设计文档

## 1. 概述

表格数据模块是CT质量保证工具的核心组件之一，负责将DICOM数据转换为表格可显示的数据格式，并提供灵活的列配置和数据格式化功能。本文档提供表格数据模块的整体设计，并引导读者了解各子组件的详细设计。

## 2. 模块目标

表格数据模块的主要目标包括：

- 提供统一的DICOM数据到表格数据的转换接口
- 支持灵活的表格列配置，便于UI定制
- 实现数据值的格式化和转换
- 支持特殊列的处理，如相位列
- 确保线程安全和高性能

## 3. 模块架构

### 3.1 核心组件

表格数据模块包含以下核心组件：

1. **表格数据引擎(TableDataEngine)**: 负责数据转换和处理的核心引擎
2. **表格配置(TableConfig)**: 负责加载和管理表格列配置
3. **标签值转换器(TagValueConverter)**: 负责格式化DICOM标签值
4. **数据转换器(DataConverter)**: 负责从DICOM对象中提取数据

### 3.2 组件关系图

```
+------------------+       +------------------+
| TableDataEngine  |------>| TableConfig      |
+------------------+       +------------------+
| -instance        |       | -columnConfigs   |
| -tableConfig     |       | -tagIdMappings   |
| -tagConverters   |       | -displayNames    |
| -dataConverters  |       +------------------+
+------------------+
| +getInstance()   |<----->+------------------+
| +convertExamData()|      | TagValueConverter|
| +convertSeriesData()|    +------------------+
| +convertImageData() |    | +convert()       |
| +getTableColumnNames()|  +------------------+
+------------------+
                           +------------------+
                           | DataConverter    |
                           +------------------+
                           | +getTagValue()   |
                           +------------------+
```

### 3.3 数据流

表格数据模块的主要数据流如下：

```
DICOM数据 --> DataConverter --> 格式化(TagValueConverter) --> 表格数据(Vector<Vector<String>>)
```

## 4. 主要功能

### 4.1 数据转换

表格数据模块支持三种类型的DICOM数据转换：

1. **检查数据转换**: 将DicomExam对象转换为表格数据
2. **序列数据转换**: 将DicomSeries对象转换为表格数据
3. **图像数据转换**: 将DicomImage对象转换为表格数据

每种转换都通过专用的DataConverter实现，根据配置的列名从DICOM对象中提取数据。

### 4.2 列配置

表格数据模块支持灵活的列配置，包括：

1. **列名定义**: 定义表格的列名和顺序
2. **标签映射**: 建立列名与DICOM标签的映射关系
3. **列显示名**: 定义列的显示名称

配置通过table_columns.properties文件进行管理，支持灵活的配置和扩展。

### 4.3 值格式化

表格数据模块支持多种值格式化功能：

1. **日期格式化**: 将YYYYMMDD格式转换为"MMM DD YY"格式
2. **浮点数格式化**: 根据不同类型的数值使用不同精度的格式化
3. **位置信息处理**: 将三维坐标转换为人类可读的格式

### 4.4 特殊列处理

表格数据模块支持特殊列的处理，如相位(PHASE)列：

1. **相位值提取**: 从DICOM对象中提取相位信息
2. **相位值格式化**: 根据相位值的特点进行格式化
3. **相位数据应用**: 支持相位筛选、排序和分析等功能

## 5. 实现细节

### 5.1 表格数据引擎

表格数据引擎(TableDataEngine)是模块的核心组件，负责数据转换和处理。它采用单例模式设计，确保全局唯一实例。

详细设计参见：[表格数据引擎设计](table-data-engine.md)

### 5.2 表格配置

表格配置(TableConfig)负责加载和管理表格列配置，支持从配置文件加载列定义。它提供列名、标签ID和显示名的获取接口。

详细设计参见：[表格配置设计](table-config.md)

### 5.3 相位列实现

相位列是一个特殊的列，用于显示CT图像的相位信息。它需要特殊的处理逻辑，包括相位值提取、格式化和应用。

详细设计参见：[相位列实现设计](phase-column.md)

## 6. 接口定义

### 6.1 TableDataEngine接口

```java
public class TableDataEngine {
    /**
     * 获取TableDataEngine实例
     * @return TableDataEngine实例
     */
    public static TableDataEngine getInstance() throws QAToolException;

    /**
     * 转换检查数据为表格数据
     * @param exams 检查列表
     * @return 表格数据
     */
    public Vector<Vector<String>> convertExamData(List<DicomExam> exams);

    /**
     * 转换序列数据为表格数据
     * @param seriesList 序列列表
     * @return 表格数据
     */
    public Vector<Vector<String>> convertSeriesData(List<DicomSeries> seriesList);

    /**
     * 转换图像数据为表格数据
     * @param images 图像列表
     * @return 表格数据
     */
    public Vector<Vector<String>> convertImageData(List<DicomImage> images);

    /**
     * 获取表格列名
     * @param tableType 表格类型
     * @return 列名数组
     */
    public String[] getTableColumnNames(String tableType);
}
```

### 6.2 TableConfig接口

```java
public class TableConfig {
    /**
     * 获取列名列表
     * @param tableType 表格类型
     * @return 列名列表
     */
    public List<String> getColumnNames(String tableType);

    /**
     * 获取列对应的标签ID
     * @param tableType 表格类型
     * @param columnName 列名
     * @return 标签ID
     */
    public String getColumnTagId(String tableType, String columnName);

    /**
     * 获取列显示名
     * @param tableType 表格类型
     * @param columnName 列名
     * @return 显示名
     */
    public String getColumnDisplayName(String tableType, String columnName);
}
```

## 7. 使用示例

### 7.1 获取表格数据

```java
// 获取表格数据引擎实例
TableDataEngine engine = TableDataEngine.getInstance();

// 获取检查表格数据
List<DicomExam> exams = dicomService.getExams();
Vector<Vector<String>> examData = engine.convertExamData(exams);

// 获取图像表格数据
List<DicomImage> images = dicomService.getImages(seriesId);
Vector<Vector<String>> imageData = engine.convertImageData(images);

// 获取表格列名
String[] columns = engine.getTableColumnNames(TableConfig.TYPE_IMAGE);
```

### 7.2 创建表格

```java
// 获取表格数据和列名
Vector<Vector<String>> data = engine.convertImageData(images);
String[] columns = engine.getTableColumnNames(TableConfig.TYPE_IMAGE);

// 创建表格模型
DefaultTableModel model = new DefaultTableModel(data, columns);

// 创建表格
JTable table = new JTable(model);

// 添加到滚动面板
JScrollPane scrollPane = new JScrollPane(table);
panel.add(scrollPane, BorderLayout.CENTER);
```

## 8. 配置示例

表格列配置示例(table_columns.properties):

```properties
# 表格类型常量
table.type.exam=exam
table.type.series=series
table.type.image=image

# 检查表格列配置
exam.columns=PatientID,Name,Date,StudyID,Modality,Description,StationName
exam.PatientID.tagId=00100020
exam.PatientID.displayName=患者ID
exam.Name.tagId=00100010
exam.Name.displayName=姓名
exam.Date.tagId=00080020
exam.Date.displayName=日期
exam.StudyID.tagId=00200010
exam.StudyID.displayName=检查ID
exam.Modality.tagId=00080060
exam.Modality.displayName=模态
exam.Description.tagId=00081030
exam.Description.displayName=描述
exam.StationName.tagId=00081010
exam.StationName.displayName=设备名称

# 图像表格列配置
image.columns=image,imgctr,thick,tilt,sfov,dfov,matrix,kv,alg,ph
image.image.tagId=00200013
image.image.displayName=图像
image.imgctr.displayName=位置
image.thick.tagId=00180050
image.thick.displayName=层厚
image.tilt.tagId=00181120
image.tilt.displayName=倾角
image.sfov.tagId=00181100
image.sfov.displayName=扫描视野
image.dfov.tagId=00181100
image.dfov.displayName=重建视野
image.matrix.tagId=00280010
image.matrix.displayName=矩阵
image.kv.tagId=00180060
image.kv.displayName=电压
image.alg.tagId=00181210
image.alg.displayName=算法
image.ph.tagId=00541300
image.ph.displayName=相位
```

## 9. 扩展点

### 9.1 添加新的表格类型

1. 在table_columns.properties中添加新表格类型的配置
2. 在TableConfig中添加新表格类型的常量
3. 在TableDataEngine中添加新的DataConverter和转换方法

### 9.2 添加新的值格式化器

1. 在initTagConverters方法中添加新的TagValueConverter
2. 实现相应的格式化逻辑

### 9.3 添加新的特殊列处理

1. 在DataConverter中添加新列的处理逻辑
2. 如需特殊处理，添加专门的处理方法

## 10. 相关文档

- [表格数据引擎设计](table-data-engine.md)
- [表格配置设计](table-config.md)
- [相位列实现设计](phase-column.md)
- [系统架构设计](../architecture/system-architecture.md)

## 11. 总结

表格数据模块是CT质量保证工具的核心组件之一，负责将DICOM数据转换为表格可显示的数据格式。通过灵活的配置机制和数据转换器设计，实现了表格数据的灵活配置和高效处理。

模块采用了单例模式、策略模式和工厂模式的组合设计，实现了DICOM数据到表格数据的灵活转换。通过配置驱动的方式，支持表格列的自定义配置，便于UI定制。同时，通过标签值转换器实现了数据的格式化，提高了数据的可读性。

整个设计遵循了单一职责原则和开闭原则，便于扩展和维护。通过AOP方式进行异常处理，提高了代码的健壮性和可维护性。
