# 表格数据引擎设计文档

## 1. 概述

表格数据引擎(TableDataEngine)是 CT 质量保证工具中的核心组件之一，负责将 DICOM 数据转换为表格可显示的数据格式。该引擎采用单例模式设计，支持三种类型的 DICOM 数据处理：检查(Exam)、序列(Series)和图像(Image)。

## 2. 设计目标

- 提供统一的 DICOM 数据到表格数据的转换接口
- 支持灵活的列配置，便于 UI 定制
- 实现数据值的格式化和转换
- 确保线程安全和高性能

## 3. 架构设计

### 3.1 核心组件

1. **TableDataEngine**: 表格数据引擎主类，提供数据转换服务
2. **TableConfig**: 表格配置类，负责加载和管理表格列配置
3. **TagValueConverter**: 标签值转换器接口，用于格式化 DICOM 标签值
4. **DataConverter**: 数据转换器接口，用于从 DICOM 对象中提取数据

### 3.2 类图

```
+------------------+       +------------------+
| TableDataEngine  |------>| TableConfig      |
+------------------+       +------------------+
| -instance        |       | +getColumnNames()|
| -tableConfig     |       | +getColumnTagId()|
| -tagConverters   |       +------------------+
| -dataConverters  |
+------------------+       +------------------+
| +getInstance()   |<----->| TagValueConverter|
| +convertExamData()|      +------------------+
| +convertSeriesData()|    | +convert()       |
| +convertImageData() |    +------------------+
| +getTableColumnNames()|
+------------------+       +------------------+
                           | DataConverter    |
                           +------------------+
                           | +getTagValue()   |
                           +------------------+
```

### 3.3 数据流

```
DICOM数据 --> DataConverter --> 格式化(TagValueConverter) --> 表格数据(Vector<Vector<String>>)
```

## 4. 详细设计

### 4.1 初始化流程

1. 通过单例模式获取 TableDataEngine 实例
2. 从 ConfigReader 获取 TableConfig 配置
3. 初始化标签值转换器(tagConverters)
4. 初始化数据转换器(dataConverters)

```java
private TableDataEngine() throws QAToolException {
    this.tableConfig = ConfigReader.getInstance().getTableConfig();
    this.tagConverters = initTagConverters();
    this.dataConverters = initDataConverters();
}
```

### 4.2 数据转换流程

1. 根据数据类型调用对应的 convertXXXData 方法
2. 内部统一调用 convertData 方法处理数据转换
3. 获取表格列名
4. 使用对应的 DataConverter 提取每列数据
5. 返回 Vector<Vector<String>>格式的表格数据

```java
@HandleException(errorCode = ErrorCode.SYS_DATA_ERROR)
public <T> Vector<Vector<String>> convertData(List<T> dataList, String tableType) {
    if (dataList == null || dataList.isEmpty()) {
        return new Vector<>();
    }

    Vector<Vector<String>> data = new Vector<>();
    String[] columns = getTableColumnNames(tableType);
    DataConverter<T> converter = (DataConverter<T>) dataConverters.get(dataList.get(0).getClass());

    if (converter != null) {
        for (T item : dataList) {
            Vector<String> row = new Vector<>();
            for (String column : columns) {
                row.add(converter.getTagValue(item, column, tableType));
            }
            data.add(row);
        }
    }

    return data;
}
```

### 4.3 列配置设计

表格列配置通过 TableConfig 类管理，支持从配置文件(table_columns.properties)加载列定义。每种表格类型(检查/序列/图像)有各自的列配置。

列配置包括：

- 列名：显示在表格头部的名称
- 列标识：用于在 DataConverter 中识别列
- 标签 ID：对应 DICOM 标签的 ID，用于从 DICOM 对象中获取数据

### 4.4 数据转换器设计

数据转换器(DataConverter)采用策略模式设计，为不同类型的 DICOM 数据提供专用的转换逻辑：

1. **ExamConverter**: 处理检查数据，提取患者和检查相关信息
2. **SeriesConverter**: 处理序列数据，提取序列相关信息
3. **ImageConverter**: 处理图像数据，提取图像相关信息

每个转换器通过 getTagValue 方法，根据列名从 DICOM 对象中提取对应的数据值。

### 4.5 值格式化设计

标签值转换器(TagValueConverter)用于格式化 DICOM 标签值，主要包括：

1. **日期转换器**: 将 YYYYMMDD 格式转换为"MMM DD YY"格式
2. **浮点数格式化**: 根据不同类型的数值使用不同精度的格式化
3. **位置信息处理**: 将三维坐标转换为人类可读的格式

## 5. 关键实现

### 5.1 单例模式实现

```java
public static TableDataEngine getInstance() throws QAToolException {
    TableDataEngine result = instance;
    if (result == null) {
        synchronized (TableDataEngine.class) {
            result = instance;
            if (result == null) {
                instance = result = new TableDataEngine();
            }
        }
    }
    return result;
}
```

### 5.2 图像数据转换器实现

```java
private DataConverter<DicomImage> createImageConverter() {
    return (image, columnName, tableType) -> {
        if (image == null) return "";

        // 使用配置文件中定义的列名
        switch (columnName.toLowerCase()) {
            case "image":
                return image.getTagValue(INSTANCE_NUMBER);
            case "imgctr":
                return processImagePosition(image);
            case "thick":
                return convertTagValue(SLICE_THICKNESS, image.getTagValue(SLICE_THICKNESS));
            case "tilt":
                return convertTagValue(GANTRY_TILT, image.getTagValue(GANTRY_TILT));
            case "sfov":
                return convertTagValue(FIELD_OF_VIEW, image.getTagValue(FIELD_OF_VIEW));
            case "dfov":
                return convertTagValue(RECONSTRUCTION_DIAMETER, image.getTagValue(RECONSTRUCTION_DIAMETER));
            case "matrix":
                return image.getTagValue(ROWS);
            case "kv":
                return image.getTagValue(KVP);
            case "alg":
                return image.getTagValue(CONVOLUTION_KERNEL);
            case "ph":
                return image.getTagValue(PHASE);
            default:
                String tagId = getColumnTagId(tableType, columnName);
                return !tagId.isEmpty() ? convertTagValue(tagId, image.getTagValue(tagId)) : "";
        }
    };
}
```

### 5.3 位置信息处理实现

```java
private String processImagePosition(DicomImage image) {
    String position = image.getTagValue(IMAGE_POSITION_PATIENT);
    if (position == null || position.isEmpty()) {
        return "";
    }

    try {
        String[] coords = position.split("\\\\");
        if (coords.length >= 3) {
            double x = Double.parseDouble(coords[0]);
            double y = Double.parseDouble(coords[1]);
            double z = Double.parseDouble(coords[2]);
            return String.format("I/S %.2f R/L %.1f A/P %.1f", z, x, y);
        }
    } catch (Exception e) {
        LOG.log(Level.WARNING, DicomMessages.PROCESSING_ERROR.format(position, e.getMessage()), e);
    }
    return "";
}
```

## 6. 异常处理

表格数据引擎使用 AOP 方式进行异常处理，所有主要的数据转换方法都使用@HandleException 注解，统一处理为 ErrorCode.SYS_DATA_ERROR 类型的异常。

```java
@HandleException(errorCode = ErrorCode.SYS_DATA_ERROR)
public Vector<Vector<String>> convertImageData(List<DicomImage> images) {
    return convertData(images, TableConfig.TYPE_IMAGE);
}
```

## 7. 配置示例

表格列配置示例(table_columns.properties):

```properties
# 检查表格列配置
exam.columns=PatientID,Name,Date,StudyID,Modality,Description,StationName
exam.PatientID.tagId=00100020
exam.Name.tagId=00100010
exam.Date.tagId=00080020
exam.StudyID.tagId=00200010
exam.Modality.tagId=00080060
exam.Description.tagId=00081030
exam.StationName.tagId=00081010

# 图像表格列配置
image.columns=image,imgctr,thick,tilt,sfov,dfov,matrix,kv,alg,ph
image.image.tagId=00200013
image.thick.tagId=00180050
image.tilt.tagId=00181120
image.sfov.tagId=00181100
image.dfov.tagId=00181100
image.matrix.tagId=00280010
image.kv.tagId=00180060
image.alg.tagId=00181210
image.ph.tagId=00541300
```

## 8. 表格数据加载逻辑分析

### 8.1 整体架构

`TableDataEngine`是一个单例类，负责处理 DICOM 数据到表格数据的转换和展示。它主要处理三种类型的 DICOM 数据：

1. **检查数据(DicomExam)**：患者级别的检查信息
2. **序列数据(DicomSeries)**：检查中的序列信息
3. **图像数据(DicomImage)**：序列中的具体图像信息

### 8.2 核心组件

1. **TableConfig**：表格配置类，负责从配置文件加载表格列配置
2. **TagValueConverter**：标签值转换器接口，用于格式化 DICOM 标签值
3. **DataConverter**：数据转换器接口，用于从 DICOM 对象中提取数据

### 8.3 数据加载流程

1. 初始化阶段：

   - 从 ConfigReader 获取 TableConfig 配置
   - 初始化标签值转换器(tagConverters)
   - 初始化数据转换器(dataConverters)

2. 数据转换过程：
   - 根据数据类型(检查/序列/图像)调用对应的 convertXXXData 方法
   - 内部统一调用 convertData 方法处理数据转换
   - 获取表格列名(getTableColumnNames)
   - 使用对应的 DataConverter 提取每列数据
   - 返回 Vector<Vector<String>>格式的表格数据

### 8.4 列配置逻辑

1. **列名定义**：

   - 通过 TableConfig.getColumnNames 获取特定表格类型的列名
   - 列名在 table_columns.properties 配置文件中定义

2. **列值获取**：

   - 对于常用列，在 DataConverter 中通过 switch 语句直接处理
   - 对于自定义列，通过 getColumnTagId 获取对应的 DICOM 标签 ID
   - 使用 DicomTagConstants 中定义的常量作为标签 ID

3. **值格式化**：
   - 对特定标签(如日期、浮点数)使用 TagValueConverter 进行格式化
   - 对于位置信息(imgctr)使用特殊的 processImagePosition 方法处理

### 8.5 PHASE 列处理

从代码中可以看到，PHASE 列是在图像数据转换器中特别处理的：

```java
case "ph":
    return image.getTagValue(PHASE);
```

这里的 PHASE 是从 DicomTagConstants.CT 类中导入的常量，表示 CT 图像的相位信息。当表格中需要显示"ph"列时，会从 DicomImage 对象中获取 PHASE 标签的值。

### 8.6 异常处理

- 所有主要的数据转换方法都使用@HandleException 注解进行异常处理
- 异常处理使用 AOP 方式，统一处理为 ErrorCode.SYS_DATA_ERROR 类型的异常
- 日志记录使用标准的 Java 日志系统

### 8.7 数据转换特点

1. **日期转换**：将 YYYYMMDD 格式转换为"MMM DD YY"格式(如"Jan 01 23")
2. **浮点数格式化**：根据不同类型的数值使用不同精度的格式化
3. **位置信息处理**：将三维坐标转换为人类可读的格式(I/S, R/L, A/P)
4. **空值处理**：所有空值或无效值都返回空字符串

## 9. 总结

表格数据引擎采用了单例模式、策略模式和工厂模式的组合设计，实现了 DICOM 数据到表格数据的灵活转换。通过配置驱动的方式，支持表格列的自定义配置，便于 UI 定制。同时，通过标签值转换器实现了数据的格式化，提高了数据的可读性。

整个设计遵循了单一职责原则和开闭原则，便于扩展和维护。通过 AOP 方式进行异常处理，提高了代码的健壮性和可维护性。
